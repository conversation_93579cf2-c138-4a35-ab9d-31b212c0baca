package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.ApplicationId.THREADFORM_APP_ID
import com.moregames.games.progress.Method.ADD
import com.moregames.games.progress.Method.REPLACE
import com.moregames.games.progress.calculation.ProdGameCoinsCalculationService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import kotlin.math.max
import kotlin.math.roundToInt
import kotlin.test.assertFailsWith

class ProdGameCoinsCalculationServiceTest {

  private val service = ProdGameCoinsCalculationService()

  private companion object {
    const val USER_ID = "userId"
    val event = UserGameProgressEvent(
      userId = USER_ID,
      applicationId = "applicationId",
      platform = ANDROID,
      score = null,
      amount = null,
      parameters = mapOf()
    )
  }

  @ParameterizedTest
  @CsvSource(
    "1,10",
    "4,100",
    "8,10",
  )
  fun `SHOULD add ON calculateCoins for colorlogic`(amount: Int, expectedCoins: Int) {
    val event = event.copy(
      applicationId = ApplicationId.COLOR_LOGIC_APP_ID,
      amount = amount,
    )
    val result = runBlocking { service.calculateCoins(event) }
    assertThat(result.coins).isEqualTo(expectedCoins)
  }


  @ParameterizedTest
  @CsvSource(
    "1,10",
    "2,30",
    "4,100",
    "8,10"
  )
  fun `SHOULD add ON calculateCoins for dice logic`(amount: Int, expectedCoins: Int) {
    val event = event.copy(
      applicationId = ApplicationId.DICE_LOGIC_APP_ID,
      amount = amount,
    )
    val result = runBlocking { service.calculateCoins(event) }
    assertThat(result.coins).isEqualTo(expectedCoins)
  }


  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with amount x 45 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.emojibounce",
      "com.relaxingbraintraining.idleemojis",
      "com.gimica.emojiclickers"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(amount * 45, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 50 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.blockshooter",
      "com.relaxingbraintraining.pixelpaint",
      "com.relaxingbraintraining.pixelcolor"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 50, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 20 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.mergecandy",
      "com.relaxingbraintraining.cookiejellymatch",
      "com.cocomagic.sweetjam",
      "com.gimica.sugarmatch"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 20, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 3 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.helix", "com.gimica.helixdash"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 3, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 5 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.unblockbar",
      "com.relaxingbraintraining.pipeout",
      "com.relaxingbraintraining.zombiechallenge",
      "com.relaxingbraintraining.onelineadvanced",
      "com.relaxingbraintraining.rollthatball",
      "com.relaxingbraintraining.oneline",
      "com.gimica.oneline",
      "com.gimica.slideandroll",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 5, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 10 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.juicesplash",
      "com.relaxingbraintraining.grindmygears",
      "com.relaxingbraintraining.raccoonbubbles",
      "com.gimica.bubblepop",
      "com.gimica.crystalcrush"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 10, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("5,0", "10,2", "100,20")
  fun `SHOULD replace with score x 10 ON calculateCoins WHEN score above 0`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.popslice",
      "com.gimica.slicepuzzle",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 10, REPLACE))
      }
    }
  }

  @Test
  fun `SHOULD add 0 ON calculateCoins WHEN score is zero`() {
    listOf(
      "com.relaxingbraintraining.popslice",
      "com.gimica.slicepuzzle",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = 0))

        assertThat(actual).isEqualTo(UserBalanceUpdate(0, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add score+amount ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.colorjump"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score + amount, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with (score+amount)x5 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.numbermerge",
      "com.gimica.mergeblast"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((score + amount) * 5, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add score x 4 ON calculateCoins WHEN is_boss is NOT passed`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.knives",
      "com.adp.treasurequest",
      "com.cocomagic.daggers",
      "com.gimica.treasuremaster"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 4, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add score x 6 ON calculateCoins WHEN is_boss is passed`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.knives",
      "com.adp.treasurequest",
      "com.cocomagic.daggers",
      "com.gimica.treasuremaster"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount, parameters = mapOf("is_boss" to "true")))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 6, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD calculate balance correctly WHEN com_gimica_treasuremaster_webgl and is_boss is not passed`(score: Int, amount: Int) {
    listOf(
      "com.gimica.treasuremaster.webgl.demo",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(coins = score * 4, method = ADD, isDemoGame = true))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD calculate balance correctly WHEN com_gimica_treasuremaster_webgl and is_boss is passed`(score: Int, amount: Int) {
    listOf(
      "com.gimica.treasuremaster.webgl.demo",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount, parameters = mapOf("is_boss" to "true")))

        assertThat(actual).isEqualTo(UserBalanceUpdate(coins = score * 6, method = ADD, isDemoGame = true))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,1", "10,2", "100,20")
  fun `SHOULD add 8 ON calculateCoins WHEN amount is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.snakeclash"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(8, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "2,0", "5,0", "23,0", "100,0")
  fun `SHOULD max(1, score div 5) ON calculateCoins WHEN amount is zero`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.snakeclash"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(max(1.0, (score / 5.0)).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 1,1 ON calculateCoins WHEN is_new_record passed`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.logicblocks", "com.relaxingbraintraining.colorpuzzle", "com.addictingpuzzlegames.zenpuzzle",
      "com.cocomagic.blockpuzzle"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(
          event.copy(
            applicationId = applicationId, score = score, amount = amount,
            parameters = mapOf("is_new_record" to "true")
          )
        )

        assertThat(actual).isEqualTo(UserBalanceUpdate((score * 1.1).roundToInt(), REPLACE))
      }
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 5, 0, 10, 100])
  fun `SHOULD replace with score x 1,1 ON calculateCoins WHEN is_new_record passed AND amount is not expected`(score: Int) {
    listOf(
      "com.gimica.colorlogic", "com.gimica.maketen"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(
          event.copy(
            applicationId = applicationId, score = score, amount = null,
            parameters = mapOf("is_new_record" to "true")
          )
        )

        assertThat(actual).isEqualTo(UserBalanceUpdate((score * 1.1).roundToInt(), REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add nothing ON calculateCoins WHEN is_new_record passed is NOT passed`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.logicblocks", "com.relaxingbraintraining.colorpuzzle",
      "com.addictingpuzzlegames.zenpuzzle",
      "com.cocomagic.blockpuzzle", "com.gimica.puzzlepopblaster"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(0, ADD))
      }
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 5, 0, 10, 100])
  fun `SHOULD add nothing ON calculateCoins WHEN is_new_record passed is NOT passed AND amount is not expected`(score: Int) {
    listOf(
      "com.gimica.colorlogic", "com.gimica.maketen"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = null))

        assertThat(actual).isEqualTo(UserBalanceUpdate(0, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,,1", "1,0,1", "0,1,10", "10,0,11", "100,,110", "0,2,20", "100,3,50", "50,4,100", "12,100,0", "10, 75,0")
  fun `SHOULD replace or add based on amount value WHEN application is zenpuzzle`(score: Int, amount: Int?, result: Int) {
    listOf(
      "com.gimica.zentiles", "com.relaxingbraintraining.zenpuzzle", "com.gimica.blockbuster"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(
          event.copy(
            applicationId = applicationId, score = score, amount = amount,
            parameters = mapOf("is_new_record" to "true")
          )
        )
        when (amount) {
          amount -> assertThat(actual).isEqualTo(
            UserBalanceUpdate(
              result, if (amount == null || amount == 0) {
                REPLACE
              } else {
                ADD
              }
            )
          )
        }
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,,0", "0,0,0", "0,1,10", "10,0,0", "100,,0", "0,2,20", "100,3,50", "50,4,100", "12,100,0", "10, 75,0")
  fun `SHOULD add score WHEN application is zenpuzzle or zentiles AND is_new_record is not set`(score: Int, amount: Int?, result: Int) {
    listOf(
      "com.gimica.zentiles", "com.relaxingbraintraining.zenpuzzle", "com.gimica.blockbuster"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))
        when (amount) {
          amount -> assertThat(actual).isEqualTo(UserBalanceUpdate(result, ADD))
        }
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,1", "10,2", "100,20")
  fun `SHOULD add 2 ON calculateCoins WHEN amount is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.mousekeeper",
      "com.relaxingbraintraining.planes",
      "com.gimica.dontpop",
      "com.gimica.aeroescape",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(2, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,0", "5,0", "10,0", "100,0")
  fun `SHOULD add 3 ON calculateCoins WHEN amount is zero and score is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.mousekeeper",
      "com.gimica.dontpop",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(3, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,0", "5,0", "10,0", "100,0")
  fun `SHOULD add (score div 2) + 1 ON calculateCoins WHEN amount is zero and score is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.planes",
      "com.gimica.aeroescape",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((1.0 + score / 2.0).roundToInt(), ADD))
      }
    }
  }

  @Test
  fun `SHOULD add 1 ON calculateCoins WHEN amount is 101`() {
    listOf(
      "com.gimica.blockholeclash"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = 0, amount = 101))

        assertThat(actual).isEqualTo(UserBalanceUpdate(1, ADD))
      }
    }
  }

  @Test
  fun `SHOULD add zero ON calculateCoins WHEN amount is zero and score is zero`() {
    listOf(
      "com.relaxingbraintraining.mousekeeper",
      "com.relaxingbraintraining.planes",
      "com.relaxingbraintraining.six",
      "com.relaxingbraintraining.dunk",
      "com.relaxingbraintraining.blocks",
      "com.relaxingbraintraining.triviamillion",
      "com.gimica.triviamadness",
      "com.gimica.hexadrop",
      "com.gimica.dontpop",
      "com.gimica.aeroescape",
      "com.gimica.slamdunk",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = 0, amount = 0))

        assertThat(actual).isEqualTo(UserBalanceUpdate(0, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,1", "10,2", "100,20")
  fun `SHOULD add 1 ON calculateCoins WHEN amount is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.six",
      "com.relaxingbraintraining.dunk",
      "com.gimica.hexadrop",
      "com.gimica.slamdunk",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(1, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,0", "5,0", "10,0", "100,0")
  fun `SHOULD add (score div 5) + 1 ON calculateCoins WHEN amount is zero and score is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.six",
      "com.relaxingbraintraining.dunk",
      "com.gimica.hexadrop",
      "com.gimica.slamdunk",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((1.0 + score / 5.0).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,10", "10,20", "100,100")
  fun `SHOULD add 1 ON calculateCoins WHEN amount is positive and amount mod 10 is 0`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.blocks"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(1, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,0", "5,0", "10,0", "100,0")
  fun `SHOULD add (score div 5) ON calculateCoins WHEN amount is zero or amount mod 10 is NOT 0 and score is positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.blocks"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((score / 5.0).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "0,1", "0,9", "0,15")
  fun `SHOULD add (score div 5) ON calculateCoins WHEN amount is zero or amount mod 10 is NOT 0 and score zero positive`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.blocks"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(0, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with score x 8 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.colorturn"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 8, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 5, 10, 100])
  fun `SHOULD add 2 ON calculateCoins`(score: Int) {
    listOf(
      "com.relaxingbraintraining.wordcup",
      "com.cocomagic.merger",
      "com.gimica.idlemergefun",
      "com.gimica.wordkitchen"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score))

        assertThat(actual).isEqualTo(UserBalanceUpdate(2, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add 3 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.solitaire",
      "com.cocomagic.mergecarsdefense",
      "com.cocomagic.snake3d",
      "com.cocomagic.slideblock",
      "com.gimica.carsmerge",
      "com.gimica.blockslider",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(3, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add 5 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.mergeblock", "com.gimica.mixblox"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(5, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add 10 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.idlerestaurant"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(10, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add (score div 2) ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.ballrush"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((score / 2.0).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with (score + 1) x 7 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.burningman2"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(((score + 1) * 7.0).roundToInt(), REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add 15 ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.brickmania",
      "com.gimica.ballbounce",
      "com.gimica.atlantis",
      "com.forevergreen.atlantis",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(15, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with (score + 1) ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.crossword"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score + 1, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD add (score div 10) ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.snakez"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate((score / 10.0).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with (score x 10) ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.cocomagic.cannonshooter",
      "com.cocomagic.hole",
      "com.cocomagic.smashball",
      "com.gimica.madsmash",
      "com.gimica.blockholeclash"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 10, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD replace with (score x 20) ON calculateCoins`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.hexapuzzle",
      "com.gimica.hexmatch"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(score * 20, REPLACE))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,500", "10,500", "100,500")
  fun `SHOULD add 5 ON calculateCoins WHEN amount is 500`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.triviamillion",
      "com.gimica.triviamadness"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(5, ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,100", "10,200", "100,300")
  fun `SHOULD add (amount div 2000) + 10 ON calculateCoins WHEN amount is positive and NOT 500`(score: Int, amount: Int) {
    listOf(
      "com.relaxingbraintraining.triviamillion",
      "com.gimica.triviamadness"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(10 + (amount / 2000.0).roundToInt(), ADD))
      }
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "0,1", "10,2", "100,20")
  fun `SHOULD throw CoinCalculationMissingException ON calculateCoins WHEN calculation is not defined for the game`(score: Int, amount: Int) {
    assertFailsWith(CoinCalculationMissingException::class) {
      runBlocking {
        service.calculateCoins(event.copy(applicationId = "some.unrecognized.game", score = score, amount = amount))
      }
    }
  }

  @ParameterizedTest
  @CsvSource(",0,", ",1,3", ",2,3", ",42,3", ",52,3", ",53,", ",208,200")
  fun `SHOULD add amountX3 or 200 ON calculateCoins WHEN 0 gt amount le 52 or 208`(score: Int?, amount: Int, coins: Int?) {
    listOf(
      "com.gimica.solitaireverse",
      "com.gimica.solitaire",
      "com.gimica.solitaireverse.webgl.demo",
      "com.forevergreen.solitaire",
      "com.gimica.spidersolitaire",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(
          if (coins != null) UserBalanceUpdate(coins, ADD, isDemoGame = applicationId == "com.gimica.solitaireverse.webgl.demo")
          else UserBalanceUpdate.noBalanceUpdate
        )
      }
    }
  }

  @ParameterizedTest
  @CsvSource(",0,", ",1,3", ",2,3", ",42,3", ",52,3", ",53,", ",208,200", "0,,3", "5,,3")
  fun `SHOULD replace amountX3 or 200 AND fallback to Add3 ON calculateCoins WHEN 0 gt amount le 52 or 208 AND amount null-notnull`(
    score: Int?,
    amount: Int?,
    coins: Int?
  ) {
    listOf(
      "com.relaxingbraintraining.solitairekingdom",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(
          when (amount) {
            null -> UserBalanceUpdate(3, ADD)
            else ->
              if (coins != null) UserBalanceUpdate(coins, ADD)
              else UserBalanceUpdate.noBalanceUpdate
          }
        )
      }
    }
  }

  @ParameterizedTest
  @CsvSource("3,0", ",0", "4,200", "801,50", "803,50", "804,50", "806,50", "810,50", "802,100", "805,100", "807,100", "809,100", "808,70")
  fun `SHOULD add 200, 50, 100 or 70 on calculateCoins WHEN the score is 4 or in range 800 to 810`(amount: Int?, coins: Int?) {
    listOf(
      "com.gimica.wordseeker"
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, amount = amount))

        assertThat(actual).isEqualTo(
          when (amount) {
            null -> UserBalanceUpdate.noBalanceUpdate
            else ->
              if (coins != null) UserBalanceUpdate(coins, ADD)
              else UserBalanceUpdate.noBalanceUpdate
          }
        )
      }
    }
  }

  @ParameterizedTest
  @CsvSource(",0", "1, 10", "100,0")
  fun `SHOULD add 10 coins WHEN amount is 1 for hexapuzzlefun`(amount: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.hexapuzzlefun", amount = amount))

      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource(",0", "100,0", "101,20", "102,40", "103,60")
  fun `SHOULD add 20, 40, 60 on calculateCoins WHEN amount is 101, 102, 103`(amount: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.puzzlepopblaster", amount = amount))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource(",,0", "100,,0", ",100,0", "101,,20", "102,,50", "103,,100")
  fun `SHOULD add 20,40,100 coins ON calculateCoins WHEN amount is 101, 102, 103`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.marblemadness", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource(",,0", "100,,0", ",100,0", "101,,50", "102,,100", "103,,150", "104,,200")
  fun `SHOULD add 50,100,150,200 coins ON calculateCoins for watersorter WHEN amount is 101, 102, 103, 104`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.watersorter", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource("1,,20", "1,1,10", "1,2,25", "1,3,15", "1,4,100", "1,5,30", "1,6,50")
  fun `SHOULD replace scoreX20 WHEN amount is null OR calculate by new logic if amount not null`(score: Int?, amount: Int?, coins: Int?) {
    listOf(
      "com.relaxingbraintraining.sudokumaster",
      "com.gimica.sudoku",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(
          when (amount) {
            null -> UserBalanceUpdate(1 * 20, REPLACE)
            else ->
              if (coins != null) UserBalanceUpdate(coins, ADD)
              else UserBalanceUpdate.noBalanceUpdate
          }
        )
      }
    }
  }

  @ParameterizedTest
  @CsvSource("1,,20", "1,1,5", "1,2,12", "1,3,10", "1,4,50", "1,5,20", "1,6,25")
  fun `SHOULD replace scoreX20 WHEN amount is null OR calculate by new logic if amount not null new reward logic`(score: Int?, amount: Int?, coins: Int?) {
    listOf(
      "com.gimica.brickdoku",
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(
          when (amount) {
            null -> UserBalanceUpdate(1 * 20, REPLACE)
            else ->
              if (coins != null) UserBalanceUpdate(coins, ADD)
              else UserBalanceUpdate.noBalanceUpdate
          }
        )
      }
    }
  }

  @ParameterizedTest
  @CsvSource(",,0", "99,,0", ",99,0", "100,,20", "101,,50", "102,,125", "103,,300")
  fun `SHOULD add 20,50,125,300 coins ON calculateCoins for tilematchpro WHEN amount is 100, 101, 102, 103`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.tilematchpro", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource("0,0", "5,0", "100,")
  fun `SHOULD correctly calculate coins ON calculateCoins WHEN fairytalematch AND amount is empty`(score: Int, amount: Int?) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.fairytalematch", score = score, amount = amount))
      assertThat(actual).isEqualTo(UserBalanceUpdate(20, ADD))
    }
  }

  @ParameterizedTest
  @CsvSource("0,201", "5,202", "100,301")
  fun `SHOULD correctly calculate coins ON calculateCoins WHEN fairytalematch AND amount is specific`(score: Int, amount: Int?) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.fairytalematch", score = score, amount = amount))
      assertThat(actual).isEqualTo(UserBalanceUpdate(20, ADD))
    }
  }

  @Test
  fun `SHOULD throw GameParameterRequiredException ON calculateCoins WHEN fairytalematch AND empty score`() {
    assertFailsWith(GameParameterRequiredException::class) {
      runBlocking {
        //no score, not specific amount
        service.calculateCoins(event.copy(applicationId = "com.gimica.fairytalematch", score = null, amount = 42))
      }
    }
  }

  @ParameterizedTest
  @CsvSource(",,0", "100,,0", ",100,0", "101,,50", "102,,60", "103,,70", "104,,100", "105,,120", "106,,150", "107,,200", "108,,150", "109,,200")
  fun `SHOULD correct coins ON calculateCoins for spaceconnect WHEN specific amount`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.spaceconnect", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource(",,0", "100,,0", ",100,0", "101,,50", "102,,100", "103,,150", "104,,200", "105,,200", "801,,50", "100500,,0")
  fun `SHOULD correct coins ON calculateCoins for tangram WHEN specific amount`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.gimica.tangram", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource(
    "1,100",
    "2,200",
    "3,0"
  )
  fun `SHOULD add ON calculateCoins for screwpuzzle`(amount: Int, expectedCoins: Int) {
    val event = event.copy(
      applicationId = ApplicationId.PIN_MASTER_APP_ID,
      amount = amount,
    )
    val result = runBlocking { service.calculateCoins(event) }
    assertThat(result.coins).isEqualTo(expectedCoins)
  }

  @ParameterizedTest
  @CsvSource(",,0", "100,,0", ",100,0", "101,,50", "102,,75", "103,,100", "201,,120", "301,,100", "302,,110", "303,,120", "401,,650", "100500,,0")
  fun `SHOULD correctly calculate coins ON calculateCoins WHEN com_bubblechef_bubbleshooter`(amount: Int?, score: Int?, coins: Int) {
    runBlocking {
      val actual = service.calculateCoins(event.copy(applicationId = "com.bubblechef.bubbleshooter", amount = amount, score = score))
      assertThat(actual).isEqualTo(
        when (amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          else ->
            if (coins > 0) UserBalanceUpdate(coins, ADD)
            else UserBalanceUpdate.noBalanceUpdate
        }
      )
    }
  }

  @ParameterizedTest
  @CsvSource("1,,5", "1,1,5", "2,42,10", "3,,15", "20,,100")
  fun `SHOULD correctly calculate coins ON calculateCoins WHEN com_gimica_threadform`(score: Int?, amount: Int?, coins: Int?) {
    listOf(
      THREADFORM_APP_ID,
    ).forEach { applicationId ->
      runBlocking {
        val actual = service.calculateCoins(event.copy(applicationId = applicationId, score = score, amount = amount))

        assertThat(actual).isEqualTo(UserBalanceUpdate(coins!!, REPLACE))
      }
    }
  }

}