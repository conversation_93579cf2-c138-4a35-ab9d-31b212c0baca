package com.moregames.base.liquibase

import liquibase.change.core.*
import liquibase.changelog.ChangeLogParameters
import liquibase.changelog.ChangeSet
import liquibase.parser.ChangeLogParserFactory
import liquibase.resource.ClassLoaderResourceAccessor
import org.junit.jupiter.api.Test
import java.io.File
import kotlin.test.fail

abstract class LiquibaseLinterTestBase {

  @Test
  fun `Liquibase plain sql folder SHOULD contain files that are listed in xml config`() {
    val dir = File(javaClass.getResource("/database")!!.file)

    val sqlFiles = dir.walk()
      .filter { it.isFile }
      .filter { it.extension == "sql" }
      .map { it.absolutePath.toString().replace("${dir.absolutePath}${File.separator}", "").replace("\\", File.separator) }
      .toSet()

    val exceptions = sqlFilePresenceCheckChangeSetExceptions()
    validateChangeSets(exceptions = exceptions) { changeSet ->
      val sqlFilePaths = changeSet.changes.filterIsInstance<SQLFileChange>().mapNotNull { it.path }.map { it.replace("/", File.separator) }
      val notFoundFiles = sqlFilePaths.filter { it !in sqlFiles }
      notFoundFiles.map { "SQL file '$it' is not found in ${dir.toPath()}" }
    }
  }

  @Test
  fun `Liquibase changes with id-like columns column SHOULD contain either a primary key, a foreign key or an index`() {
    val validatableColumns = idLikeColumnsToCheckIndexPresence()
    val exceptions = idLikeColumnsToCheckIndexesChangeSetExceptions()

    validateChangeSets(exceptions = exceptions) { changeSet ->
      val userIdColumns = changeSet.changes.flatMap { change ->
        when (change) {
          is CreateTableChange -> change.columns.filter { column -> validatableColumns.any { it in column.name } }
          is AddColumnChange -> change.columns.filter { column -> validatableColumns.any { it in column.name } }
          else -> emptyList()
        }
      }
      val invalidColumns = userIdColumns.filter { userIdColumn ->
        userIdColumn.constraints?.isPrimaryKey != true && userIdColumn.constraints?.foreignKeyName.isNullOrEmpty() && changeSet.changes.none { change ->
          when (change) {
            is AddPrimaryKeyChange -> validatableColumns.any { it in change.columnNames }
            is AddForeignKeyConstraintChange -> validatableColumns.any { it in change.baseColumnNames }
            is CreateIndexChange -> change.columns.any { column -> validatableColumns.any { it in column.name } }
            else -> false
          }
        }
      }
      invalidColumns.map { column -> "Column '${column.name}' does not have a primary key, a foreign key or an index" }
    }
  }

  private fun validateChangeSets(
    exceptions: List<String> = emptyList(),
    validate: (ChangeSet) -> List<String>
  ) {
    val changelogFileName = "database/liquibase-migration.xml"
    val resourceAccessor = ClassLoaderResourceAccessor()

    val changeSets = ChangeLogParserFactory.getInstance()
      .getParser(changelogFileName, resourceAccessor)
      .parse(changelogFileName, ChangeLogParameters(), resourceAccessor)
      .changeSets

    val violations = changeSets
      .filterNot { changeSet -> changeSet.id in exceptions }
      .flatMap { changeSet -> validate(changeSet).map { changeSet to it } }

    if (violations.isNotEmpty()) {
      fail("Liquibase script contains issues:\n" + violations.joinToString(separator = "\n") { (changeSet, message) -> "Change set '${changeSet.id}': $message" })
    }
  }

  protected open fun sqlFilePresenceCheckChangeSetExceptions(): List<String> =
    listOf()

  protected open fun idLikeColumnsToCheckIndexesChangeSetExceptions(): List<String> =
    listOf()

  protected open fun idLikeColumnsToCheckIndexPresence(): List<String> =
    listOf("user_id")
}