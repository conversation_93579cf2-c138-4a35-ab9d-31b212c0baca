package com.moregames.base.messaging.dto

import com.moregames.base.bigquery.BqEvent
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.BigDecimalAsString
import com.moregames.base.util.InstantAsString
import io.ktor.util.*
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.time.Instant

@Serializable
data class OrganicRevenueReceivedEventDto(
  val eventId: String,
  val timestamp: InstantAsString,
  val source: RevenueReceivedEventDto.RevenueSource = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
  val amount: BigDecimalAsString,
  val createdAt: InstantAsString,
  val network: String? = null,
  val amountExtra: BigDecimalAsString? = null,
  val adUnitFormat: String? = null,
  val countryCode: String? = null,
  val idfv: String? = null,
  val idfa: String? = null,
  val eventToken: String? = null,
  val ip: String? = null,
  val platform: String? = null,
  val packageName: String? = null,
  val adPlacement: String? = null,
  val adUnitId: String? = null,
  val adUnitName: String? = null,
  val applovinRevenue: BigDecimalAsString? = null,
) : BqEvent {
  override val topicName: String = "organic-revenue-received"

  companion object {
    fun from(queryParams: StringValues, createdAt: Instant): OrganicRevenueReceivedEventDto =
      queryParams.let {
        OrganicRevenueReceivedEventDto(
          timestamp = Instant.ofEpochSecond(it["TS"]!!.toLong()),
          idfv = if (it["PLATFORM"]?.uppercase() == AppPlatform.IOS.name) it["IDFV"] else it["IDFV"].orEmpty(),
          idfa = if (it["PLATFORM"]?.uppercase() == AppPlatform.ANDROID.name) it["IDFA"] else it["IDFA"].orEmpty(),
          eventId = it["EVENT_ID"]!!,
          eventToken = it["EVENT_TOKEN"]?.ifBlank { null },
          ip = it["IP"]?.ifBlank { null },
          countryCode = it["CC"]?.ifBlank { null },
          platform = it["PLATFORM"]?.ifBlank { null },
          packageName = it["PACKAGE_NAME"]?.ifBlank { null },
          adPlacement = it["PLACEMENT"]?.ifBlank { null },
          adUnitId = it["AD_UNIT_ID"]?.ifBlank { null },
          adUnitName = it["AD_UNIT_NAME"]?.ifBlank { null },
          applovinRevenue = BigDecimal(it["REVENUE"]),
          amount = BigDecimal(it["ALL_REVENUE"]),
          adUnitFormat = it["FORMAT"]?.ifBlank { null },
          network = it["NETWORK"]?.ifBlank { null },
          createdAt = createdAt
        )
      }
  }
}
