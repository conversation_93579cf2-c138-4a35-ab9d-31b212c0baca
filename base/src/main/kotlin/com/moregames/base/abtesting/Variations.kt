package com.moregames.base.abtesting

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.moregames.base.abtesting.ClientExperiment.*
import kotlin.reflect.KClass

const val VARIATION_KEY_DEFAULT = "default"
private val keySet = mutableSetOf<String>()

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY)
sealed interface BaseVariation {
  fun getKey(): String
}

data object BaseVariationDefault : BaseVariation {
  override fun getKey(): String = VARIATION_KEY_DEFAULT
}

val DEFAULT = BaseVariationDefault

interface TypedVariation : BaseVariation

interface TypedVariationBase<T : TypedVariation> {
  val variations: List<T>
}

fun <T : TypedVariation> TypedVariationBase<T>.findVariation(variationKey: String): T? = variations.firstOrNull { it.getKey() == variationKey }

fun ExperimentBase.findVariation(variationKey: String): BaseVariation? {
  if (variationKey == VARIATION_KEY_DEFAULT) return DEFAULT
  return base?.findVariation(variationKey)
    ?: Variations.byKeys(this, variationKey)
}

class TypedVariationBaseImpl<T : TypedVariation>(clazz: KClass<T>) : TypedVariationBase<T> {
  override val variations: List<T> by lazy { clazz.sealedSubclasses.mapNotNull { it.objectInstance } }
}

inline fun <reified T : TypedVariation> base(): TypedVariationBase<T> = TypedVariationBaseImpl(T::class)

enum class Variations(val variationKey: String, val experiment: ExperimentBase) : BaseVariation {
  TREMENDOUS_GIFT_CARDS("tremendousGiftCards", ClientExperiment.TREMENDOUS_GIFT_CARDS),

  FIVE_MINUTES("fiveMinutes", VIDEO_OFFER_COOL_DOWN),
  TEN_MINUTES("tenMinutes", VIDEO_OFFER_COOL_DOWN),
  ONE_HOUR("oneHour", VIDEO_OFFER_COOL_DOWN),

  SHOW_VIDEO_PREVIEW("showVideoPreview", ClientExperiment.SHOW_VIDEO_PREVIEW),
  SHOW_VIDEO_PREVIEW_TOP2("showVideoPreviewTop2", ClientExperiment.SHOW_VIDEO_PREVIEW),

  TOP_RUNNING_BAR_BLUE_WHITE("topRunningBarBlueWhite", SHOW_TOP_RUNNING_BAR),
  TOP_RUNNING_BAR_TEMPLATED("topRunningBarTemplated", SHOW_TOP_RUNNING_BAR),
  IOS_TOP_RUNNING_BAR_TEMPLATED("iosTopRunningBarTemplated", SHOW_TOP_RUNNING_BAR),
  TOP_RUNNING_BAR_TEMPLATED_DECREASED("topRunningBarTemplatedDecreased", SHOW_TOP_RUNNING_BAR),
  TOP_RUNNING_BAR_TEMPLATED_MAX("topRunningBarTemplatedMax", SHOW_TOP_RUNNING_BAR),
  TOP_RUNNING_BAR_TEMPLATED_AND_EDUCATIONAL("topRunningBarTemplatedAndEducational", SHOW_TOP_RUNNING_BAR),
  IOS_TOP_RUNNING_BAR_TEMPLATED_AND_EDUCATIONAL("iosTopRunningBarTemplatedAndEducational", SHOW_TOP_RUNNING_BAR),

  @Deprecated("no more used, considered as default")
  EM2_EM_1P5("em1p5", EARNINGS_MODEL_V2),

  @Deprecated("no more used, considered as default")
  EM2_EM_1P4("em1p4", EARNINGS_MODEL_V2),
  EM2_BETA_01("em2beta01", EARNINGS_MODEL_V2),
  EM2_BETA_FLAT("em2betaFlat", EARNINGS_MODEL_V2),
  EM2_BOILED_FROG_SOFT("em2BoiledFrogSoft", EARNINGS_MODEL_V2),
  EM2_BOILED_FROG("em2BoiledFrog", EARNINGS_MODEL_V2),
  EM2_STASH_5PCT("em2Stash5pct", EARNINGS_MODEL_V2),
  EM2_STASH_5PCT_DQ("em2Stash5pctDQ", EARNINGS_MODEL_V2),

  // EM2_STASH_5PCT_DQ is badly implemented, but already allocated. this one is same, but with fixed logic
  EM2_STASH_5PCT_DQ_FIXED("em2Stash5pctDQFixed", EARNINGS_MODEL_V2),
  EM2_STASH_ONE_TIME_FIX("em2StashOneTimeFix", EARNINGS_MODEL_V2),
  EM2_STASH_ONE_TIME_FIX_DQ("em2StashOneTimeFixDQ", EARNINGS_MODEL_V2),
  EM2_STASH_TO_EARN_45("em2StashEarn45", EARNINGS_MODEL_V2),
  EM2_STASH_TO_EARN_45_DQ("em2StashEarn45DQ", EARNINGS_MODEL_V2),
  EM2_STASH_TO_EARN_55("em2StashEarn55", EARNINGS_MODEL_V2),
  EM2_STASH_TO_EARN_55_DQ("em2StashEarn55DQ", EARNINGS_MODEL_V2),
  EM2_STASH_TO_EARN_65("em2StashEarn65", EARNINGS_MODEL_V2),
  EM2_INCREASE_COINS_ON_NEW_GAME("em2IncreaseCoinsOnNewGame", EARNINGS_MODEL_V2),
  EM2_RV_FOR_PERSONAL("em2RvForPersonal", EARNINGS_MODEL_V2),
  EM2_REV_SHARE_50("em2RevShare50", EARNINGS_MODEL_V2),
  EM2_REV_SHARE_50_DQ_HT("em2RevShare50DQHT", EARNINGS_MODEL_V2),
  EM2_REV_SHARE_50_DQ_HT_NO_OW("em2RevShare50DQHTnoOW", EARNINGS_MODEL_V2),
  EM3("em3", EARNINGS_MODEL_V2),

  SHOW_TIMER_IN_COIN_GOAL("showTimerInCoinGoal", ClientExperiment.SHOW_TIMER_IN_COIN_GOAL),

  SEON_EMAIL_VERIFICATION("seonEmailVerification", ClientExperiment.SEON_EMAIL_VERIFICATION),

  USE_AMPLITUDE_ANALYTICS("useAmplitudeAnalytics", ClientExperiment.USE_AMPLITUDE_ANALYTICS),

  GAMES_PLAY_INTEGRITY("gamesPlayIntegrity", ClientExperiment.GAMES_PLAY_INTEGRITY),

  ENABLE_VENMO("enableVenmo", ClientExperiment.ENABLE_VENMO),

  INSTALL_BONUS_0P10("iBonus10", EM2_INSTALL_BONUS_SIZE),
  INSTALL_BONUS_0P05("iBonus5", EM2_INSTALL_BONUS_SIZE),
  INSTALL_BONUS_NO("iBonusNo", EM2_INSTALL_BONUS_SIZE),

  GPS_CHECK_ACTIVE("active", ANDROID_GPS_VERIFICATION),
  OFFERWALL_REVENUE_ONLY("ofwRevOnly", ANDROID_GPS_VERIFICATION),
  EARNINGS10("earnings10", ANDROID_GPS_VERIFICATION),
  EARNINGS20("earnings20", ANDROID_GPS_VERIFICATION),
  REVENUE_ABOVE_30("revenueAbove30", ANDROID_GPS_VERIFICATION),

  LESS_ADS_3H("lessAds3h", LESS_ADS_GAMES_ON_BOARDING),
  LESS_ADS_3H_AD_V2("lessAds3hadv2", LESS_ADS_GAMES_ON_BOARDING),
  LESS_ADS_3H_BOOST_50("lessAds3hBoost50", LESS_ADS_GAMES_ON_BOARDING),
  LESS_ADS_3H_BOOST_100("lessAds3hBoost100", LESS_ADS_GAMES_ON_BOARDING),
  LESS_ADS_60M("lessAds60m", LESS_ADS_GAMES_ON_BOARDING),
  LESS_ADS_20M("lessAds20m", LESS_ADS_GAMES_ON_BOARDING),

  INVITED_TO_INTERVIEW("invitedToInterview", IOS_USERS_INTERVIEW),
  INVITED_TO_SURVEY("invitedToSurvey", IOS_USERS_INTERVIEW),

  CONSENT_VARIANT_1("consentVariant1", IOS_GAMES_ATT_CONSENT),
  CONSENT_VARIANT_SIMPLE("simple", IOS_GAMES_ATT_CONSENT),
  CONSENT_VARIANT_AGGRESSIVE("aggressive", IOS_GAMES_ATT_CONSENT),

  IOS_GAME_COIN_GOALS_CONTROL_DEFAULT("controldefault", IOS_GAME_COIN_GOALS),
  IOS_GENERAL_PLUS_TWO_8H("iosGeneralPlusTwo8h", IOS_GAME_COIN_GOALS),
  IOS_GENERAL_ONCE_THEN_3_GAMES_8H("iosGeneralOnceThen3games8h", IOS_GAME_COIN_GOALS),
  IOS_GENERAL_PLUS_TWO_EASY_GOALS_8H("iosGeneralPlusTwoEasyGoals8h", IOS_GAME_COIN_GOALS),
  IOS_GENERAL_PLUS_FIVE_8H("iosGeneralPlusFive8h", IOS_GAME_COIN_GOALS),
  IOS_THREE_GAMES_8H("iosThreeGames8h", IOS_GAME_COIN_GOALS),
  IOS_SIX_GAMES_8H("iosSixGames8h", IOS_GAME_COIN_GOALS),

  SHOW_BADGE_ANDROID("showBadgeAndroid", BEST_COINS_BADGE),
  SHOW_FIRST_GAME_BADGE_ANDROID("showFirstGameBadgeAndroid", BEST_COINS_BADGE),
  SHOW_ALL_GAMES_BADGE_ANDROID("showAllGamesBadgeAndroid", BEST_COINS_BADGE),
  DAILY_V1_BADGE_ANDROID("dailyV1", BEST_COINS_BADGE),
  WEEKLY_V1_BADGE_ANDROID("weeklyV1", BEST_COINS_BADGE),
  DAILY_V2_BADGE_ANDROID("dailyV2", BEST_COINS_BADGE),
  TEST_BADGE_ANDROID("test", BEST_COINS_BADGE),

  ANDROID_CASHOUT_NOW("cashoutNow", ANDROID_CASHOUT_FOR_COINS),
  ANDROID_CASHOUT_COINS("cashoutForCoins", ANDROID_CASHOUT_FOR_COINS),
  ANDROID_HOME_SCREEN_COINS("homeScreenCoins", ANDROID_CASHOUT_FOR_COINS),
  ANDROID_DEADLINE_15_MINUTES("deadline15Minutes", ANDROID_CASHOUT_FOR_COINS),
  ANDROID_DEADLINE_30_MINUTES("deadline30Minutes", ANDROID_CASHOUT_FOR_COINS),
  ANDROID_CASHOUT_QUIT_CONFIRMATION("cashoutQuitConfirmation", ANDROID_CASHOUT_FOR_COINS),

  ANDROID_CASHOUT_SURVEY("androidCashoutSurvey", ClientExperiment.ANDROID_CASHOUT_SURVEY),

  ANDROID_HIDE_EARNINGS_GRAY("androidHideEarningsGray", ANDROID_HIDE_EARNINGS),
  ANDROID_HIDE_EARNINGS_GRAY_GREEN("androidHideEarningsGrayGreen", ANDROID_HIDE_EARNINGS),
  ANDROID_HIDE_FOR_ALL_IN_FIRST_CP("androidHideEarningsForAllInFirstCp", ANDROID_HIDE_EARNINGS),
  ANDROID_HIDE_EARNINGS_FOR_ALL_IN_ALL_CP("androidHideEarningsForAllInAllCp", ANDROID_HIDE_EARNINGS),


  ANDROID_NOTIFY_TO_PLAY("androidNotifyToPlay", ClientExperiment.ANDROID_NOTIFY_TO_PLAY),

  ANDROID_DIGITAL_TURBINE_IGNITE_INSTALL("androidDtiInstall", ClientExperiment.ANDROID_DIGITAL_TURBINE_IGNITE_INSTALL),

  ANDROID_LINEAR_PROGRESS_BAR("linear", ANDROID_CASHOUT_PROGRESS_BAR),
  ANDROID_DASHED_PROGRESS_BAR("dashed", ANDROID_CASHOUT_PROGRESS_BAR),
  ANDROID_CIRCLES_PROGRESS_BAR("circles", ANDROID_CASHOUT_PROGRESS_BAR),

  ANDROID_RESTORE_CASHOUT("androidRestoreCashout", ANDROID_INCOMPLETE_CASHOUT_RESTORING),

  ANDROID_SHOW_OFW_AFTER_FIRST_SC("showOfwAfterFirstSC", ANDROID_SHOW_OFW_AFTER_FIRST_SUCCESSFUL_CASHOUT),

  UNIFIED_ID("unifiedId", ClientExperiment.UNIFIED_ID),
  RAMP_ID("rampId", ClientExperiment.RAMP_ID),

  CHANGE_EMAIL("changeEmail", ANDROID_PAYPAL_HINTS),
  CHANGE_EMAIL_AND_FOLLOWING_TEXT("changeEmailAndFollowingText", ANDROID_PAYPAL_HINTS),

  GROUP_A("groupA", AA_TEST),
  GROUP_B("groupB", AA_TEST),
  GROUP_C("groupC", AA_TEST),
  GROUP_D("groupD", AA_TEST),

  ANDROID_PAYPAL_LIMITS_NO_LIMITS("androidNoLimits", ANDROID_PAYPAL_LIMITS),
  ANDROID_PAYPAL_LIMITS_MINIMUM_3_USD("androidMinimum3usd", ANDROID_PAYPAL_LIMITS),
  ANDROID_PAYPAL_LIMITS_MINIMUM_4_USD("androidMinimum4usd", ANDROID_PAYPAL_LIMITS),
  ANDROID_PAYPAL_LIMITS_MINIMUM_5_USD("androidMinimum5usd", ANDROID_PAYPAL_LIMITS),

  ANDROID_FIXED_FIRST_11_ORDER("fixedFirst11GamesOrder", ANDROID_GAMES_ORDER),
  ANDROID_COINS_FIRST_11_ORDER("coinsFirst11GamesOrder", ANDROID_GAMES_ORDER),
  ANDROID_BLOCK_BUSTER_1ST_ORDER("blockbuster1st", ANDROID_GAMES_ORDER),
  ANDROID_BLOCK_INSTEAD_WBP("blockInsteadWPB", ANDROID_GAMES_ORDER),
  ANDROID_PIN_MASTER_1ST_ORDER("pinMaster1st", ANDROID_GAMES_ORDER),
  ANDROID_BLOCK_BUSTER_3RD_ORDER("blockbuster3rd", ANDROID_GAMES_ORDER),
  ANDROID_BUBBLE_CHEF_1ST_ORDER("bubbleChef1st", ANDROID_GAMES_ORDER),

  GAMES_REMINDERS_INTENSE24H_STORE("intense24hGoogleStore", ANDROID_INSTALL_GAMES_REMINDERS),
  GAMES_REMINDERS_INTENSE24H_PREGAME("intense24hPregameScreen", ANDROID_INSTALL_GAMES_REMINDERS),
  GAMES_REMINDERS_INTENSE6D_HOME_SCREEN("6daysHomeScreen", ANDROID_INSTALL_GAMES_REMINDERS),

  TUTORIAL_FULL_SCREEN("tutorialFullScreen", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_AND_REVIEWS("tutorialFullScreenAndReviews", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_EXPLORE_NOW("exploreNowFullScreen", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_EXPLORE_NOW_HAND("exploreNowFullScreenHand", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_POPUP_EXPLORE_NOW("exploreNowPopup", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_POPUP_EXPLORE_NOW_HAND("exploreNowPopupHand", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_LTI_TM("fullScreenLtiTm", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_LTI_SOLITAIRE("fullScreenLtiSolitaire", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_LTI_CAROUSEL("fullScreenLtiCarousel", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_NO_SHOW_TUTORIAL("noShowTutorial", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_FULL_SCREEN_BLUE_SOCIAL("fullScreenBlue", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_LONG_WITH_PLAYTIME("longWithPlaytime", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_LONG_WITHOUT("longWithout", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_SHORT_WITH_PLAYTIME("shortWithPlaytime", ClientExperiment.TUTORIAL_FULL_SCREEN),
  TUTORIAL_SHORT_WITHOUT("shortWithout", ClientExperiment.TUTORIAL_FULL_SCREEN),

  SHOW_CHALLENGES("showChallenges", ANDROID_CHALLENGES),
  SHOW_CHALLENGES_PROMO("showChallengesPromo", ANDROID_CHALLENGES),

  @Deprecated("no needed after FTUE starts")
  SHOW_CHALLENGES_D1("showChallengesD1", ANDROID_CHALLENGES),

  @Deprecated("no needed after FTUE starts")
  SHOW_CHALLENGES_D2("showChallengesD2", ANDROID_CHALLENGES),
  SHOW_SPECIAL_CHALLENGES("showSpecialChallenges", ANDROID_CHALLENGES),

  LONG_AGGRESSIVE("longAggressive", ANDROID_COIN_GOAL_TEXT),
  SHORTER_AGGRESSIVE("shorterAggressive", ANDROID_COIN_GOAL_TEXT),
  SHORTER("shorter", ANDROID_COIN_GOAL_TEXT),
  SHORT_AGGRESSIVE("shortAggressive", ANDROID_COIN_GOAL_TEXT),

  DOUBLE_FIRST("doubleFirst", ANDROID_COIN_GOAL_VARIATIONS),
  FIRST_X4("firstX4", ANDROID_COIN_GOAL_VARIATIONS),
  FIRST_X6("firstX6", ANDROID_COIN_GOAL_VARIATIONS),
  FIRST_X8("firstX8", ANDROID_COIN_GOAL_VARIATIONS),
  FIRST_X10("firstX10", ANDROID_COIN_GOAL_VARIATIONS),
  HALF_FOR_REST("halfForRest", ANDROID_COIN_GOAL_VARIATIONS),
  QUARTER_FOR_REST("quarterForRest", ANDROID_COIN_GOAL_VARIATIONS),
  REDUCE_IF_FAIL("reduceIfFail", ANDROID_COIN_GOAL_VARIATIONS),
  INCREASE_IF_MEET("increaseIfMeet", ANDROID_COIN_GOAL_VARIATIONS),
  FOLLOW_UP_ON_RESULT("followUpOnResult", ANDROID_COIN_GOAL_VARIATIONS),
  ALL_TOGETHER("allTogether", ANDROID_COIN_GOAL_VARIATIONS),

  AGGRESSIVE_AND_TRUSTWORTHY("aggressiveAndTrustworthy", ANDROID_TUTORIAL_TEXT),
  NOT_AGGRESSIVE_AND_TRUSTWORTHY("notAggressiveAndTrustworthy", ANDROID_TUTORIAL_TEXT),

  LOYALTY_NORMAL("loyaltyNormal", ANDROID_EARN_PLAYING_GAMES_TEXT),
  LOYALTY_FIRE("loyaltyFire", ANDROID_EARN_PLAYING_GAMES_TEXT),
  LOYALTY_DOLLARS("loyaltyDollars", ANDROID_EARN_PLAYING_GAMES_TEXT),
  NO_TEXT("noText", ANDROID_EARN_PLAYING_GAMES_TEXT),

  ANDROID_BEGINNER_FRIENDLY_GAMES_BADGE("androidBeginnerFriendlyGamesBadge", ANDROID_BEGINNER_FRIENDLY_GAMES),
  ANDROID_TASK_IN_PRE_GAME("tasksInPreGame", ANDROID_TASKS_IN_PRE_GAME),
  ANDROID_TASK_IN_PRE_GAME_ONE_TASK("tasksInPreGameOneTask", ANDROID_TASKS_IN_PRE_GAME),

  ALL_GAMES_30MIN_EARN("allGames30minEarn", ANDROID_PLAY_AT_LEAST_BADGES),
  EACH_GAME_UNIQUE("eachGameUnique", ANDROID_PLAY_AT_LEAST_BADGES),
  ALL_GAMES_30MIN_DAILY("allGames30minDaily", ANDROID_PLAY_AT_LEAST_BADGES),

  TICKETS("tickets", ANDROID_COINS_RENAMING),
  BUCKS("bucks", ANDROID_COINS_RENAMING),
  CHIPS("chips", ANDROID_COINS_RENAMING),
  TOKENS("tokens", ANDROID_COINS_RENAMING),

  ANDROID_HIGHLIGHT_GAMES_ON_LOW_EARNINGS_ON("androidHighlightGamesOnLowEarningsOn", ANDROID_HIGHLIGHT_GAMES_ON_LOW_EARNINGS),
  ANIMATION_TO_CELEBRATE_EARNINGS("animationToCelebrateEarnings", ANDROID_ANIMATION_TO_CELEBRATE_EARNINGS),
  ANDROID_SHOW_PAYPAL_LOGO("androidShowPaypalLogo", ClientExperiment.ANDROID_SHOW_PAYPAL_LOGO),
  ANDROID_SOLITAIRE_CLASSIC("androidSolitaireClassic", ANDROID_NEW_SOLITAIRE),

  ANDROID_GAME_STORIES_PRE_GAME("androidGameStoriesPreGame", ANDROID_GAME_STORIES),
  ANDROID_GAME_STORIES_GOOGLE_STORE("androidGameStoriesGoogleStore", ANDROID_GAME_STORIES),

  P90_FOREVER("90forever", ANDROID_HIDE_OFW),
  P90_FIRST_3_DAYS("90first3days", ANDROID_HIDE_OFW),
  P90_FIRST_7_DAYS("90first7days", ANDROID_HIDE_OFW),

  FTUE_PROMOS_D0D5("ftuePromosD0D5", FTUE),
  FTUE_PROMOS_D0D5_CHALLENGES_D1D3("ftuePromosD0D5ChallengesD1D3", FTUE),
  FTUE_PROMOS_D0D5_CHALLENGES_D2D4("ftuePromosD0D5ChallengesD2D4", FTUE),
  FTUE_PROMOS_D2D4_CHALLENGES_D2D4("ftuePromosD2D4ChallengesD2D4", FTUE),
  FTUE_BM_D0_1P5("bmD0_1p5", FTUE),
  FTUE_BM_D0_2P0("bmD0_2p0", FTUE),

  NO_PROMOS_DURING_CHALLENGES("noPromosDuringChallenges", ClientExperiment.NO_PROMOS_DURING_CHALLENGES),

  NEXT_REFRESH("nextRefresh", OFFERWALL_CAMPAIGNS),

  BIG_OFW_GB_ALL_COUNTRIES("bigOfwGbAllCountries", BIG_OFW_GB),

  P10("highlightForP10", HIGHLIGHT_OFW_LOW_ECPM),
  P20("highlightForP20", HIGHLIGHT_OFW_LOW_ECPM),
  P30("highlightForP30", HIGHLIGHT_OFW_LOW_ECPM),

  ANDROID_SHOW_COINS_CONVERSION_RATIO("androidShowCoinsConversionRatio", ClientExperiment.ANDROID_SHOW_COINS_CONVERSION_RATIO),

  MX_NO_OFW_HIGHLIGHT("mxNoOfwHighlight", DISABLE_OFW_MX),
  MX_NO_OFW("mxNoOfw", DISABLE_OFW_MX),

  LUCKY_HOUR_WEEKENDS("luckyHourWeekends", ANDROID_LUCKY_HOUR_2),
  LUCKY_HOUR_WEEKENDS_BM_1("luckyHourWeekendsBM1", ANDROID_LUCKY_HOUR_2),
  LUCKY_HOUR_WEEKENDS_BM_1_25("luckyHourWeekendsBM1.25", ANDROID_LUCKY_HOUR_2),

  BUBBLE_CHEF_INSTEAD_OF_BUBBLE_POP("bubbleChefVSbubblePop", BUBBLE_CHEF_VS_BUBBLE_POP),

  BONUS_CASH_BAR("bonusCashBar", BONUS_BANK),
  // Just a scratch
//  CAROUSEL_V1("carouselV1", BONUS_BANK),
//  PIGGY_BANK_V1("piggyBankV1", BONUS_BANK),

  BOOSTED_GAMES("boostedGames", IOS_BOOSTED_GAMES),
  ANDROID_CUSTOM_GAME_PAGES_V2("androidCustomGamePagesV2", ClientExperiment.ANDROID_CUSTOM_GAME_PAGES_V2),

  SHOW_GAMES_RANKING("showGamesRanking", ANDROID_USER_GAME_RANKING)
  ;

  init {
    if (!keySet.add("${experiment.key}.$variationKey"))
      throw IllegalArgumentException("combination of experiment and key is not unique")
  }

  override fun getKey() = variationKey

  companion object {
    private val keyMap = entries
      .groupBy { it.experiment }
      .mapValues { entry -> entry.value.associateBy { it.variationKey } }

    fun byKeys(experiment: ExperimentBase, variantKey: String): Variations? {
      return keyMap[experiment]?.get(variantKey)
    }
  }
}
