package com.moregames.base.secret

import com.google.cloud.secretmanager.v1.SecretManagerServiceClient
import com.google.cloud.secretmanager.v1.SecretVersionName
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import com.google.inject.Inject
import com.moregames.base.util.Constants.GOOGLE_CLOUD_PROJECT_ID
import com.moregames.base.util.IoCoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Singleton
class SecretService @Inject constructor() {

  private val secretManagerServiceClient = SecretManagerServiceClient.create()
  private val coroutineScope = IoCoroutineScope()

  private val cache: LoadingCache<Secret, Deferred<String>> = CacheBuilder
    .newBuilder()
    .expireAfterWrite(10, TimeUnit.MINUTES)
    .build(CacheLoader.from { secret ->
      coroutineScope.async { loadSecretValue(secret!!) }
    })

  suspend fun secretValue(secret: Secret) = cache[secret].await()

  private suspend fun loadSecretValue(secret: Secret): String =
    withContext(coroutineScope.coroutineContext) {
      secretManagerServiceClient
        .accessSecretVersion(SecretVersionName.of(GOOGLE_CLOUD_PROJECT_ID, secret.key, "latest"))
    }.payload.data.toStringUtf8()
}
