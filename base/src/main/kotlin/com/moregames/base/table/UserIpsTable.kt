package com.moregames.base.table

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.time.Instant

/**
 * Audit of all IPs that were associated with a specific user. This tracking is used for fraud detection
 * - user_id - [User identifier][com.moregames.base.table.UserTable.id]
 * - ip - IP address
 */
object UserIpsTable : Table("playtime.user_ips") {
  val userId = varchar("user_id", 36)
  val ip = varchar("ip", 100)
  val countryCode = varchar("country_code", 2).nullable()
  val updatedAt = timestamp("updated_at").clientDefault { Instant.now() }
}