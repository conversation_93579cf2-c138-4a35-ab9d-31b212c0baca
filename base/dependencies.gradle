dependencies {
  api project(':base-proxy')
  api project(':base:proto')

  api "io.grpc:grpc-netty-shaded:${libs.versions.grpc.java.get()}"
  api "io.grpc:grpc-services:${libs.versions.grpc.java.get()}"
  api "io.grpc:grpc-opentelemetry:${libs.versions.grpc.java.get()}"

  api(libs.jackson.databind)
  api(libs.jackson.module.kotlin)

  implementation ("io.fabric8:kubernetes-client:$kubernetes_client_version") {
    exclude(group: 'io.fabric8', module: 'kubernetes-httpclient-vertx')
  }
  implementation ("io.fabric8:kubernetes-httpclient-okhttp:$kubernetes_client_version")
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  implementation "io.ktor:ktor-server-servlet:$ktor_version"
  implementation "io.ktor:ktor-auth:$ktor_version"
  implementation "io.ktor:ktor-serialization:$ktor_version"
  api "io.ktor:ktor-client-cio:$ktor_version"
  api "io.ktor:ktor-client-serialization:$ktor_version"

  api 'com.sksamuel.aedile:aedile-core:1.3.1'

  implementation "org.jetbrains.exposed:exposed-core:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-jdbc:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-java-time:$exposed_version"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:$kotlinx_coroutines_version"
  api("com.google.cloud.sql:mysql-socket-factory-connector-j-8:1.13.1") {
    // to not transiently load guava-android version of dependency, because it has poorer interface, than
    //    guava-jre
    //noinspection GroovyAssignabilityCheck
    exclude group: "com.google.guava", module: "guava"
  }
  api('com.google.cloud.sql:postgres-socket-factory:1.13.1') {
    exclude group: "com.google.guava", module: "guava"
  }
  implementation 'org.apache.maven:maven-artifact:3.9.6'

  api files("libs/raas-2.3.4.jar")

  implementation "mysql:mysql-connector-java:$mysql_connector_version"
  implementation "org.postgresql:postgresql:42.7.3"
  compileOnly "org.liquibase:liquibase-core:$liquibase_version"

  implementation "com.zaxxer:HikariCP:$hikari_version"

  api "com.google.inject:guice:6.0.0"

  implementation "com.google.guava:guava:33.0.0-jre"
  api "com.google.cloud:google-cloud-secretmanager:1.1.1"
  api "com.google.cloud:google-cloud-pubsub:1.126.0"
  api "com.google.cloud:google-cloud-tasks:2.13.0"
  api "com.google.cloud:google-cloud-logging-logback:0.130.7-alpha"
  implementation "ch.qos.logback:logback-classic:$logback_version"

  implementation "com.sendgrid:sendgrid-java:4.6.8"

  implementation "redis.clients:jedis:3.7.0"

  // launchdarkly
  implementation "com.launchdarkly:launchdarkly-java-server-sdk:7.1.1"
  implementation "com.launchdarkly:api-client:14.0.0"

  //region Opentelemetry
  api "io.opentelemetry:opentelemetry-api:1.43.0"
  api("io.opentelemetry:opentelemetry-extension-kotlin:1.43.0") {
    exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib'
  }
  api("io.opentelemetry:opentelemetry-sdk:1.43.0")
  api("io.opentelemetry:opentelemetry-exporter-prometheus:1.34.1-alpha")
  //endregion

  api 'io.github.resilience4j:resilience4j-kotlin:1.7.1'
  api 'io.github.resilience4j:resilience4j-circuitbreaker:1.7.1'

  testImplementation "net.bytebuddy:byte-buddy:1.10.18"
  testImplementation "net.bytebuddy:byte-buddy-agent:1.10.18"
  testImplementation "org.mockito:mockito-inline:5.2.0"
  testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "org.junit.jupiter:junit-jupiter:$junit_jupiter_version"
  testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_jupiter_version"
  testImplementation "org.junit.jupiter:junit-jupiter-params:$junit_jupiter_version"
  testImplementation "io.ktor:ktor-server-netty:$ktor_version"
  testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_jupiter_version"
  testApi "org.testcontainers:testcontainers:$testcontainers_version"
  testApi "org.testcontainers:gcloud:$testcontainers_version"
  testApi "org.testcontainers:junit-jupiter:$testcontainers_version"
  testApi "net.javacrumbs.json-unit:json-unit:2.38.0"
  testImplementation "com.redis:testcontainers-redis:2.0.1"
  testImplementation "org.awaitility:awaitility-kotlin:4.0.3"
  testImplementation "io.ktor:ktor-server-test-host:$ktor_version"
  testImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.22"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "org.junit-pioneer:junit-pioneer:1.7.1"
  testImplementation "com.zaxxer:HikariCP:$hikari_version"
  testImplementation "net.javacrumbs.json-unit:json-unit:2.38.0"

  testFixturesImplementation "com.zaxxer:HikariCP:$hikari_version"
  testFixturesApi files("libs/wix-embedded-mysql-4.6.3-alpo-1.jar")
  testFixturesApi files("libs/wix-embedded-mysql-download-and-extract-4.6.3-alpo-1.jar")
  testFixturesImplementation "org.jetbrains.exposed:exposed-core:$exposed_version"
  testFixturesImplementation "org.junit.jupiter:junit-jupiter:$junit_jupiter_version"
  testFixturesApi "org.liquibase:liquibase-core:$liquibase_version"
  testFixturesImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testFixturesImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testFixturesImplementation 'org.mockito:mockito-core:5.2.0'
  testFixturesImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testFixturesImplementation "org.mockito:mockito-inline:5.2.0"
  testFixturesImplementation "com.madgag.spongycastle:bcpkix-jdk15on:1.58.0.0"
  testFixturesImplementation "org.junit-pioneer:junit-pioneer:1.7.1"
  testFixturesApi "net.javacrumbs.json-unit:json-unit:2.38.0"
  testFixturesImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testFixturesImplementation "io.ktor:ktor-client-serialization:$ktor_version"
  testFixturesImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testFixturesApi "org.testcontainers:mysql:$testcontainers_version"
  testFixturesApi "org.testcontainers:postgresql:$testcontainers_version"
  testFixturesImplementation "com.redis:testcontainers-redis:2.0.1"
  // wix-embedded-mysql dependencies
  testFixturesApi "de.flapdoodle.embed:de.flapdoodle.embed.process:2.0.3"
  testFixturesApi "commons-io:commons-io:2.6"
  testFixturesApi "com.tngtech.archunit:archunit:1.3.0"
  testFixturesImplementation testFixtures(project(":base:proto"))
}
