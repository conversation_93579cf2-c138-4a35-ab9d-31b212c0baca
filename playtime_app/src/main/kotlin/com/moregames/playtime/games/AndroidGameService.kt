package com.moregames.playtime.games

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.playtime.administration.blacklist.BlacklistService
import com.moregames.playtime.rewarding.RewardingFacade.Companion.isGameAllowedForEm2Participants
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.gamerank.UserGameRankService
import com.moregames.playtime.user.offer.AndroidGameOffer
import com.moregames.playtime.user.offer.AndroidGameOrderExperimentService
import com.moregames.playtime.user.offer.AndroidGameReplacementService
import java.util.*

@Singleton
class AndroidGameService @Inject constructor(
  private val gamePersistenceService: GamePersistenceService,
  private val abTestingService: AbTestingService,
  private val blacklistService: BlacklistService,
  private val translationService: UserTranslationService,
  private val androidGameOrderExperimentService: AndroidGameOrderExperimentService,
  private val androidGameReplacementService: AndroidGameReplacementService,
  private val userGameRankService: UserGameRankService,
) {

  companion object {
    const val GAME_INSTALLATION_LINK_TEMPLATE = "https://play.google.com/store/apps/details?id=%s"
  }

  /**
   * this list should be sorted by the orderKey from the database
   */
  suspend fun loadGameOffers(user: UserDto, locale: Locale, loadLatGamesOnly: Boolean): List<AndroidGameOffer> {
    val isEm2Participant = abTestingService.isEm2Participant(user.id)
    return when {
      user.googleAdId != null && blacklistService.isGoogleAdIdBlacklisted(user.googleAdId) ->
        gamePersistenceService.loadVisibleAndroidGimicaGamesOffer(locale.language)

      else ->
        gamePersistenceService.loadVisibleGames(locale.language, user.appPlatform)
    }
      .let { androidGameOrderExperimentService.addGameToTheTop(user.id, it) }
      .let { androidGameReplacementService.replaceGames(user, it) }
      .filter { !loadLatGamesOnly || it.showForLat }
      .filter { byEm2Participant(isEm2Participant, it.applicationId) }
      .map { applyAndroidUserGameRankingExperiment(user.id, it) }
      .map { translateGame(it, locale, user.id) }
      .sortedBy { it.orderKey }
  }

  private suspend fun applyAndroidUserGameRankingExperiment(userId: String, offer: AndroidGameOffer): AndroidGameOffer {
    val modifiers = userGameRankService.getOfferModifiers(userId, offer.id)
    return if (modifiers != null) {
      offer.copy(imageFilename = modifiers.imageFilename, infoTextInstallBottom = modifiers.infoTextInstallBottom)
    } else offer
  }

  private fun byEm2Participant(isEm2Participant: Boolean, applicationId: String): Boolean {
    if (!isEm2Participant) {
      return true
    }
    return isGameAllowedForEm2Participants(applicationId)
  }

  private suspend fun translateGame(game: AndroidGameOffer, locale: Locale, userId: String) =
    with(translationService) {
      val installTop = tryTranslate(game.infoTextInstallTop, locale, userId)
      val installBottom = tryTranslate(game.infoTextInstallBottom, locale, userId)
      game.copy(
        description = tryTranslate(game.description, locale, userId),
        infoTextInstallTop = installTop,
        infoTextInstallBottom = installBottom,
      )
    }
}