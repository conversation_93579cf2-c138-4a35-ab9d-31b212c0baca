package com.moregames.playtime.translations

import com.moregames.playtime.user.cashout.FaceScanPreScreenService.Companion.BIPA_HTML_DEFAULT_TEXT

enum class TranslationResource(val resourceName: String, val defaultValue: String) {

  YOU_REACHED_EARNING_CAP(
    "\$_you_reached_earning_cap",
    "You have reached the earning cap. Congrats on that! In order to keep your earnings coming in, please cash out your existing earnings."
  ),
  DONATION_PROCESS_STARTED("\$_donation_process_started", "The donation process has started."),
  PAYMENT_PROCESS_STARTED("\$_payment_process_started", "The payment process has started."),
  PAYMENT_PROCESSED("\$_payment_processed", "Your payment is being processed."),
  EMAIL_NOT_ASSOCIATED_WITH_PAYPAL(
    "\$_email_not_associated_with_paypal",
    "The email address that has been used for this payout ({payment_recipientEmail}) is not associated with any PayPal account. PayPal allows you to open an account for this email address within 30 days in order to claim your payout. Otherwise, the payout will return to your JustPlay balance after 30 days."
  ),
  PAYPAL_HOLDING_PAYOUT("\$_paypal_holding_payout", "PayPal is currently holding your payout. For more details please log in to your PayPal account."),
  DONATION_SUCCESSFULLY_SENT("\$_donation_successfully_sent", "The donation was successfully sent."),
  PAYPAL_SUCCESSFULLY_SENT_FEES_APPLIED(
    "\$_paypal_successfully_sent_fee_applied",
    "The payout was successfully sent to the PayPal account {payment_recipientEmail} with 5% fee applied."
  ),
  PAYPAL_SUCCESSFULLY_SENT("\$_paypal_successfully_sent", "The payout was successfully sent to the PayPal account {payment_recipientEmail}."),
  PAYOUT_SUCCESSFULLY_SENT("\$_payout_successfully_sent", "The payout was successfully sent to {payment_recipientEmail}."),
  PAYPAL_PAYOUT_REJECTED("\$_paypal_payout_rejected", "PayPal has rejected the payout to your account: {payment_recipientEmail}."),
  PAYOUT_REJECTED("\$_payout_rejected", "The payout to {payment_recipientEmail} was rejected, please try again."),
  CLAIM_GIFT_CARD("\$_claim_gift_card", "How to redeem your {formatted_amount} gift card:"),
  THANKS_FOR_DONATION("\$_thanks_for_donation", "Thank you for your donation of {formatted_amount} to"),
  SECURITY_CODE("\$_security_code", "Security code"),
  GIFT_CODE("\$_gift_code", "Gift code"),
  REDEMPTION_URL("\$_redemption_url", "Redemption URL"),
  JP_DOLLAR_TO_DOLLAR_DONATION(
    "\$_jp_dollar_to_dollar_donation",
    "We at JustPlay will match any donation Dollar for Dollar to do our part and with that maximise your impact on the issues you care about."
  ),
  NOTIFICATION_KEEP_PLAYING("\$_notification_keep_playing", "Keep playing to make the most earnings"),
  NOTIFICATION_30MIN_TO_CASHOUT("\$_notification_30min_to_cashout", "30 minutes to cashout!"),
  EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT(
    "\$_exception_liveness_selfie_tips",
    "Having issues with scanning your face?\n" +
      "1. Try to gently clean your camera with a dry cloth.\n" +
      "2. Make sure there is sufficient light around you, preferably daylight, but avoid any direct light on the camera and glare.\n" +
      "3. Place your phone on a stable surface at your eye level.\n" +
      "4. Try taking off your glasses if you're wearing any."
  ),
  EXCEPTION_UNIQUENESS(
    "\$_exception_uniqueness",
    "You have already been associated with another JustPlay account in the past. Please verify another person for this cash-out"
  ),
  KEEP_PLAYING_GAME("\$_keep_playing_game", "Keep playing {gameName}!"),
  MORE_YOU_PLAY_MORE_YOU_EARN("\$_more_you_play_more_you_earn", "The more you play the more you earn!"),
  COME_BACK_AND_EARN("\$_come_back_and_earn", "Come back and earn even more!"),
  CONGRATULATIONS_ON_CASHOUT("\$_congratulations_on_cashout", "Congratulations on Cashout!"),
  YOU_MISSED_OUT_ON_EARNINGS("\$_you_missed_out_on_earnings", "Oh no you missed out on earnings!"),
  JUST_PLAY_A_FEW_MINUTES_TO_MAKE_MONEY("\$_just_play_a_few_minutes_to_make_money", "Just play a few minutes to make money!"),
  YOUR_DEVICE_IS_LAT("\$_your_device_is_lat", "Your device does not have an Advertising ID enabled and as result your JustPlay app experience is limited."),
  KEEP_PLAYING_TO_MAXIMIZE_EARNINGS("\$_keep_playing_to_maximize_earnings", "Keep playing to maximize your earnings"),
  ALL_COINS_OVER_COIN_GOAL_ARE_EARNINGS(
    "\$_all_coins_over_coin_goal_are_earnings",
    "Keep playing! All the loyalty coins earned over the loyalty coin goal are additional earnings!"
  ),
  REACH_COIN_GOAL("\$_reach_coin_goal", "Reach the loyalty coin goal!"),
  YOU_NOT_REACHED_COIN_GOAL_REACH_IT(
    "\$_you_not_reached_coin_goal_reach_it",
    "Looks like you haven't reached your loyalty coin goal, reach it to maximize your earnings!"
  ),
  WELCOME_COINS_OFFER_AVAILABLE("\$_welcome_coins_offer_available", "Your commitment reward is ready to claim!"),
  MENU_ITEM_TUTORIAL_HUB("\$_menu_item_tutorial_hub", "Tutorials"),
  MENU_ITEM_MY_REWARDS("\$_my_reward_text", "My Rewards"),
  MENU_ITEM_CONTACT_US("\$_contact_us", "Help"),
  MENU_ITEM_PRIVACY_POLICY("\$_privacy_policy", "Privacy policy"),
  MENU_ITEM_FAQ("\$_menu_item_faq", "Loyalty Program Rules"),
  MENU_ITEM_ACCOUNT_DELETION("\$_menu_item_account_deletion", "Request Account Deletion"),
  SURVEY_OPTION_HAPPY("\$_survey_oncashout_happy_title", ""),
  SURVEY_OPTION_NEUTRAL_CASHOUT_AMOUNT("\$_survey_oncashout_neutral_suboption_cashout_amount", "Cash-out amounts"),
  SURVEY_OPTION_NEUTRAL_CASHOUT_PROCESS("\$_survey_oncashout_neutral_suboption_cashout_process", "Cash-out process"),
  SURVEY_OPTION_NEUTRAL_PAYOUT_OPTIONS("\$_survey_oncashout_neutral_suboption_payout_options", "Payout options"),
  SURVEY_OPTION_NEUTRAL_VERIFICATION("\$_survey_oncashout_neutral_suboption_verification_process", "Verification process"),
  SURVEY_OPTION_UNHAPPY_DATA_PRIVACY("\$_survey_oncashout_unhappy_suboption_data_privacy_concerns", "Data privacy concerns"),
  SURVEY_OPTION_UNHAPPY_LONG_CASHOUT("\$_survey_oncashout_unhappy_suboption_long_cashout_process", "Long cash-out process"),
  SURVEY_OPTION_UNHAPPY_LOW_AMOUNT("\$_survey_oncashout_unhappy_suboption_low_cash_amount", "Low cash-out amounts"),
  SURVEY_OPTION_UNHAPPY_OTHERS("\$_survey_oncashout_unhappy_suboption_other", "Others"),
  SURVEY_OPTION_UNHAPPY_DELAYED_PAYMENT("\$_survey_oncashout_unhappy_suboption_payment_delay", "Delayed payment"),
  SURVEY_OPTION_UNHAPPY_LOW_PAYOUT_OPTIONS("\$_survey_oncashout_unhappy_suboption_payout_options_shortage", "Not enough payout options"),
  SURVEY_OPTION_UNHAPPY_UNCLEAR_CASHOUT("\$_survey_oncashout_unhappy_suboption_unclear_cashout_instructions", "Unclear cash-out instructions"),
  IOS_NOTIFICATION_BALANCE_UPDATED_DESCRIPTION("\$_ios_notification_balance_updated_description", "You now have {coins} loyalty points!"),
  IOS_NOTIFICATION_BALANCE_UPDATED_EXP_DESCRIPTION("\$_ios_notification_balance_updated_exp_description", "You now have {coins} testing experience points!"),
  IOS_NOTIFICATION_CASHOUT_PROCESSED_TITLE("\$_ios_notification_cashout_processed_title", "Congratulations!"),
  IOS_NOTIFICATION_CASHOUT_PROCESSED_DESCRIPTION("\$_ios_notification_cashout_processed_description", "Your reward is ready!"),
  IOS_NOTIFICATION_EARNING_ADDED_TITLE("\$_ios_notification_earnings_added_title", "{formatted_amount} added to your JustPlay balance"),
  IOS_NOTIFICATION_EARNING_ADDED_DESCRIPTION("\$_ios_notification_earnings_added_description", "Congratulations! Now you can withdraw your JustPlay reward!"),
  GOOGLE_PLAYSTORE_OPENED_NOTIFICATION_TITLE("\$_google_playstore_notification_title", "You're on Track!"),
  GOOGLE_PLAYSTORE_OPENED_NOTIFICATION_DESCRIPTION(
    "\$_google_playstore_notification_description",
    "This is it! You're one step away from starting to earn money, download {gameName} now to start earning!"
  ),
  IOS_CASHOUT_EMAIL_HINT("\$_ios_cashout_email_hint", "Email address"),
  GAME_UNLOCK_REMINDER_TEXT("\$_game_unlock_reminder_offer_subtext", "{gameName} is now available for you to Play!"),
  GAME_UNLOCK_NOTIFICATION_TITLE("\$_game_unlock_notification_title", "New game unlocked! \uD83C\uDF89"),
  GAME_UNLOCK_NOTIFICATION_DESCRIPTION(
    "\$_game_unlock_notification_description",
    "Hooray! you\'ve unlocked {gameName}. \uD83C\uDF89 Don\'t miss out and try it out now!"
  ),
  GAME_UNLOCK_NOTIFICATION_BUTTON("\$_game_unlock_notification_button", "Try it Out!"),
  ROOKIE("\$_rookie", "Rookie"),
  NOVICE("\$_novice", "Novice"),
  APPRENTICE("\$_apprentice", "Apprentice"),
  EXPLORER("\$_explorer", "Explorer"),
  WATCHER("\$_watcher", "Watcher"),
  SEEKER("\$_seeker", "Seeker"),
  CHALLENGER("\$_challenger", "Challenger"),
  MASTER("\$_master", "Master"),
  CONQUEROR("\$_conqueror", "Conqueror"),
  LEGEND("\$_legend", "Legend"),
  CHAMPION("\$_champion", "Champion"),

  //region Black Friday promotion
  EARN_PLAYING_GAMES_BF("\$_earn_playing_games_bf", "Limited Timer Offer!"),
  NOTIFICATION_BALANCE_UPDATED_BF("\$_notificationBalanceUpdateText_bf", "You now have %s boosted coins!"),
  NOTIFICATION_BALANCE_UPDATED_TITLE_BF("\$_notificationBalanceUpdateTitle_bf", "Boosted coins!"),
  BALANCE_TITLE_BF("\$_balance_title_bf", "Boosted coins:"),

  //endregion
  CASHOUT_TIMER_SUBTEXT("\$_cashout_timer_sub_text", "\$\$\$ Boosted"),
  CHALLENGES_COMPLETED_NOTIFICATION_TITLE("\$_challenges_completed_notification_title", "Challenge Completed. Claim Your Golden Ticket!"),
  CHALLENGES_1ST_COMPLETED_NOTIFICATION_TITLE("\$_challenges_1st_completed_notification_title", "First Challenge Done!"),
  CHALLENGES_1ST_COMPLETED_NOTIFICATION_BODY(
    "\$_challenges_1st_completed_notification_body",
    "Off to a strong start! ✅ Keep going to grab your \$\$\$ cash bonus! \uD83D\uDCB8"
  ),
  CHALLENGES_3RD_COMPLETED_NOTIFICATION_TITLE("\$_challenges_3rd_completed_notification_title", "Over Halfway! Nice!"),
  CHALLENGES_3RD_COMPLETED_NOTIFICATION_BODY(
    "\$_challenges_3rd_completed_notification_body",
    "Challenge #3 is DONE! \uD83D\uDCAA Just two more to lock in your \$\$\$ cash bonus! \uD83D\uDE80"
  ),
  CHALLENGES_4TH_COMPLETED_NOTIFICATION_TITLE("\$_challenges_4th_completed_notification_title", "One More to get your \$\$\$\$!"),
  CHALLENGES_4TH_COMPLETED_NOTIFICATION_BODY(
    "\$_challenges_4th_completed_notification_body",
    "You’ve crushed 4 challenges—just 1 left for your \$\$\$\$ cash bonus! \uD83D\uDCB0"
  ),
  REWARD_EARNINGS_NOTIFICATION_TITLE("\$_reward_earnings_notification_title", "{formatted_amount} Bonus Received \uD83D\uDE80"),
  CASHOUT_BIPA_PRE_SCREEN_HTML_TEXT("\$_cashout_bipa_pre_screen_html_text", BIPA_HTML_DEFAULT_TEXT),
  BALANCE_UPDATE_NOTIFICATION_TITLE("\$_balance_update_notification_title", "Balance Update!"),
  BALANCE_UPDATE_NOTIFICATION_BODY("\$_balance_update_notification_body", "You now have %s loyalty coins!"),
  CHALLENGE_CLAIM_TEXT("\$_challenge_claim_text", "You've won a golden ticket"),
  CHALLENGE_CLAIM_BUTTON_TEXT("\$_challenge_claim_button_text", "Claim Your Bonus!"),
  CHALLENGE_AHEAD_BUTTON_TEXT("\$_challenge_ahead_button_text", "Bonus Ahead!"),
  PROMOTION_UPDATE_VERSION_BUTTON_TEXT("\$_promotion_update_version_button_text", "Update Now"),
  GAME_RANK_RULES_TITLE("\$_game_rank_rules_title", "Game Rank Rules"),
  SPECIAL_CHALLENGE_QUEST_COMPLETED_NOTIFICATION_TITLE("\$_special_challenge_quest_completed_notification_title", "Quest Completed! Claim Your Key \uD83D\uDDDD\uFE0F"),
  SPECIAL_CHALLENGE_QUEST_COMPLETED_NOTIFICATION_DESCRIPTION("\$_special_challenge_quest_completed_notification_description", "Collect all keys to unbox your cash treasure!"),
  SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_TITLE("\$_special_challenge_key_collected_notification_title", "Cash Treasure Key Collected \uD83D\uDDDD\uFE0F\uD83C\uDF81"),
  SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_DESCRIPTION("\$_special_challenge_key_collected_notification_description", "Collect all keys to unbox your cash treasure!"),
  SPECIAL_CHALLENGE_BONUS_RECEIVED_NOTIFICATION_TITLE("\$_special_challenge_bonus_received_notification_title", "Treasure Bonus Received \uD83D\uDCB5\uD83C\uDF81"),
  SPECIAL_CHALLENGE_BONUS_RECEIVED_NOTIFICATION_DESCRIPTION("\$_special_challenge_bonus_received_notification_description", "A new cash treasure is waiting for you, collect new keys to win again!"),
  ;

  companion object {
    fun fromResourceName(resourceName: String) =
      entries.first { it.resourceName == resourceName }
  }

}