package com.moregames.playtime.buseffects

import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.COIN_NOTIFICATION_COOLDOWN
import com.moregames.base.abtesting.variations.CooldownCoinNotificationVariation
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bigquery.BqEvent
import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.util.InstantAsString
import com.moregames.base.util.TimeService
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.playtime.app.isPushNotificationsTrackingEnabled
import com.moregames.playtime.notifications.PushNotification
import com.moregames.playtime.notifications.PushNotification.*
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.*
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.PlayFirstGameReminder
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.notifications.android.AndroidPushNotificationService
import com.moregames.playtime.notifications.ios.IosPushNotificationService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout2xoffer.Cashout2xOfferService
import kotlinx.serialization.Serializable
import redis.clients.jedis.params.SetParams
import javax.inject.Inject

@Singleton
class PushNotificationEffectHandler @Inject constructor(
  private val userNotificationStatusService: UserNotificationStatusService,
  private val androidPushNotificationService: AndroidPushNotificationService,
  private val iosPushNotificationService: IosPushNotificationService,
  private val userService: UserService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val applicationConfig: ApplicationConfig,
  private val timeService: TimeService,
  private val abTestingService: AbTestingService,
  private val safeJedisClient: SafeJedisClient,
  private val cashout2xOfferService: Cashout2xOfferService,
) {

  @EffectHandler
  suspend fun handlePushNotificationEffect(effect: PushNotificationEffect) {
    val user = userService.getUser(effect.notification.userId, true)
    if (user.isDeleted) {
      logger().info("Rejected push notification for deleted user: $effect")
      return
    }
    if (!userNotificationStatusService.areNotificationsEnabled(user.id)) return
    val notification = effect.notification

    val notificationSent = when (notification) {
      is AndroidPushNotification -> handleAndroidPushNotification(user, notification)
      is IosPushNotification -> handleIosPushNotification(user, notification)
      is CrossPlatformPushNotification -> handleCrossPlatformNotification(user, notification)
    }

    if (notificationSent) trackPushNotification(notification, user.appPlatform)
  }

  private suspend fun handleAndroidPushNotification(user: UserDto, notification: AndroidPushNotification): Boolean {
    if (user.appPlatform != ANDROID) {
      logger().alert("Wrong user platform for the Android notification effect: $notification")
      return false
    }

    when (notification) {
      is FirstCoinsForGamePushNotification -> androidPushNotificationService.sendFirstCoinsForGameNotification(notification, user.locale)

      is FirstCashoutOfDayNotification -> androidPushNotificationService.sendOnFirstCashoutOfDayNotification(notification, user.locale)

      is XMinutesToCashoutPeriodEndNotification -> androidPushNotificationService.sendXMinutesToCashoutPeriodEndNotification(notification, user.locale)

      is ReachCoinGoalNotification -> androidPushNotificationService.sendReachCoinGoalNotification(notification, user.locale)

      is MissedEarningsNotification -> androidPushNotificationService.sendMissedEarningsNotification(notification, user.locale)

      is InactivityReminder -> androidPushNotificationService.sendInactivityReminder(notification, user.locale)

      is CashoutFailedNotification -> androidPushNotificationService.sendCashoutFailedNotification(notification)

      is WelcomeCoinsOfferAvailableNotification -> androidPushNotificationService.sendWelcomeCoinsOfferAvailableNotification(notification, user.locale)

      is GooglePlayOpenedNotification -> androidPushNotificationService.sendGooglePlayOpenedNotification(notification, user.locale)

      is SurveyNotification -> androidPushNotificationService.sendSurveyNotification(notification)

      is GameUnlockedNotification -> androidPushNotificationService.sendGameUnlockedNotification(notification, user.locale)

      is RemindToPlayNotification -> androidPushNotificationService.sendRemindToPlayNotification(notification)

      is ContinueIncompleteCashoutNotification -> androidPushNotificationService.sendContinueIncompleteCashoutNotification(notification)

      is InstallGameReminder -> androidPushNotificationService.sendInstallGameReminder(notification)

      is RewardEarningsAddedNotification -> androidPushNotificationService.sendRewardEarningsAdded(notification)

      is RewardSpecialChallengesEarningsAddedNotification -> androidPushNotificationService.sendSpecialChallengeEarningsAdded(notification)

      is LuckyHourNotification -> androidPushNotificationService.sendLuckyHourNotification(notification)

      is Cashout2xOfferActivatedNotification -> androidPushNotificationService.sendCashout2xOfferActivatedNotification(notification)

      is CashoutOfferStarted -> androidPushNotificationService.sendCashoutOfferStartedNotification(notification)

      is CashoutOfferBalanceUpdate -> androidPushNotificationService.sendCashoutOfferBalanceUpdatedNotification(notification, user.locale)

      is ChallengeCompletedNotification -> androidPushNotificationService.sendChallengeCompleted(notification)

      is ChallengeSpecialOfferActivatedNotification -> androidPushNotificationService.sendChallengeSpecialOfferActivatedNotification(notification)

      is SpecialChallengeCompletedNotification -> androidPushNotificationService.sendSpecialChallengeCompleted(notification)

      is SpecialChallengeClaimedNotification -> androidPushNotificationService.sendSpecialChallengeClaimed(notification)

      is KeepDoingChallengesNotification -> androidPushNotificationService.sendKeepDoingChallenges(notification)

      is DayStreakRewardReadyNotification -> androidPushNotificationService.sendDayStreakRewardReadyNotification(notification)

      is OnOfferwallNowAllowedNotification -> androidPushNotificationService.sendOfferwallNowAllowedNotification(notification)

      is GameRankUpdatedNotification -> androidPushNotificationService.sendGameRankUpdated(notification)
    }
    return true
  }

  private suspend fun handleIosPushNotification(user: UserDto, notification: IosPushNotification): Boolean {
    if (user.appPlatform != IOS && user.appPlatform != IOS_WEB) {
      logger().alert("Wrong user platform for the iOS notification effect: $notification")
      return false
    }
    when (notification) {
      is PlayFirstGameReminder -> iosPushNotificationService.sendPlayFirstGameReminder(notification)

      is ShareYourExperienceNotification -> iosPushNotificationService.sendShareYourExperienceNotification(notification)
    }
    return true
  }

  private suspend fun handleCrossPlatformNotification(user: UserDto, notification: CrossPlatformPushNotification): Boolean {
    when (notification) {
      is BalanceUpdatedNotification -> {
        if (isCoolDownBalanceUpdatedNotification(notification.userId)) return false
        when (user.appPlatform) {
          ANDROID -> androidPushNotificationService.sendBalanceUpdatedNotification(
            notification = notification,
            locale = user.locale,
            cashout2xOfferActive = cashout2xOfferService.isOfferActive(user.id)
          )

          IOS, IOS_WEB -> iosPushNotificationService.sendBalanceUpdatedNotification(notification, user.locale, user.appVersion)
        }
      }

      is CashoutProcessedNotification -> when (user.appPlatform) {
        ANDROID -> androidPushNotificationService.sendCashoutProcessedNotification(notification)
        IOS, IOS_WEB -> iosPushNotificationService.sendCashoutProcessedNotification(notification, user.locale)
      }

      is EarningsAddedNotification -> when (user.appPlatform) {
        ANDROID -> androidPushNotificationService.sendEarningsAddedNotification(notification, user.locale)
        IOS, IOS_WEB -> iosPushNotificationService.sendEarningsAddedNotification(notification, user.locale)
      }

      is RatingPromptCommand -> when (user.appPlatform) {
        ANDROID -> androidPushNotificationService.sendRatingPromptCommand(notification)
        IOS, IOS_WEB -> iosPushNotificationService.sendRatingPromptCommand(notification)
      }

      is GameCoinGoalReachedNotification -> when (user.appPlatform) {
        ANDROID -> androidPushNotificationService.sendGameCoinGoalReachedNotification(notification)
        IOS, IOS_WEB -> iosPushNotificationService.sendGameCoinGoalReachedNotification(notification)
      }

      is UnclaimedEarningNotification -> when (user.appPlatform) {
        ANDROID -> androidPushNotificationService.sendUnclaimedEarningNotification(notification)
        IOS, IOS_WEB -> iosPushNotificationService.sendUnclaimedEarningNotification(notification)
      }
    }
    return true
  }

  private suspend fun isCoolDownBalanceUpdatedNotification(userId: String): Boolean {
    val coolDownVariation = abTestingService.assignedVariationValue(userId = userId, COIN_NOTIFICATION_COOLDOWN)
    if (coolDownVariation is CooldownCoinNotificationVariation) {
      val coolDownWasSaved = safeJedisClient.set(
        key = "COIN_NOTIFICATION_COOLDOWN_$userId",
        value = "COOLDOWN",
        SetParams().ex(coolDownVariation.period.inWholeSeconds).nx()
      )
      if (!coolDownWasSaved) {
        logger().debug("CoinNotificationCoolDown for user = $userId")
        return true
      }
    }
    return false
  }

  private suspend fun trackPushNotification(notification: PushNotification, appPlatform: AppPlatform) {
    if (featureFlagsFacade.isPushNotificationsTrackingEnabled()) {
      bigQueryEventPublisher.publish(
        PushNotificationSentBqEvent(
          userId = notification.userId,
          platform = appPlatform,
          label = notification.label,
          market = applicationConfig.justplayMarket,
          createdAt = timeService.now(),
        )
      )
    }
  }

}

data class PushNotificationEffect(
  val notification: PushNotification
) : AsyncEffect

@Serializable
data class PushNotificationSentBqEvent(
  val userId: String,
  val platform: AppPlatform,
  val label: String,
  val market: String,
  val createdAt: InstantAsString,
) : BqEvent {
  override val topicName: String = "push-notification-sent"
}
