package com.moregames.playtime.buseffects


import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.TimeService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.ChallengeCompletedNotification
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.KeepDoingChallengesNotification
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.SpecialChallengeCompletedNotification
import com.moregames.playtime.translations.TranslationResource.*
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeState
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ChallengeCompletedEffectHandler @Inject constructor(
  private val challengeService: ChallengeService,
  private val userService: UserService,
  private val userTranslationService: UserTranslationService,
  private val messageBus: MessageBus,
  private val gamesService: GamesService,
  private val bqPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
) {
  @EffectHandler
  suspend fun handle(challengeCompleted: ChallengeCompletedEffect) {
    val user = userService.getUser(challengeCompleted.userId)

    sendChallengeCompletedNotification(challengeCompleted, user.locale)
    sendKeepDoingChallengesNotification(challengeCompleted.userId, user.locale)
    sendAmplitudeEvent(challengeCompleted, user.appPlatform)
    sendBqEvent(challengeCompleted, user)
  }

  private suspend fun sendBqEvent(
    challengeCompleted: ChallengeCompletedEffect,
    user: UserDto
  ) {
    bqPublisher.publish(UserChallengeUpdatedBqDto(
      userId = user.id,
      challengeId = challengeCompleted.challengeId,
      challengeEventId = challengeCompleted.challengeEventId,
      state = ChallengeState.COMPLETED,
      gameId = challengeCompleted.gameId,
      completedAt = timeService.now(),
      createdAt = timeService.now(),
    ))
  }

  private fun sendChallengeCompletedNotification(challengeCompleted: ChallengeCompletedEffect, locale: Locale) {
    val userId = challengeCompleted.userId
    when (challengeCompleted.challengeType) {
      ChallengeType.REGULAR -> messageBus.publishAsync(PushNotificationEffect(ChallengeCompletedNotification(userId, locale)))
      ChallengeType.SPECIAL -> messageBus.publishAsync( PushNotificationEffect(SpecialChallengeCompletedNotification(userId, locale)))
    }
  }

  private suspend fun sendKeepDoingChallengesNotification(userId: String, locale: Locale) {
    val challengesCompleted = challengeService.getUserChallenges(userId)
        .filter { it.challenge.challengeType == ChallengeType.REGULAR }
        .count { it.state.isFinal() }

    val notification = when (challengesCompleted) {
      1 -> KeepDoingChallengesNotification(
        userId,
        title = userTranslationService.translateOrDefault(CHALLENGES_1ST_COMPLETED_NOTIFICATION_TITLE, locale, userId),
        body = userTranslationService.translateOrDefault(CHALLENGES_1ST_COMPLETED_NOTIFICATION_BODY, locale, userId),
      )

      3 -> KeepDoingChallengesNotification(
        userId,
        title = userTranslationService.translateOrDefault(CHALLENGES_3RD_COMPLETED_NOTIFICATION_TITLE, locale, userId),
        body = userTranslationService.translateOrDefault(CHALLENGES_3RD_COMPLETED_NOTIFICATION_BODY, locale, userId),
      )

      4 -> KeepDoingChallengesNotification(
        userId,
        title = userTranslationService.translateOrDefault(CHALLENGES_4TH_COMPLETED_NOTIFICATION_TITLE, locale, userId),
        body = userTranslationService.translateOrDefault(CHALLENGES_4TH_COMPLETED_NOTIFICATION_BODY, locale, userId),
      )

      else -> null // logic was made when we normally had 5 challenges, could be rethought if there are more now.
    }

    notification?.let { messageBus.publishAsync(PushNotificationEffect(it)) }
  }

  private suspend fun sendAmplitudeEvent(challengeCompleted: ChallengeCompletedEffect, appPlatform: AppPlatform) {
    val applicationId = gamesService.getGames()[challengeCompleted.gameId]?.applicationId ?: return

    messageBus.publish(
      UserChallengeCompletedEffect(
        userId = challengeCompleted.userId,
        appPlatform = appPlatform,
        challengeEventId = challengeCompleted.challengeEventId,
        applicationId = applicationId,
      )
    )
  }

  data class ChallengeCompletedEffect(
    val userId: String,
    val challengeId: ChallengeId,
    val challengeEventId: ChallengeEventId,
    val gameId: Int,
    val challengeType: ChallengeType,
  ) : AsyncEffect
}