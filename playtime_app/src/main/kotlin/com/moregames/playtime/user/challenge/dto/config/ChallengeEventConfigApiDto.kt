package com.moregames.playtime.user.challenge.dto.config

import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class ChallengeEventConfigApiDto(
  val startTime: Long,
  val endTime: Long,
  /**
   * It's required to display text at the bottom, Android
   * team decided that it's better to use separate field from "endTime" for this text. For now, it equals endTime.
   * https://justplayapps.slack.com/archives/C03FPUVJCRX/p1733923306696969?thread_ts=1733482001.523389&cid=C03FPUVJCRX
   */
  val challengesUpdateTime: Long,
  val timestamp: Long,
  @Contextual
  val challengeEventId: ChallengeEventId,
  val challengesUpdateText: String,
  val claimWidget: ClaimWidgetApiDto,
  val tutorialSteps: List<String>,
  val challenges: List<ChallengeApiDto>,
  val specialChallengePotState: SpecialChallengePotState?,
  val specialChallengePotProgressCurrent: Int?,
  val specialChallengePotProgressMax: Int?,
  val specialChallengeWidgets: SpecialChallengeWidgetsDto?,
  val tutorialWidget: TutorialWidgetDto?,
  val bonusTracker: BonusTrackerApiDto?,
) : IChallengeEventConfigApiDto
