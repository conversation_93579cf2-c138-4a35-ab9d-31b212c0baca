package com.moregames.playtime.user.challenge

import com.google.inject.Inject
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.TimeService
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.general.exception.InvalidParameterValueException
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventsAdminApiDto
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.config.*
import com.moregames.playtime.user.offer.AndroidGameOffer
import com.moregames.playtime.user.offer.AndroidInstallationLinkProvider
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.Instant
import java.util.*
import javax.inject.Singleton

@Singleton
class ChallengeEventConfigService @Inject constructor(
  private val timeService: TimeService,
  private val json: Json,
  private val gamePersistenceService: GamePersistenceService,
  private val imageService: ImageService,
  private val androidInstallationLinkProvider: AndroidInstallationLinkProvider,
  private val userTranslationService: UserTranslationService,
  private val challengeService: ChallengeService,
  private val challengeConfigValidator: ChallengeConfigValidator,
  private val specialChallengePotService: SpecialChallengePotService,
) {

  suspend fun getChallengeEvent(userId: String, locale: Locale): IChallengeEventConfigApiDto {
    val challengeEvent = challengeService.getCurrentChallengeEvent(userId)
    if (challengeEvent == null) {
      logger().debug("No challenge event for $userId")
      return EmptyChallengeEventConfigApiDto
    }
    val challengeCfg = try {
      json.decodeFromString<ChallengeEventJsonConfigDto>(challengeEvent.cfg)
    } catch (exception: Exception) {
      logger().error("Unable to parse challenge event config id = ${challengeEvent.id}", exception)
      return EmptyChallengeEventConfigApiDto
    }
    val claimWidget = buildClaimWidget(challengeCfg.claimWidget, challengeEvent, userId, locale)
    val challenges = getChallenges(userId, locale)
    val userSpecialPot = specialChallengePotService.findUserSpecialPot(userId)
    val specialChallengeWidgets = createSpecialChallengeWidgets(userSpecialPot, locale)
    val bonusTracker = buildBonusTracker(challengeCfg, userId, locale)
    return ChallengeEventConfigApiDto(
      startTime = challengeEvent.dateFrom.epochSecond,
      endTime = challengeEvent.dateTo.epochSecond,
      challengesUpdateTime = challengeEvent.dateTo.epochSecond,
      timestamp = timeService.now().epochSecond,
      challengeEventId = challengeEvent.id,
      tutorialSteps = challengeCfg.tutorialSteps,
      challengesUpdateText = challengeCfg.challengesUpdateText.translate(locale, userId),
      claimWidget = claimWidget,
      challenges = challenges,
      specialChallengePotState = userSpecialPot?.state,
      specialChallengePotProgressCurrent = userSpecialPot?.progress,
      specialChallengePotProgressMax = userSpecialPot?.potConfig?.progressMax,
      specialChallengeWidgets = specialChallengeWidgets,
      tutorialWidget = challengeCfg.tutorialWidget,
      bonusTracker = bonusTracker,
    )
  }

  private suspend fun buildBonusTracker(
    challengeCfg: ChallengeEventJsonConfigDto,
    userId: String,
    locale: Locale
  ): BonusTrackerApiDto? {
    val bonusTrackerDto = challengeCfg.bonusTracker
    if (bonusTrackerDto == null) return null
    val completeText = bonusTrackerDto.completeText?.translate(locale, userId)
    val bonusId = bonusTrackerDto.bonusId
    val progressCurrent = challengeService.countClaimedBonusEvent(userId = userId, bonusId = bonusId).toInt()
    return BonusTrackerApiDto(
      bonusId = bonusTrackerDto.bonusId,
      progressMax = bonusTrackerDto.progressMax,
      completeText = completeText,
      progressCurrent = progressCurrent,
    )
  }

  suspend fun createSpecialChallengeWidgets(userSpecialPot: UserSpecialChallengePot?, locale: Locale): SpecialChallengeWidgetsDto? {
    if (userSpecialPot == null) return null
    val widgets = try {
      json.decodeFromString<SpecialChallengeWidgetsDto>(userSpecialPot.potConfig.uiConfig)
    } catch (exception: Exception) {
      logger().error("Unable to parse special pot config = ${userSpecialPot.potConfig}", exception)
      return null
    }
    val userId = userSpecialPot.userId
    val mainScreen = widgets.mainScreen.map {
      SpecialChallengeWidgetsDto.MenuItemDto(
        challengeType = it.challengeType,
        title = it.title.translate(locale, userId),
        imageUrl = imageService.toUrl(it.imageUrl),
      )
    }
    val specialChallengeScreen = SpecialChallengeWidgetsDto.SpecialChallengeScreenDto(
      treasureImageUrl = imageService.toUrl(widgets.specialChallengeScreen.treasureImageUrl),
    )
    val claimWidget = SpecialChallengeWidgetsDto.ClaimWidgetDto(
      title = widgets.claimWidget.title.translate(locale, userId),
      description = widgets.claimWidget.description.translate(locale, userId),
      image = imageService.toUrl(widgets.claimWidget.image)
    )
    return SpecialChallengeWidgetsDto(
      mainScreen = mainScreen,
      specialChallengeScreen = specialChallengeScreen,
      claimWidget = claimWidget,
    )
  }

  suspend fun getChallengeEventList(dateFrom: Instant, dateTo: Instant, enabled: Boolean?): ChallengeEventsAdminApiDto {
    val events = challengeService.getChallengeEventList(dateFrom, dateTo, enabled)
    return events.map { event ->
      val challengeCfg = buildChallengeEventCfgFromJson(event)
      ChallengeEventAdminApiDto(
        id = event.id.value,
        dateFrom = event.dateFrom,
        dateTo = event.dateTo,
        enabled = event.enabled,
        claimWidget = challengeCfg.claimWidget,
        challengesUpdateText = challengeCfg.challengesUpdateText,
        tutorialSteps = challengeCfg.tutorialSteps,
        eventType = event.eventType,
        challenges = event.challenges.map {
          it.toChallengeAdminApiDto(gamePersistenceService.loadAndroidGamesByIds(setOf(it.gameId)).first().applicationId)
        },
        tutorialWidget = challengeCfg.tutorialWidget,
        bonusTracker = challengeCfg.bonusTracker,
      )
    }.let {
      ChallengeEventsAdminApiDto(it)
    }
  }

  suspend fun getChallengeEvent(eventId: String): ChallengeEventAdminApiDto {
    val event = challengeService.getChallengeEventById(ChallengeEventId(eventId))
    if (event != null) {
      val challengeCfg = buildChallengeEventCfgFromJson(event)
      return ChallengeEventAdminApiDto(
        id = event.id.value,
        dateFrom = event.dateFrom,
        dateTo = event.dateTo,
        enabled = event.enabled,
        claimWidget = challengeCfg.claimWidget,
        challengesUpdateText = challengeCfg.challengesUpdateText,
        tutorialSteps = challengeCfg.tutorialSteps,
        eventType = event.eventType,
        challenges = event.challenges.map {
          it.toChallengeAdminApiDto(gamePersistenceService.loadAndroidGamesByIds(setOf(it.gameId)).first().applicationId)
        },
        tutorialWidget = challengeCfg.tutorialWidget,
        bonusTracker = challengeCfg.bonusTracker,
      )
    } else {
      throw IllegalArgumentException("Unknown event with id $eventId")
    }
  }

  suspend fun createChallengeEvent(event: ChallengeEventAdminApiDto) {
    challengeConfigValidator.validateChallengeEvent(event)
    val challengeEvent = event.toChallengeEvent()
    challengeService.createChallengeEvent(challengeEvent)
  }

  suspend fun updateChallengeEvent(event: ChallengeEventAdminApiDto) {
    challengeConfigValidator.validateChallengeEvent(event)
    val challengeEvent = event.toChallengeEvent()
    challengeService.updateChallengeEvent(challengeEvent)
  }

  private fun buildChallengeEventCfgFromJson(challengeEvent: ChallengeEvent): ChallengeEventJsonConfigDto =
    try {
      json.decodeFromString<ChallengeEventJsonConfigDto>(challengeEvent.cfg)
    } catch (exception: Exception) {
      logger().error("Unable to parse challenge event config id = ${challengeEvent.id}", exception)
      throw IllegalStateException("Unable to parse challenge event config id = ${challengeEvent.id}", exception)
    }

  private suspend fun buildClaimWidget(
    claimWidgetDto: ClaimWidgetDto,
    challengeEventDto: ChallengeEvent,
    userId: String,
    locale: Locale,
  ): ClaimWidgetApiDto {
    val eventRewardClaimed = challengeService.getUserChallengeEvent(userId, challengeEventDto.id)
      ?.state == ChallengeEventState.CLAIMED
    return ClaimWidgetApiDto(
      bannerColor = claimWidgetDto.bannerColor,
      bannerEndColor = claimWidgetDto.bannerEndColor,
      textColor = claimWidgetDto.textColor,
      headerText = claimWidgetDto.headerText.translate(locale, userId),
      progressBarSubtext = claimWidgetDto.progressBarSubtext.translate(locale, userId),
      eventRewardClaimed = eventRewardClaimed,
      claimButtonText = claimWidgetDto.claimButtonText.translateNullableDefault(locale, userId, TranslationResource.CHALLENGE_CLAIM_BUTTON_TEXT),
      aheadButtonText = claimWidgetDto.aheadButtonText.translateNullableDefault(locale, userId, TranslationResource.CHALLENGE_AHEAD_BUTTON_TEXT),
    )
  }

  private suspend fun String?.translateNullableDefault(locale: Locale, userId: String, resource: TranslationResource): String =
    this?.let { userTranslationService.tryTranslate(it,locale, userId) }
      ?: userTranslationService.translateOrDefault(resource, locale, userId)


  private suspend fun String.translate(locale: Locale, userId: String): String =
     userTranslationService.tryTranslate(this, locale, userId)


  private suspend fun getChallenges(userId: String, locale: Locale) =
    challengeService.getUserChallenges(userId)
      .sortByState()
      .mapNotNull { toChallengeApiDto(it, locale) }


  private suspend fun toChallengeApiDto(userChallenge: UserChallenge, locale: Locale): ChallengeApiDto? {
    val gameId = userChallenge.challenge.gameId
    val androidGame = gamePersistenceService.loadAndroidGamesByIds(setOf(gameId))
      .map { it.toChallengeOfferApiDto(userChallenge.userId, locale) }
      .firstOrNull()
    if (androidGame == null) {
      logger().alert("Not found game by id: $gameId")
      return null
    }
    return ChallengeApiDto(
      challengeId = userChallenge.challenge.id,
      title = userChallenge.challenge.title.translate(locale, userChallenge.userId).format(userChallenge.challenge.progressMax),
      icon = imageService.toUrl(userChallenge.challenge.icon),
      progressMax = userChallenge.challenge.progressMax,
      progressCurrent = userChallenge.progress,
      rewardClaimed = userChallenge.state == ChallengeState.CLAIMED,
      gameInfo = androidGame,
      challengeType = userChallenge.challenge.challengeType
    )
  }

  private suspend fun ChallengeEventAdminApiDto.toChallengeEvent(): ChallengeEvent {
    return ChallengeEvent(
      id = ChallengeEventId(this.id),
      dateTo = this.dateTo,
      dateFrom = this.dateFrom,
      enabled = this.enabled,
      cfg = json.encodeToString(
        ChallengeEventJsonConfigDto(
          challengesUpdateText = this.challengesUpdateText,
          claimWidget = this.claimWidget,
          tutorialSteps = this.tutorialSteps,
          bonusTracker = this.bonusTracker,
          tutorialWidget = this.tutorialWidget,
        )
      ),
      eventType = this.eventType,
      bonusId = this.bonusTracker?.bonusId,
      challenges =  this.challenges.map { challenge ->
      val gameId = gamePersistenceService.getGameIdByApplicationId(challenge.gameApplicationId, AppPlatform.ANDROID)
      if (gameId == null) {
        throw InvalidParameterValueException("Not found gameId by applicationId = ${challenge.gameApplicationId}")
      }
      Challenge(
        id = ChallengeId(challenge.id),
        eventId = ChallengeEventId(this.id),
        title = challenge.title,
        icon = challenge.icon,
        progressMax = challenge.progressMax,
        gameId = gameId,
        calculator = challenge.calculator,
        order = challenge.order,
        goal = challenge.goal,
        applyEarningsCut = challenge.applyEarningsCut,
        challengeType = challenge.challengeType,
      )
    })
  }

  private suspend fun AndroidGameOffer.toChallengeOfferApiDto(userId: String, locale: Locale): ChallengeOfferApiDto =
    ChallengeOfferApiDto(
      id = this.id.toString(),
      applicationId = this.applicationId,
      activityName = this.activityName,
      title = this.name.translate(locale, userId),
      imageUrl = imageService.toUrl(this.imageFilename),
      iconUrl = imageService.toUrl(this.iconFilename),
      isEnabled = true,
      installImageUrl = imageService.toUrl(this.installImageFilename),
      infoTextInstallTop = this.infoTextInstallTop.translate(locale, userId),
      infoTextInstallBottom = this.infoTextInstallBottom.translate(locale, userId),
      showInstallImage = this.showInstallImage,
      installationLink = androidInstallationLinkProvider.provide(this.applicationId, userId),
    )

  private fun List<UserChallenge>.sortByState(): List<UserChallenge> {
    val byStatus = this.groupBy { it.state }

    val completed = byStatus[ChallengeState.COMPLETED]?.sortedWith(compareBy({ it.challenge.order }, { it.challenge.id.value })) ?: emptyList()
    val inProgress = byStatus[ChallengeState.IN_PROGRESS]?.sortedByDescending { it.updatedAt } ?: emptyList()
    val notStarted = byStatus[ChallengeState.NOT_STARTED]?.sortedWith(compareBy({ it.challenge.order }, { it.challenge.id.value })) ?: emptyList()
    val rest = (this - (completed + inProgress + notStarted).toSet()).sortedWith(compareBy({ it.challenge.order }, { it.challenge.id.value }))

    return (completed + inProgress + notStarted + rest)
  }
}