package com.moregames.playtime.user.challenge

import com.google.inject.Singleton
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.applovin.APPLOVIN_BANNER_REVENUE_ALIGNER
import com.moregames.base.bus.MessageBus
import com.moregames.base.user.FakeMetaEarnings
import com.moregames.base.util.TimeService
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.RewardEarningsAddedEventDto
import com.moregames.playtime.user.challenge.dto.UserSpecialChallengePot
import java.math.BigDecimal
import javax.inject.Inject

@Singleton
class SpecialChallengeRewardingService @Inject constructor(
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val cashoutService: CashoutService,
  private val cashoutSettingsService: CashoutSettingsService,
  private val cashoutStatusService: CashoutStatusService,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
  private val challengeRevenueService: ChallengeRevenueService,
) {

  companion object {
    private val MIN_EVENT_REWARD = BigDecimal("0.01")
  }


  suspend fun calculateChallengeEventRewardUsd(pot: UserSpecialChallengePot): BigDecimal {
    val previousUnpaidUserEarnings =
      cashoutService.getNonCashedUserCurrencyEarnings(pot.userId).amountUsd
    val userMaxEarnings = cashoutSettingsService.getUserMaxEarningsAmount(pot.userId)
    val maxEarningsLimit = (userMaxEarnings - previousUnpaidUserEarnings).max(BigDecimal.ZERO)

    return (pot.applovinNonBannerRevenue)
      .multiply(APPLOVIN_BANNER_REVENUE_ALIGNER) // as only inters are at hands
      .multiply(challengeRevenueService.specialChallengeRevenueShare())
      .max(MIN_EVENT_REWARD)
      .min(maxEarningsLimit)
  }

  /**
   * @return false - when reward was already added to DB. true - otherwise.
   */
  suspend fun giveChallengeEventReward(pot: UserSpecialChallengePot, reward: CurrencyExchangeResultDto): Boolean {
    val rewardAdded = userEarningsPersistenceService.addSpecialChallengeReward(
      pot.id,
      pot.userId,
      rewardUsd = reward.usdAmount,
      currency = reward.userCurrency,
      reward = reward.amount,
    )

    if (rewardAdded) {
      cashoutStatusService.enableCashout(pot.userId)
      emitRewardEarningsAdded(pot, reward)
    }

    return rewardAdded
  }

  private suspend fun emitRewardEarningsAdded(pot: UserSpecialChallengePot, reward: CurrencyExchangeResultDto) {
    val earningsAddedEvent = RewardEarningsAddedEventDto(
      metaId = FakeMetaEarnings.FAKE_META_ID_CHALLENGE_REWARD.id,
      userId = pot.userId,
      amount = reward.usdAmount,
      amountUserCurrency = reward.amount,
      userCurrencyCode = reward.userCurrency.currencyCode,
      challengeEventId = pot.id,
      createdAt = timeService.now(),
      challengeType = ChallengeType.SPECIAL,
    )

    messageBus.publish(earningsAddedEvent)
  }
}