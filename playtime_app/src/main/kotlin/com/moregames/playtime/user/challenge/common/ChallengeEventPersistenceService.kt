package com.moregames.playtime.user.challenge.common

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.PackagePrivate
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.Challenge
import com.moregames.playtime.user.challenge.dto.ChallengeEvent
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.ChallengeEventState
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.dto.ChallengeState
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.dto.UserChallengeEvent
import com.moregames.playtime.user.challenge.table.ChallengeEventTable
import com.moregames.playtime.user.challenge.table.ChallengeTable
import com.moregames.playtime.user.challenge.table.UserChallengeEventTable
import com.moregames.playtime.user.challenge.table.UserChallengeTable
import com.moregames.playtime.util.minus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import org.jetbrains.exposed.sql.CustomFunction
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.decimalLiteral
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import java.math.BigDecimal
import java.time.Instant
import kotlin.time.Duration.Companion.days

/**
 * Try to use ChallengeService
 */
@PackagePrivate
class ChallengeEventPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService,
) : BasePersistenceService(database) {

  private companion object {
    private val eventCompletedStates = setOf(ChallengeEventState.COMPLETED, ChallengeEventState.CLAIMED)
  }

  suspend fun startUserChallenge(challengeId: ChallengeId, userId: String) = dbQuery {
    UserChallengeTable.insertIgnore {
      it[UserChallengeTable.challengeId] = challengeId.value
      it[UserChallengeTable.userId] = userId
      it[state] = ChallengeState.IN_PROGRESS
      it[coins] = 0
      it[progress] = 0
    }.insertedCount == 1
  }

  suspend fun startUserEvent(eventId: ChallengeEventId, userId: String) = dbQuery {
    UserChallengeEventTable.insertIgnore {
      it[UserChallengeEventTable.eventId] = eventId.value
      it[UserChallengeEventTable.userId] = userId
      it[state] = ChallengeEventState.IN_PROGRESS
      it[earnings] = BigDecimal(0)
    }.insertedCount == 1
  }

  suspend fun completeEvent(eventId: ChallengeEventId, userId: String): Boolean = dbQuery {
    UserChallengeEventTable.update(
      where = {
        (UserChallengeEventTable.eventId eq eventId.value) and
          (UserChallengeEventTable.userId eq userId) and
          (UserChallengeEventTable.state eq ChallengeEventState.IN_PROGRESS)
      },
    ) {
      it[state] = ChallengeEventState.COMPLETED
    } == 1
  }

  //uses by Qa team
  suspend fun forceCompleteUserChallenge(challengeId: String, userId: String): Boolean = dbQuery {
    val progressMax = ChallengeTable
      .slice(ChallengeTable.progressMax)
      .select {
        ChallengeTable.id eq challengeId
      }
    UserChallengeTable.update(
      where = {
        (UserChallengeTable.challengeId eq challengeId) and
          (UserChallengeTable.userId eq userId) and
          (UserChallengeTable.state eq ChallengeState.IN_PROGRESS)
      },
    ) {
      it[state] = ChallengeState.COMPLETED
      it[progress] = progressMax
    } == 1
  }

  suspend fun getUserChallenge(challengeId: ChallengeId, userId: String): UserChallenge? = dbQuery {
      userChallengeQuery(userId)
      .select { (UserChallengeTable.challengeId eq challengeId.value) and (UserChallengeTable.userId eq userId) }
      .map { it.toUserChallenge(userId) }
      .singleOrNull()
  }

  private fun ResultRow.toUserChallenge(userId: String): UserChallenge =
    UserChallenge(
      challenge = Challenge(
        id = ChallengeId(this[ChallengeTable.id]),
        eventId = ChallengeEventId(this[ChallengeTable.eventId]),
        title = this[ChallengeTable.title],
        icon = this[ChallengeTable.icon],
        progressMax = this[ChallengeTable.progressMax],
        gameId = this[ChallengeTable.gameId],
        calculator = this[ChallengeTable.calculator],
        order = this[ChallengeTable.order],
        goal = this[ChallengeTable.goal],
        applyEarningsCut = this[ChallengeTable.applyEarningsCut],
        challengeType = this[ChallengeTable.challengeType],
      ),
      userId = userId,
      progress = getProgress(this),
      state = getChallengeState(this),
      coins = getCoins(this),
      achievement = this.getOrNull(UserChallengeTable.achievement),
      completedAt = this.getOrNull(UserChallengeTable.completedAt),
      updatedAt = this.getOrNull(UserChallengeTable.updatedAt),
    )

  suspend fun getUserChallengeEvent(userId: String, eventId: ChallengeEventId): UserChallengeEvent? = dbQuery {
    UserChallengeEventTable
      .slice(
        UserChallengeEventTable.state,
        UserChallengeEventTable.earnings,
        UserChallengeEventTable.createdAt,
        UserChallengeEventTable.applovinNonBannerRev,
      )
      .select { (UserChallengeEventTable.eventId eq eventId.value) and (UserChallengeEventTable.userId eq userId) }
      .map {
        UserChallengeEvent(
          userId = userId,
          eventId = eventId,
          state = it[UserChallengeEventTable.state],
          applovinNonBannerRevenue = it[UserChallengeEventTable.applovinNonBannerRev],
          earnings = it[UserChallengeEventTable.earnings],
          startedAt = it[UserChallengeEventTable.createdAt]
        )
      }.firstOrNull()
  }

  suspend fun loadChallengeEvent(atDateTime: Instant): ChallengeEvent? = dbQuery {
    val events = challengeEventQuery()
      .select {
        (ChallengeEventTable.dateTo greater atDateTime) and
          (ChallengeEventTable.dateFrom lessEq atDateTime) and
          (ChallengeEventTable.enabled eq true) and
          (ChallengeEventTable.eventType eq ChallengeEventType.GLOBAL)
      }
      .toChallengeEvent()
    if (events.size > 1) {
      logger().warn("More than one challenge event was found")
    }
    events.minByOrNull { it.dateFrom }
  }

  suspend fun countClaimedBonusEvent(userId: String, bonusId: String): Long = dbQuery {
    ChallengeEventTable
      .join(
        otherTable = UserChallengeEventTable,
        otherColumn = UserChallengeEventTable.eventId,
        onColumn = ChallengeEventTable.id,
        joinType = JoinType.INNER,
      )
      .select { (UserChallengeEventTable.userId eq userId) and
        (ChallengeEventTable.bonusId eq bonusId) and
        (UserChallengeEventTable.state eq ChallengeEventState.CLAIMED) }
      .count()
  }

  suspend fun loadChallengeEventById(id: ChallengeEventId): ChallengeEvent? =  dbQuery {
    challengeEventQuery()
      .select {
          (ChallengeEventTable.id eq id.value) and
          (ChallengeEventTable.enabled eq true)
      }
      .toChallengeEvent()
      .firstOrNull()
  }

  suspend fun getUserChallenges(eventId: ChallengeEventId, userId: String) = dbQuery {
      userChallengeQuery(userId)
      .select { ChallengeTable.eventId eq eventId.value }
      .map { it.toUserChallenge(userId)}
  }

  private fun userChallengeQuery(userId: String) =
    ChallengeTable
    .join(
      otherTable = UserChallengeTable,
      joinType = JoinType.LEFT,
      onColumn = ChallengeTable.id,
      otherColumn = UserChallengeTable.challengeId,
      additionalConstraint = {
        UserChallengeTable.userId eq userId
      }
    )
    .slice(
      ChallengeTable.id,
      ChallengeTable.eventId,
      ChallengeTable.progressMax,
      ChallengeTable.gameId,
      ChallengeTable.title,
      ChallengeTable.calculator,
      ChallengeTable.icon,
      ChallengeTable.order,
      ChallengeTable.goal,
      ChallengeTable.applyEarningsCut,
      ChallengeTable.challengeType,
      UserChallengeTable.state,
      UserChallengeTable.achievement,
      UserChallengeTable.progress,
      UserChallengeTable.coins,
      UserChallengeTable.completedAt,
      UserChallengeTable.updatedAt,
      UserChallengeTable.userId,
    )

  suspend fun getChallengeEventList(dateFrom: Instant, dateTo: Instant, enabled: Boolean?) = dbQuery {
    val events = challengeEventQuery().select {
        (ChallengeEventTable.dateTo greaterEq dateFrom) and
          (ChallengeEventTable.dateFrom lessEq dateTo)
      }
    if (enabled != null) {
      events.andWhere {
        (ChallengeEventTable.enabled eq enabled)
      }
    }
    events.orderBy(ChallengeEventTable.dateFrom to SortOrder.DESC, ChallengeTable.order to SortOrder.ASC)
    events.toChallengeEvent()
  }

  private fun challengeEventQuery() = ChallengeEventTable
    .join(
      otherTable = ChallengeTable,
      joinType = JoinType.LEFT,
      onColumn = ChallengeEventTable.id,
      otherColumn = ChallengeTable.eventId
    ).slice(
      ChallengeEventTable.id,
      ChallengeEventTable.dateFrom,
      ChallengeEventTable.dateTo,
      ChallengeEventTable.cfg,
      ChallengeEventTable.enabled,
      ChallengeEventTable.eventType,
      ChallengeEventTable.bonusId,
      ChallengeTable.id,
      ChallengeTable.eventId,
      ChallengeTable.gameId,
      ChallengeTable.icon,
      ChallengeTable.goal,
      ChallengeTable.applyEarningsCut,
      ChallengeTable.order,
      ChallengeTable.calculator,
      ChallengeTable.progressMax,
      ChallengeTable.title,
      ChallengeTable.challengeType,
    )


  suspend fun updateChallengeEvent(event: ChallengeEvent) {
    dbQuery {
      ChallengeEventTable.update(
        { ChallengeEventTable.id eq event.id.value}
      ) {
        it[id] = event.id.value
        it[dateFrom] = event.dateFrom
        it[dateTo] = event.dateTo
        it[enabled] = event.enabled
        it[cfg] = event.cfg
        it[eventType] = event.eventType
        it[bonusId] = event.bonusId
      }

      event.challenges.forEach { challenge ->
        ChallengeTable.update(
          { ChallengeTable.id eq challenge.id.value}
        ){
          it[id] = challenge.id.value
          it[eventId] = challenge.eventId.value
          it[title] = challenge.title
          it[icon] = challenge.icon
          it[progressMax] = challenge.progressMax
          it[gameId] = challenge.gameId
          it[calculator] = challenge.calculator
          it[order] = challenge.order
          it[goal] = challenge.goal
          it[applyEarningsCut] = challenge.applyEarningsCut
          it[challengeType] = challenge.challengeType
        }
      }
    }
  }

  suspend fun createChallengeEvent(event: ChallengeEvent) {
    dbQuery {
      ChallengeEventTable.insert {
        it[id] = event.id.value
        it[dateFrom] = event.dateFrom
        it[dateTo] = event.dateTo
        it[enabled] = event.enabled
        it[cfg] = event.cfg
        it[eventType] = ChallengeEventType.GLOBAL
      }

      event.challenges.forEach { challenge ->
        ChallengeTable.insert {
          it[id] = challenge.id.value
          it[eventId] = challenge.eventId.value
          it[title] = challenge.title
          it[icon] = challenge.icon
          it[progressMax] = challenge.progressMax
          it[gameId] = challenge.gameId
          it[calculator] = challenge.calculator
          it[order] = challenge.order
          it[goal] = challenge.goal
          it[applyEarningsCut] = challenge.applyEarningsCut
          it[challengeType] = challenge.challengeType
        }
      }
    }
  }

  private fun Iterable<ResultRow>.toChallengeEvent(): List<ChallengeEvent> =
    this.groupBy { it[ChallengeEventTable.id] }
      .map { (_, rows) ->
        ChallengeEvent(
            id = ChallengeEventId(rows[0][ChallengeEventTable.id]),
            dateFrom = rows[0][ChallengeEventTable.dateFrom],
            dateTo = rows[0][ChallengeEventTable.dateTo],
            cfg = rows[0][ChallengeEventTable.cfg],
            enabled = rows[0][ChallengeEventTable.enabled],
            eventType = rows[0][ChallengeEventTable.eventType],
            bonusId = rows[0][ChallengeEventTable.bonusId],
            challenges = rows
              .filter { it.getOrNull(ChallengeTable.id) != null }
              .map {
              Challenge(
                id = ChallengeId(it[ChallengeTable.id]),
                eventId = ChallengeEventId(it[ChallengeTable.eventId]),
                title = it[ChallengeTable.title],
                icon = it[ChallengeTable.icon],
                progressMax = it[ChallengeTable.progressMax],
                gameId = it[ChallengeTable.gameId],
                calculator = it[ChallengeTable.calculator],
                order = it[ChallengeTable.order],
                goal = it[ChallengeTable.goal],
                applyEarningsCut = it[ChallengeTable.applyEarningsCut],
                challengeType = it[ChallengeTable.challengeType],
              )
            }
          )
        }

  private fun getCoins(resultRow: ResultRow) = resultRow.getOrNull(UserChallengeTable.coins) ?: 0

  private fun getChallengeState(resultRow: ResultRow) =
    resultRow.getOrNull(UserChallengeTable.state) ?: ChallengeState.NOT_STARTED

  private fun getProgress(resultRow: ResultRow) = resultRow.getOrNull(UserChallengeTable.progress) ?: 0

  suspend fun claimChallenge(challengeId: ChallengeId, userId: String, coins: Int) = dbQuery {
    UserChallengeTable.update(
      where = {
        (UserChallengeTable.challengeId eq challengeId.value) and
          (UserChallengeTable.userId eq userId) and
          (UserChallengeTable.state eq ChallengeState.COMPLETED)
      },
    ) {
      it[state] = ChallengeState.CLAIMED
      it[UserChallengeTable.coins] = coins
    } == 1
  }

  suspend fun claimEvent(eventId: ChallengeEventId, userId: String, earnings: BigDecimal) = dbQuery {
    UserChallengeEventTable.update(
      where = {
        (UserChallengeEventTable.eventId eq eventId.value) and
          (UserChallengeEventTable.userId eq userId) and
          (UserChallengeEventTable.state eq ChallengeEventState.COMPLETED)
      },
    ) {
      it[state] = ChallengeEventState.CLAIMED
      it[UserChallengeEventTable.earnings] = earnings
    } == 1
  }

  suspend fun updateChallengeProgress(
    dto: UserChallenge,
    progress: ChallengeProgress,
    state: ChallengeState,
  ) = dbQuery {
    UserChallengeTable.update(
      where = {
        (UserChallengeTable.challengeId eq dto.challenge.id.value) and
          (UserChallengeTable.userId eq dto.userId) and
          (UserChallengeTable.state eq dto.state) and
          (UserChallengeTable.progress eq dto.progress)
      },
    ) {
      it[UserChallengeTable.state] = state
      it[UserChallengeTable.progress] = progress.progress
      it[achievement] = progress.achievement
      if (state == ChallengeState.COMPLETED) {
        it[completedAt] = timeService.now()
      }
    } == 1
  }

  suspend fun updateIncompleteChallengeEventRevenue(userId: String, eventId: ChallengeEventId, revenue: BigDecimal) = dbQuery {
    val nonNullRevenue = CustomFunction<BigDecimal>(
      functionName = "ifNull",
      _columnType = UserChallengeEventTable.applovinNonBannerRev.columnType,
      UserChallengeEventTable.applovinNonBannerRev,
      decimalLiteral(BigDecimal.ZERO)
    )

    UserChallengeEventTable.update({
      (UserChallengeEventTable.userId eq userId) and
        (UserChallengeEventTable.eventId eq eventId.value) and
        (UserChallengeEventTable.state notInList eventCompletedStates)
    }) {
      it.update(applovinNonBannerRev, nonNullRevenue + revenue)
    }
  }

  suspend fun userHadChallengesInLastDay(userId: String): Boolean = dbQuery {
    ChallengeEventTable
      .join(UserChallengeEventTable, JoinType.INNER, ChallengeEventTable.id, UserChallengeEventTable.eventId)
      .select {
        (UserChallengeEventTable.userId eq userId) and
          (ChallengeEventTable.dateTo greaterEq timeService.now().minus(1.days))
      }
      .count() > 0
  }
}