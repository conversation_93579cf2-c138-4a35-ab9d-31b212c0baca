package com.moregames.playtime.user.challenge

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.util.format
import com.moregames.base.util.logger
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import com.moregames.playtime.user.challenge.dto.claim.pot.ClaimPotResponseApiDto
import com.moregames.playtime.user.challenge.dto.config.SpecialChallengeWidgetsDto
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import java.util.*

@Singleton
class ClaimSpecialChallengePotService @Inject constructor(
  private val specialChallengeRewardingService: SpecialChallengeRewardingService,
  private val marketService: MarketService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val specialChallengePotService: SpecialChallengePotService,
  private val challengeEventConfigService: ChallengeEventConfigService,
  private val userService: UserService,
) {
  suspend fun claimPot(userId: String): ClaimPotResponseApiDto {
    val userCurrency = marketService.getUserCurrency(userId)

    val pot = specialChallengePotService.findUserSpecialPot(userId)
    if (pot == null || !pot.state.isFinal()) {
      return BigDecimal.ZERO.toClaimEventResponse(userCurrency, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    }
    val locale = userService.getUser(userId).locale
    val claimWidget = challengeEventConfigService.createSpecialChallengeWidgets(pot, locale)?.claimWidget
    if (pot.state == SpecialChallengePotState.CLAIMED) {
      logger().warn("Duplicate claim of pot $pot for user $userId")
      return pot.earnings.toClaimEventResponse(userCurrency, claimWidget)
    }

    val earnings = specialChallengeRewardingService.calculateChallengeEventRewardUsd(pot)
        .toUserCurrency(userCurrency)
    val rewarded = specialChallengeRewardingService.giveChallengeEventReward(pot, earnings)
    val claimed = (rewarded && specialChallengePotService.claimSpecialPot(pot.id, earnings.usdAmount))

    if (claimed) {
      return earnings.usdAmount.toClaimEventResponse(userCurrency, claimWidget)
    } else {
      logger().warn("Parallel claiming of event $pot for user $userId")
    }

    val updatedPot = specialChallengePotService.findUserSpecialPot(userId)
    if (updatedPot == null || updatedPot.state != SpecialChallengePotState.CLAIMED) {
      logger().warn("Invalid state of event $pot for user $userId")
      return BigDecimal.ZERO.toClaimEventResponse(currency = userCurrency, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    }

    return updatedPot.earnings.toClaimEventResponse(userCurrency, claimWidget)
  }

  private fun CurrencyExchangeResultDto.toAmountString(): String =
    this.amount.roundDownToSecondDigit().format(this.userCurrency)

  private suspend fun BigDecimal.toUserCurrency(currency: Currency): CurrencyExchangeResultDto =
    currencyExchangeService.convert(this, currency)

  private suspend fun BigDecimal.toClaimEventResponse(
    currency: Currency,
    claimWidget: SpecialChallengeWidgetsDto.ClaimWidgetDto? = null,
    errorMessage: String? = null,
  ): ClaimPotResponseApiDto {
    return ClaimPotResponseApiDto(
      amountString = this.toUserCurrency(currency).toAmountString(),
      title = claimWidget?.title,
      description = claimWidget?.description,
      image = claimWidget?.image,
      errorMessage = errorMessage,
      )
  }
}