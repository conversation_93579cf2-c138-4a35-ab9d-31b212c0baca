package com.moregames.playtime.user.challenge.dto.config

import com.moregames.playtime.user.challenge.dto.ChallengeType
import kotlinx.serialization.Serializable

@Serializable
data class SpecialChallengeWidgetsDto(
  val mainScreen: List<MenuItemDto>,
  val specialChallengeScreen: SpecialChallengeScreenDto,
  val claimWidget: ClaimWidgetDto,
) {

  @Serializable
  data class MenuItemDto(
    val challengeType: ChallengeType,
    val imageUrl: String,
    val title: String,
  )

  @Serializable
  data class SpecialChallengeScreenDto(
    val treasureImageUrl: String,
  )
  @Serializable
  data class ClaimWidgetDto(
    val title: String,
    val description: String,
    val image: String,
  )
}