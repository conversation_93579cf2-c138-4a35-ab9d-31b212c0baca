package com.moregames.playtime.user.gamerank

data class GameRankConfig(
  val oneStarProgressMax: Int,
  val twoStarProgressMax: Int,
  val threeStarProgressMax: Int,
  val calculator: GameRankCalculator,
) {
  val progressMax = threeStarProgressMax
}

object GameRankConfigHolder {
  val gamesConfig = mapOf(
    // solitaire
    200044 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.SOLITAIRE,
    ),
    // sugar rush
    200052 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // blockbuster
    200074 to GameRankConfig(
      oneStarProgressMax = 1000,
      twoStarProgressMax = 3000,
      threeStarProgressMax = 10000,
      calculator = GameRankCalculator.SCORE_PROGRESS,
    ),
    // pinmaster
    200075 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // bubblepop
    200057 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Mad Smash
    200041 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Word Seeker
    200046 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Hex Match
    200050 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Block Hole Clash
    200054 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Word Kitchen
    200061 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Water Sorter
    200062 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Spiral Drop
    200067 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Space Connect
    200069 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Bubble Chef
    200077 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Atlantis Bounce
    200078 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Slice Puzzle
    200086 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Slide & Roll
    200087 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // Merge Blast
    200048 to GameRankConfig(
      oneStarProgressMax = 5000,
      twoStarProgressMax = 15000,
      threeStarProgressMax = 50000,
      calculator = GameRankCalculator.SCORE_PROGRESS,
    ),
    // solitaire classic
    200079 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.SOLITAIRE,
    ),
  )
}