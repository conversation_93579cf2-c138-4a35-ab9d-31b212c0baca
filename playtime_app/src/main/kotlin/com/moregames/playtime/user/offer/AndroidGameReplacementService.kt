package com.moregames.playtime.user.offer

import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ExperimentBase
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.util.plus
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days

@Singleton
class AndroidGameReplacementService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val timeService: TimeService,
) {

  suspend fun replaceGames(user: UserDto, offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    return offers
      .withReplacedGame(
        user = user,
        showInExperiment = ClientExperiment.ANDROID_NEW_SOLITAIRE,
        showToAllUsersAfterFtue = 7.days,
        existingApplicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
        replacementApplicationId = ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID
      )
      .withReplacedGame(
        user = user,
        showInExperiment = ClientExperiment.BUBBLE_CHEF_VS_BUBBLE_POP,
        showToAllUsersAfterFtue = 7.days,
        existingApplicationId = ApplicationId.BUBBLE_POP_APP_ID,
        replacementApplicationId = ApplicationId.BUBBLE_CHIEF_APP_ID,
      )
  }

  private suspend fun List<AndroidGameOffer>.withReplacedGame(
    user: UserDto,
    showInExperiment: ExperimentBase,
    showToAllUsersAfterFtue: Duration,
    existingApplicationId: String,
    replacementApplicationId: String,
  ): List<AndroidGameOffer> {
    if ((user.createdAt + showToAllUsersAfterFtue).isAfter(timeService.now()) && !abTestingService.isUserExperimentParticipant(user.id, showInExperiment)) return this
    if (any { it.applicationId == replacementApplicationId }) return this
    val existingGame = find { it.applicationId == existingApplicationId } ?: return this

    val replacementGame = gamesService.getGameId(replacementApplicationId, AppPlatform.ANDROID)
      ?.let { gamePersistenceService.loadAndroidGamesByIds(setOf(it)) }
      ?.takeIf { it.isNotEmpty() }
      ?.map { it.copy(orderKey = existingGame.orderKey) }
      ?: run {
        logger().warn("Can't find game for $replacementApplicationId")
        return this@withReplacedGame
      }

    return filter { it.applicationId != existingApplicationId } + replacementGame
  }
}