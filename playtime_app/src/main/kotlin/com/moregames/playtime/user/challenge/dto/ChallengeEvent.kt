package com.moregames.playtime.user.challenge.dto

import java.time.Instant

data class ChallengeEvent(
  val id: ChallengeEventId,
  val dateFrom: Instant,
  val dateTo: Instant,
  val cfg: String,
  val enabled: Boolean,
  val challenges: List<Challenge>,
  val eventType: ChallengeEventType,
  val bonusId: String?,
) {
  companion object {
    fun empty() =
      ChallengeEvent(
        id = ChallengeEventId("EMPTY"),
        dateFrom = Instant.EPOCH,
        dateTo = Instant.EPOCH,
        cfg = "{}",
        enabled = true,
        challenges = emptyList(),
        eventType = ChallengeEventType.GLOBAL,
        bonusId = null
      )
  }
}