package com.moregames.playtime.user.verification

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.exceptions.GpsVerificationFailedException
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.dto.UserFaceVerifiedEventDto
import com.moregames.base.util.ClientVersionsSupport.IOS_BIPA_NON_RESTRICTED_MIN_APP_VERSION
import com.moregames.base.util.TimeService
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.administration.user.FacetecFacesService
import com.moregames.playtime.app.forbidIosBipaCashoutWithFaceVerification
import com.moregames.playtime.buseffects.SendLocationCheckedEventEffect
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.ios.dto.IosExaminationEnv
import com.moregames.playtime.ios.examination.IosExaminationService
import com.moregames.playtime.ios.examination.dto.IosExaminationRequestApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.translations.TranslationResource.EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT
import com.moregames.playtime.translations.TranslationResource.EXCEPTION_UNIQUENESS
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.EmailValidationService
import com.moregames.playtime.user.UserCheckManager
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.exception.VerificationIncompleteException
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.verification.dto.*
import com.moregames.playtime.user.verification.dto.AssociatedUsersCheckResult.PASSED
import com.moregames.playtime.user.verification.dto.VerificationResultType.*
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus.REQUIRED
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus.VERIFIED
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStep
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationType.*
import com.moregames.playtime.user.verification.email.SeonService
import com.moregames.playtime.user.verification.exception.*
import com.moregames.playtime.util.buildFacetecId
import com.moregames.playtime.web.WebUserService
import com.moregames.playtime.webhook.adjust.AdjustEventPersistenceService
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton

@Singleton
class VerificationService @Inject constructor(
  private val verificationPersistenceService: VerificationPersistenceService,
  private val facetecClient: FacetecClient,
  private val userService: UserService,
  private val timeService: TimeService,
  private val userCheckManager: UserCheckManager,
  private val fraudScoreService: FraudScoreService,
  private val translationService: UserTranslationService,
  private val facetecFacesService: FacetecFacesService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val cashoutService: CashoutService,
  private val iosExaminationService: IosExaminationService,
  private val marketService: MarketService,
  private val abTestingService: AbTestingService,
  private val rewardingFacade: RewardingFacade,
  private val seonService: SeonService,
  private val buildVariant: BuildVariant,
  private val faceFraudstersCounter: FaceFraudstersCounter,
  private val faceFraudstersPersistenceService: FaceFraudstersPersistenceService,
  private val associatedUsersService: AssociatedUsersService,
  private val hashService: HashService,
  private val messageBus: MessageBus,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val ipService: IpService,
  private val encryptionService: EncryptionService,
  private val emailValidationService: EmailValidationService,
  private val webUserService: WebUserService,
  private val adjustEventPersistenceService: AdjustEventPersistenceService,
) {
  companion object {
    const val SESSION_EXPIRATION_MINUTES = 10L
    const val FACE_MAP_TIME_TO_LIVE_DAYS = 120L
    const val FACE_PICTURE_AUDIT_TIME_TO_LIVE_DAYS = 30L
    const val UNTRUSTED_DEVICES_UA_NETWORK = "untrusted devices"
  }

  suspend fun initiateVerification(
    userId: String,
    provider: PaymentProviderType? = null,
    email: String? = null,
    userIp: String?,
    appVersion: AppVersionDto,
  ): VerificationSessionDto {
    if (!userService.userExists(userId)) {
      throw UserRecordNotFoundException(userId)
    }
    findStubbedSession(userId)?.let { (sessionId, expiredAt) ->
      return VerificationSessionDto.fromStub(userId, sessionId, expiredAt)
    }
    val encryptedEmail = encryptionService.encrypt(email.orEmpty())
    val emailHash = hashService.emailSha256(email.orEmpty())
    val normalizedEmail = emailValidationService.normalize(email.orEmpty())
    val normalizedEmailHash = hashService.emailSha256(normalizedEmail)
    val normalizedEncryptedEmail = encryptionService.encrypt(normalizedEmail) ?: ""
    if (email != null) {
      fraudScoreService.onNewUserEmail(userId, emailHash, normalizedEmailHash)
    }
    val isDonation = provider?.donation ?: false
    val appPlatform = appVersion.platform

    var userIdentificationStep =
      createUserIdentificationStep(userId, encryptedEmail, emailHash, isDonation, stepOrder = 1, normalizedEmailHash, normalizedEncryptedEmail)
    val faceVerificationRequired = userIdentificationStep?.type == FACE

    checkNoBipaForIos(userId, appVersion, faceVerificationRequired, userIp)

    val (sessionId, facetecOk) = getNewSessionId(faceVerificationRequired)
    if (faceVerificationRequired && !facetecOk) {
      logger().error("Failed to create facetec-session for user $userId face check will be ignored!")
      userIdentificationStep = null
    }

    val session = VerificationSessionDto(
      sessionId = sessionId,
      userId = userId,
      expiredAt = timeService.now().plus(SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
      verification = listOfNotNull(
        userIdentificationStep,
        addExaminationStep(userId, appPlatform, 2),
        addJailBreakStep(userId, appPlatform, 3),
        addLocationStep(userId, appPlatform, 4),
      )
    )
    verificationPersistenceService.createSession(session)
    return VerificationSessionDto.toApiModel(session)
  }

  suspend fun verifyFace(sessionId: String, userAgent: String, request: VerifyFaceLivenessRequestDto): VerifyFaceApiResponseDto {
    try {
      val userId = validateSessionAndGetUserId(sessionId, FACE)

      val livenessCheckResult = facetecClient.checkLiveness(sessionId, userAgent, userId, request)
      val locale = userService.getUser(userId).locale
      if (livenessCheckResult.faceScanSecurityChecks?.faceScanLivenessCheckSucceeded != true) {
        throw FaceLivenessCheckFailedException(
          userId = userId,
          externalMessage = translationService.translateOrDefault(EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT, locale, userId)
        )
      }
      val otherChecksFailed = with(livenessCheckResult.faceScanSecurityChecks) {
        arrayOf(auditTrailVerificationCheckSucceeded, replayCheckSucceeded, sessionTokenCheckSucceeded)
          .any { it == false }
      }
      if (otherChecksFailed) {
        val fraudstersCount = faceFraudstersCounter.incrementAndGet()
        if (fraudstersCount > faceFraudstersCounter.counterLimit) {
          if (fraudstersCount - faceFraudstersCounter.counterLimit == 1L) { // first time at day
            if (marketService.isUsMarket()) {
              logger().alert("Face fraudsters counter limit reached: $fraudstersCount")
            }
          }
          logger().info("Exceeded number of allowed face fraudsters: $fraudstersCount")
          faceFraudstersPersistenceService.save(userId, FaceFraudResult.BLOCKED)
          throw FaceLivenessCheckFailedException(
            userId = userId,
            externalMessage = translationService.translateOrDefault(EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT, locale, userId)
          )
        } else {
          logger().warn("Possible face check fraud: $userId ; $livenessCheckResult")
          faceFraudstersPersistenceService.save(userId, FaceFraudResult.SNEAKED)
        }
      }
      val uniquenessCheckResult = facetecClient.checkUniqueness(sessionId, userAgent, userId)
      if (!uniquenessCheckResult.successful) {
        verificationPersistenceService.saveAssociatedUsers(sessionId, uniquenessCheckResult.associatedUserIds)
        associatedUsersService.check(userId, uniquenessCheckResult.associatedUserIds)
          .takeIf { it != PASSED }
          ?.let { result ->
            verificationPersistenceService.saveAssociatedUsersCheckResults(sessionId, result)
            userCheckManager.onUserFaceUniquenessCheckFailed(userId)
            throw FaceUniquenessCheckFailedException(
              userId = userId,
              externalMessage = translationService.translateOrDefault(EXCEPTION_UNIQUENESS, locale, userId),
              associatedUsersCheckResult = result
            )
          }
      }
      facetecClient.saveToDatabase(sessionId, userAgent, userId)
      verificationPersistenceService.verifyUserSessionBy(sessionId, FACE)
      bigQueryEventPublisher.publish(UserFaceVerifiedEventDto(userId = userId, createdAt = timeService.now()))
      userCheckManager.onUserFaceVerified(userId)
      return VerifyFaceApiResponseDto(scanResultBlob = livenessCheckResult.scanResultBlob)
    } catch (e: Exception) {
      val verificationResultType = when (e) {
        is FaceLivenessCheckFailedException -> LIVENESS_CHECK_FAILED
        is FaceUniquenessCheckFailedException -> e.associatedUsersCheckResult.toVerificationFailReason()
        is InvalidSessionIdException -> INVALID_SESSION
        is SessionExpiredException -> SESSION_EXPIRED
        else -> {
          logger().error("Unexpected facetec exception for sessionId $sessionId, face check will be ignored!", e)
          verificationPersistenceService.verifyUserSessionBy(sessionId, FACE, VerificationStatus.ALLOWED_ONCE)
          ERROR
        }
      }
      if (!(verificationResultType == INVALID_SESSION || verificationResultType == SESSION_EXPIRED)) {
        verificationPersistenceService.addErrorMessageToSession(
          sessionId = sessionId,
          verificationType = FACE,
          verificationResultType = verificationResultType,
          message = e.message
        )
      }
      if (verificationResultType != ERROR) {
        // For now, we mute errors
        throw e
      }
      return VerifyFaceApiResponseDto(scanResultBlob = null)
    }
  }

  suspend fun validateSession(userId: String, sessionId: String) {
    if (!verificationPersistenceService.isSessionValidAndCompleted(userId, sessionId))
      throw VerificationIncompleteException(sessionId, userId)
  }

  suspend fun disassociateSession(userId: String): Int =
    verificationPersistenceService
      .getAllSessionsForUser(userId)
      .sumOf { sessionId -> verificationPersistenceService.disassociateSession(sessionId) }

  suspend fun removeFaces(userId: String) = coroutineScope {
    // back compatibility when faces were stored with userId identifier
    facetecClient.removeFromDatabase(sessionId = null, userId = userId)

    // more optimal to check only verified by face sessions, but this is a test-only method and so this is not critical
    verificationPersistenceService.getAllSessionsForUser(userId)
      .map { sessionId ->
        async {
          val uniquenessResult = facetecClient.checkUniqueness(sessionId, "", userId)
          if (uniquenessResult.rawAssociatedUsers.isNotEmpty()) {
            uniquenessResult.rawAssociatedUsers.map {
              async { facetecClient.removeFromDatabase(it) }
            }.awaitAll()
          }
          facetecClient.removeFromDatabase(sessionId, userId)
        }
      }.awaitAll()
  }

  suspend fun getFaceVerificationStatus(userId: String): VerificationStatusExt =
    verificationPersistenceService.getFaceVerificationDataForUser(userId)
      .let {
        when {
          it == null -> VerificationStatusExt.NOT_TRIED
          it.verificationStatus == VERIFIED -> VerificationStatusExt.VERIFIED
          it.verificationStatus == REQUIRED && it.errorMessage?.contains("Liveness check is failed") ?: false
          -> VerificationStatusExt.LIVENESS_CHECK_FAILED

          it.verificationStatus == REQUIRED && it.errorMessage?.contains("Uniqueness check is failed") ?: false
          -> VerificationStatusExt.UNIQUENESS_CHECK_FAILED

          it.verificationStatus == REQUIRED -> VerificationStatusExt.REQUIRED
          it.verificationStatus == VerificationStatus.ALLOWED_ONCE -> VerificationStatusExt.VERIFIED
          else -> VerificationStatusExt.UNKNOWN
        }
      }

  suspend fun removeOldFaceMaps(olderThanDays: Long, limit: Int, isDryRun: Boolean = true): Int {
    if (olderThanDays < FACE_MAP_TIME_TO_LIVE_DAYS) {
      throw IllegalStateException("This function is not allowed for value ${olderThanDays}!")
    }

    val faceMapsToBeRemoved = measureTimeMillis("DB querying cycle for face maps to delete") {
      verificationPersistenceService.getVerificationSessionsOlderThanDays(olderThanDays, limit)
    }

    if (faceMapsToBeRemoved.isNotEmpty()) {
      val faceMapsSize = faceMapsToBeRemoved.size
      if (isDryRun) {
        logger().info("[dry run] Identified $faceMapsSize faceMaps for removal! First 10: ${faceMapsToBeRemoved.take(10).joinToString(",")}")
      } else {
        faceMapsToBeRemoved.forEach { session ->
          val sessionId = session.sessionId
          val userId = session.userId

          val result = measureTimeMillis("User and session id based Facetec deletion cycle") {
            facetecClient.removeFromDatabase(sessionId, userId)
          }

          //remove below call after first full run
          val resultSessionIdNull = measureTimeMillis("User id based Facetec deletion cycle") {
            facetecClient.removeFromDatabase(sessionId = null, userId)
          }

          if (result.success || resultSessionIdNull.success) {
            measureTimeMillis("Marking a face map deleted in DB cycle") {
              verificationPersistenceService.markFaceMapDeleted(sessionId)
            }
            // TODO Mongo REST API is deprecated and will be removed at September. New Mongo cluster doesn't have REST API at all. https://app.asana.***************************************************/1209635526384011
            // TODO It should be ok for a while cause we don't have old faces in new cluster now
//            measureTimeMillis("Mongo deletion cycle") {
//              deleteFaceFromMongo(sessionId, userId)
//            }
          }
        }
        return faceMapsSize
      }
    }
    return 0
  }

  suspend fun generateIosChallenge(sessionId: String): String {
    val userId = validateSessionAndGetUserId(sessionId, EXAMINATION)
    return iosExaminationService.generateChallenge(userId)
  }

  suspend fun examineIosDevice(sessionId: String, request: IosExaminationRequestApiDto, iosExaminationEnv: IosExaminationEnv) {
    val userId = validateSessionAndGetUserId(sessionId, EXAMINATION)
    try {
      iosExaminationService.examine(userId, request, iosExaminationEnv)
    } catch (e: Exception) {
      verificationPersistenceService.addErrorMessageToSession(
        sessionId = sessionId,
        verificationType = EXAMINATION,
        verificationResultType = IOS_EXAMINATION_ERROR,
        message = e.message
      )
      throw e
    }
    verificationPersistenceService.verifyUserSessionBy(sessionId, EXAMINATION)
  }

  suspend fun verifyIosJailBreak(sessionId: String, request: JailBreakRequestApiDto) {
    val userId = validateSessionAndGetUserId(sessionId, JAIL_BREAK)
    if (request.jailBreak) {
      verificationPersistenceService.addErrorMessageToSession(
        sessionId = sessionId,
        verificationType = JAIL_BREAK,
        verificationResultType = IOS_JAIL_BREAK_USAGE_DETECTED,
        message = "UserId: '$userId'. JailBreak usage detected"
      )
      fraudScoreService.blockUserOnJailBreakUsageDetected(userId)
      throw JailBreakUsageException(userId)
    }
    verificationPersistenceService.verifyUserSessionBy(sessionId, JAIL_BREAK)
  }

  suspend fun verifyGpsLocation(sessionId: String, request: GpsLocationRequestApiDto, appPlatform: AppPlatform) {
    val userId = validateSessionAndGetUserId(sessionId, LOCATION)
    val isMocked = request.isMocked == true

    if (isMocked) {
      verificationPersistenceService.addErrorMessageToSession(
        sessionId = sessionId,
        verificationType = LOCATION,
        verificationResultType = GPS_LOCATION_IS_MOCKED,
        message = "UserId: '$userId'. GPS location is mocked"
      )
      fraudScoreService.blockUserOnMockedLocation(userId)
      sendLocationCheckEvent(userId, appPlatform, sessionId, gpsVerificationPassed = false)
      throw GpsVerificationFailedException()
    }

    val country = request.location.uppercase()
    userService.updateGpsLocationCountry(userId, country)
    val isCountryAllowed = isCountryAllowed(userId, country)
    fraudScoreService.onLocationCheck(userId, checkPassed = isCountryAllowed)
    sendLocationCheckEvent(userId, appPlatform, sessionId, gpsVerificationPassed = isCountryAllowed)
    if (isCountryAllowed) {
      verificationPersistenceService.verifyUserSessionBy(sessionId, LOCATION)
    } else {
      verificationPersistenceService.addErrorMessageToSession(
        sessionId = sessionId,
        verificationType = LOCATION,
        verificationResultType = GPS_LOCATION_NOT_ALLOWED,
        message = "UserId: '$userId'. GPS location is not allowed"
      )
      throw GpsVerificationFailedException()
    }
  }

  private suspend fun createUserIdentificationStep(
    userId: String,
    encryptedEmail: String?,
    emailHash: String,
    isDonation: Boolean,
    stepOrder: Int,
    normalizedEmailHash: String,
    normalizedEncryptedEmail: String
  ): VerificationStep? {
    //no user identification for donations
    // if (isDonation) return null

    // https://justplayapps.atlassian.net/browse/FRAUD-18
    val adjustId = userService.getAdjustId(userId)
    val adjustInstallation = adjustId?.let { adjustEventPersistenceService.loadLastAdjustInstallationByAdjustId(it) }
    if ((adjustInstallation == null || adjustInstallation.adNetwork?.lowercase() == UNTRUSTED_DEVICES_UA_NETWORK)
      && !verificationPersistenceService.userVerifiedBy(userId, FACE)
    ) {
      logger().warn("Face verification was forced for user '$userId', adjustId '$adjustId', ad_network '${adjustInstallation?.adNetwork}'")
      return VerificationStep(type = FACE, status = REQUIRED, order = stepOrder)
    }

    //email verification attempt
    val isTrustedEmail = !encryptedEmail.isNullOrEmpty()
      && !fraudScoreService.isUserSharedEmail(userId)
      && abTestingService.isSeonIntegrationParticipant(userId)
      && !isNewUserWithHighEarnings(userId)
      && seonService.isTrustedEmail(userId, encryptedEmail, emailHash, normalizedEncryptedEmail, normalizedEmailHash)

    //email verified by SEON, track this fact and no more verifications needed
    if (isTrustedEmail) return VerificationStep(type = EMAIL, status = VERIFIED, stepOrder)

    //email is not trusted. Trying to add face verification step
    //check if user already verified by FaceTec
    if (verificationPersistenceService.userVerifiedBy(userId, FACE) && !faceFraudstersPersistenceService.isFraudster(userId)) return null

    //check if we have verified user with the same trackingId
    if (isLegitUserWithSameTrackingIdVerified(userId)) {
      logger().info("User $userId was verified because there is a verified user with the same trackingId met conditions")
      //track this fact
      return VerificationStep(type = FACE, status = VERIFIED, order = stepOrder)
    }

    //noting helped, adding face verification
    return VerificationStep(type = FACE, status = REQUIRED, order = stepOrder)
  }

  private suspend fun isCountryAllowed(userId: String, gpsLocationCountry: String): Boolean =
    userService.getUser(userId).isWhitelisted || marketService.getAllowedCountries().contains(gpsLocationCountry)

  private suspend fun sendLocationCheckEvent(userId: String, appPlatform: AppPlatform, sessionId: String, gpsVerificationPassed: Boolean) {
    // some events may be occasionally not delivered - not critical.
    messageBus.publishAsync(
      SendLocationCheckedEventEffect(
        userId,
        appPlatform,
        sessionId,
        gpsCheckResult = gpsVerificationPassed,
        gpsServiceCheckOption = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GPS_VERIFICATION).getKey()
      )
    )
  }

  //todo remove this function after analysis complete
  private inline fun <T> measureTimeMillis(
    text: String,
    function: () -> T
  ): T {
    val startTime = System.currentTimeMillis()
    val result: T = function.invoke()

    logger().info(
      "$text: ${
        Duration.ofMillis(System.currentTimeMillis() - startTime).toString()
          .substring(2)
          .replace("(\\d[HMS])(?!$)", "$1 ")
          .lowercase()
      }"
    )

    return result
  }

  private suspend fun deleteFaceFromMongo(sessionId: String, userId: String) {
    val response = if (sessionId.length > 36)
      facetecFacesService.deleteFace(buildFacetecId(sessionId, userId))
    else
    //Remove this branch after full pass, new sessions stored with session id composition
    //no harm in deleting sessions by userId because they are all older than 120 (FACE_MAP_TIME_TO_LIVE) days
      facetecFacesService.deleteFace(userId)
    if (response != 1) {
      logger().warn("Could not delete face from storage for session $sessionId")
    }
  }

  private suspend fun getNewSessionId(faceVerificationRequired: Boolean): Pair<String, Boolean> {
    var facetecOk = true
    val sessionId = if (faceVerificationRequired) {
      try {
        facetecClient.initiateSession()
      } catch (e: Exception) {
        logger().error(e.message, e)
        facetecOk = false
        UUID.randomUUID().toString()
      }
    } else UUID.randomUUID().toString()
    return (sessionId to facetecOk)
  }

  private suspend fun addExaminationStep(userId: String, appPlatform: AppPlatform, stepOrder: Int): VerificationStep? {
    return if (appPlatform == IOS && !cashoutService.userHasSuccessfulCashout(userId)) {
      VerificationStep(type = EXAMINATION, status = REQUIRED, order = stepOrder)
    } else {
      null
    }
  }

  private suspend fun addJailBreakStep(userId: String, appPlatform: AppPlatform, stepOrder: Int): VerificationStep? =
    when {
      appPlatform == IOS && !cashoutService.userHasSuccessfulCashout(userId) ->
        VerificationStep(type = JAIL_BREAK, status = REQUIRED, order = stepOrder)

      appPlatform == IOS_WEB && !webUserService.jailBreakCheckPassed(userId) ->
        VerificationStep(type = JAIL_BREAK, status = REQUIRED, order = stepOrder) //it's impossible for web app to pass it now
      else -> null
    }

  private suspend fun addLocationStep(userId: String, appPlatform: AppPlatform, stepOrder: Int): VerificationStep? =
    when (appPlatform) {
      IOS -> addLocationStepForIos(userId, stepOrder)
      ANDROID -> addLocationStepForAndroid(userId, stepOrder)
      else -> null
    }

  private suspend fun addLocationStepForIos(userId: String, stepOrder: Int): VerificationStep? =
    if (cashoutService.userHasSuccessfulCashout(userId)) null
    else VerificationStep(type = LOCATION, status = REQUIRED, order = stepOrder)

  private suspend fun addLocationStepForAndroid(userId: String, stepOrder: Int): VerificationStep? =
    when {
      shouldRequireLocationForAndroid(userId) -> VerificationStep(type = LOCATION, status = REQUIRED, order = stepOrder)
      else -> null
    }

  private suspend fun shouldRequireLocationForAndroid(userId: String): Boolean {
    if (isRevenueAbove30Variation(userId)) return true

    if (cashoutService.userHasSuccessfulCashout(userId)) return false

    return applyAndroidGpsExperiment(userId)
  }

  private suspend fun applyAndroidGpsExperiment(userId: String): Boolean {
    return when (abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GPS_VERIFICATION)) {
      Variations.GPS_CHECK_ACTIVE -> true
      Variations.EARNINGS10 -> cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(userId).amountUsd > 10.toBigDecimal()
      Variations.EARNINGS20 -> cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(userId).amountUsd > 20.toBigDecimal()
      Variations.OFFERWALL_REVENUE_ONLY -> rewardingFacade.getNonCashedUserEarningsWithOfferwallAmount(userId).let {
        it.offerwallRevenue > BigDecimal.ZERO && it.offerwallRevenue.compareTo(it.totalRevenue) == 0
      }

      else -> false
    }
  }

  private suspend fun isRevenueAbove30Variation(userId: String): Boolean =
    abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GPS_VERIFICATION) == Variations.REVENUE_ABOVE_30 &&
      rewardingFacade.getUserRevenueLast2Days(userId) >= BigDecimal("30")

  private suspend fun isLegitUserWithSameTrackingIdVerified(userId: String): Boolean {
    val verifiedUsersWithSameTrackingId = verificationPersistenceService.getUsersWithSameTrackingIdVerifiedBy(userId, FACE)
    return verifiedUsersWithSameTrackingId.isNotEmpty() && associatedUsersService.check(userId, verifiedUsersWithSameTrackingId) == PASSED
  }

  private suspend fun validateSessionAndGetUserId(sessionId: String, verificationType: VerificationSessionDto.VerificationType): String =
    try {
      if (verificationPersistenceService.sessionExpired(sessionId)) {
        throw SessionExpiredException(sessionId)
      }
      verificationPersistenceService.getUserIdBySessionId(sessionId)
    } catch (e: Exception) {
      val verificationResultType = when (e) {
        is InvalidSessionIdException -> INVALID_SESSION
        is SessionExpiredException -> SESSION_EXPIRED
        else -> ERROR
      }
      verificationPersistenceService.addErrorMessageToSession(
        sessionId = sessionId,
        verificationType = verificationType,
        verificationResultType = verificationResultType,
        message = e.message
      )
      throw e
    }

  private suspend fun isNewUserWithHighEarnings(userId: String): Boolean {
    val userCreationDate = userService.getUser(userId).createdAt
    val userEarnings = rewardingFacade.getTotalUsdEarningsForUser(userId)
    val earningsThreshold = BigDecimal("8")
    return Instant.now().isBefore(userCreationDate.plus(24, ChronoUnit.HOURS)) && userEarnings > earningsThreshold
  }

  private suspend fun findStubbedSession(userId: String): Pair<String, Instant>? {
    if (buildVariant == BuildVariant.PRODUCTION) return null
    return verificationPersistenceService.findActiveAndVerifiedSession(userId)
  }

  private suspend fun checkNoBipaForIos(userId: String, appVersion: AppVersionDto, faceVerificationRequired: Boolean, userIp: String?) {
    if (appVersion.platform == IOS &&
      appVersion.version < IOS_BIPA_NON_RESTRICTED_MIN_APP_VERSION &&
      faceVerificationRequired &&
      userIp != null &&
      featureFlagsFacade.forbidIosBipaCashoutWithFaceVerification() &&
      ipService.isFromBlockedBipaState(userIp)
    ) {
      throw IllegalStateException("Illegal BIPA state for userId: ${userId}, user IP: $userIp")
    }
  }

  @Serializable
  enum class VerificationStatusExt {
    NOT_TRIED,
    LIVENESS_CHECK_FAILED,
    UNIQUENESS_CHECK_FAILED,
    REQUIRED,
    VERIFIED,
    UNKNOWN
  }
}
