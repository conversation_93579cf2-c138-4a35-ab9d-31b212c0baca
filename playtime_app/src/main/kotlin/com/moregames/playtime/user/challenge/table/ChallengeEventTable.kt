package com.moregames.playtime.user.challenge.table

import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.time.Instant

object ChallengeEventTable : Table("playtime.challenge_event") {
  val id = varchar("id", 36)
  val dateFrom = timestamp("date_from")
  val dateTo = timestamp("date_to")
  val enabled = bool("enabled")
  val cfg = text("cfg")
  val eventType = enumerationByName("event_type", 50, ChallengeEventType::class)
  val bonusId = varchar("bonus_id", 50).nullable()
  val createdAt = timestamp("created_at").clientDefault { Instant.now() }
  val updatedAt = timestamp("updated_at").clientDefault { Instant.now() }
}