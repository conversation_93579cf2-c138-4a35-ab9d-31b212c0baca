package com.moregames.playtime.user.challenge.api.admin

import com.moregames.base.util.InstantAsString
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.papsign.ktor.openapigen.annotations.Request
import kotlinx.serialization.Serializable

@Request("Challenge event configuration")
@Serializable
data class ChallengeEventAdminApiDto(
  val id: String,
  val dateFrom: InstantAsString,
  val dateTo: InstantAsString,
  val challengesUpdateText: String,
  val claimWidget: ClaimWidgetDto,
  val tutorialSteps: List<String>,
  val challenges: List<ChallengeAdminApiDto>,
  val enabled: Boolean = true,
  val eventType: ChallengeEventType,
  val bonusTracker: BonusTrackerDto?,
  val tutorialWidget: TutorialWidgetDto?
)