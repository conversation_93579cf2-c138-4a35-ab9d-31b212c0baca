package com.moregames.playtime.user.challenge.dto

import com.moregames.base.messaging.dto.MessageDto
import com.moregames.base.util.BigDecimalAsString
import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable

@Serializable
data class RewardEarningsAddedEventDto(
  val metaId: Int,
  val userId: String,
  val amount: BigDecimalAsString,
  val amountUserCurrency: BigDecimalAsString,
  val userCurrencyCode: String,
  val challengeEventId: String,
  val createdAt: InstantAsString,
  //it is nullable for compatibility during release
  val challengeType: ChallengeType?,
) : MessageDto {

  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "reward-earnings-added"
  }
}