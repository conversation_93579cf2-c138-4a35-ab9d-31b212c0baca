package com.moregames.playtime.user.challenge.common

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.PackagePrivate
import com.moregames.playtime.user.challenge.dto.Challenge
import com.moregames.playtime.user.challenge.dto.ChallengeEvent
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.dto.ChallengeProgressTrackingDto
import com.moregames.playtime.user.challenge.dto.ChallengeState
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.dto.UserChallengeEvent
import java.math.BigDecimal
import java.time.Instant

@OptIn(PackagePrivate::class)
@Singleton
class ChallengeService
@Inject constructor(
  private val challengeEventPersistenceService: ChallengeEventPersistenceService,
  private val challengeEventResolver: ChallengeEventResolver,
  private val abTestingService: AbTestingService,
) {

  suspend fun forceCompleteChallenge(userId: String, challengeId: String) {
    challengeEventPersistenceService.forceCompleteUserChallenge(challengeId = challengeId, userId = userId)
  }

  suspend fun getChallengeProgressTracking(userId: String, gameId: Int): ChallengeProgressTrackingDto? {
    val challengeEvent = challengeEventResolver.getChallengeEvent(userId) ?: return null
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CHALLENGES)
    return challengeEventPersistenceService.getUserChallenges(challengeEvent.id, userId)
      .filter { it.challenge.gameId == gameId && !it.state.isFinal() }
      .filter { byUserSpecialVariation(it.challenge, variation) }
      .map { ChallengeProgressTrackingDto(it.challenge.progressMax)}
      .firstOrNull()
  }

  suspend fun getUserChallenges(userId: String): List<UserChallenge> {
    val challengeEvent = challengeEventResolver.getChallengeEvent(userId) ?: return emptyList()
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CHALLENGES)
    return challengeEventPersistenceService.getUserChallenges(challengeEvent.id, userId)
      .filter { byUserSpecialVariation(it.challenge, variation) }
  }

  suspend fun getUserChallenges(userId: String, eventId: ChallengeEventId): List<UserChallenge> {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CHALLENGES)
    return challengeEventPersistenceService.getUserChallenges(eventId, userId)
      .filter { byUserSpecialVariation(it.challenge, variation) }
  }

  suspend fun challengeEventExists(userId: String): Boolean {
    return challengeEventResolver.getChallengeEvent(userId) != null
  }

  suspend fun getCurrentChallengeEvent(userId: String): ChallengeEvent? {
    val event = challengeEventResolver.getChallengeEvent(userId)
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CHALLENGES)
    return event?.copy(challenges = event.challenges.filter { byUserSpecialVariation(it, variation) })
  }

  suspend fun startUserEvent(eventId: ChallengeEventId, userId: String): Boolean =
    challengeEventPersistenceService.startUserEvent(eventId, userId)

  suspend fun completeEvent(challengeEventId: ChallengeEventId, userId: String): Boolean {
    return challengeEventPersistenceService.completeEvent(challengeEventId, userId)
  }


  suspend fun startUserChallenge(challengeId: ChallengeId, userId: String): Boolean =
    challengeEventPersistenceService.startUserChallenge(challengeId, userId)

  suspend fun updateChallengeProgress(challenge: UserChallenge, progress: ChallengeProgress, state: ChallengeState): Boolean =
    challengeEventPersistenceService.updateChallengeProgress(challenge, progress, state)

  suspend fun getChallengeEventList(dateFrom: Instant, dateTo: Instant, enabled: Boolean?) =
    challengeEventPersistenceService.getChallengeEventList(dateFrom, dateTo, enabled)

  suspend fun createChallengeEvent(challengeEvent: ChallengeEvent) {
    challengeEventPersistenceService.createChallengeEvent(challengeEvent)
  }

  suspend fun updateChallengeEvent(challengeEvent: ChallengeEvent) {
    challengeEventPersistenceService.updateChallengeEvent(challengeEvent)
  }

  suspend fun getUserChallengeEvent(userId: String, challengeEventId: ChallengeEventId): UserChallengeEvent? =
    challengeEventPersistenceService.getUserChallengeEvent(userId, challengeEventId)

  suspend fun updateIncompleteChallengeEventRevenue(userId: String, challengeEventId: ChallengeEventId, amount: BigDecimal): Int =
    challengeEventPersistenceService.updateIncompleteChallengeEventRevenue(userId, challengeEventId, amount)

  suspend fun getUserChallenge(challengeId: ChallengeId, userId: String): UserChallenge? =
    challengeEventPersistenceService.getUserChallenge(challengeId, userId)

  suspend fun claimChallenge(challengeId: ChallengeId, userId: String, coins: Int): Boolean =
    challengeEventPersistenceService.claimChallenge(challengeId, userId, coins)

  suspend fun claimEvent(eventId: ChallengeEventId, userId: String, amount: BigDecimal): Boolean =
    challengeEventPersistenceService.claimEvent(eventId, userId, amount)

  suspend fun userHadChallengesInLastDay(userId: String): Boolean = challengeEventPersistenceService.userHadChallengesInLastDay(userId)

  suspend fun getChallengeEventById(challengeEventId: ChallengeEventId): ChallengeEvent? =
    challengeEventPersistenceService.loadChallengeEventById(challengeEventId)

  private fun byUserSpecialVariation(challenge: Challenge, variation: BaseVariation): Boolean =
    when (challenge.challengeType) {
      ChallengeType.REGULAR -> true
      ChallengeType.SPECIAL -> variation == Variations.SHOW_SPECIAL_CHALLENGES
    }

  suspend fun countClaimedBonusEvent(userId: String, bonusId: String): Long {
    return challengeEventPersistenceService.countClaimedBonusEvent(userId, bonusId)
  }
}