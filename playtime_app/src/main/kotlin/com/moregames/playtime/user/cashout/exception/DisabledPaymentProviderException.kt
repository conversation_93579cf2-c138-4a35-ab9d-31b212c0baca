package com.moregames.playtime.user.cashout.exception

import com.moregames.base.app.PaymentProviderType
import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class DisabledPaymentProviderException(userId: String, provider: PaymentProviderType) : BaseException(
  internalMessage = "User $userId can't cashout using provider ${provider.key}",
  externalMessage = "Invalid payment provider"
) {
  override val errorCode = PlaytimeErrorCodes.INVALID_PAYMENT_PROVIDER
  override val errorType = ErrorType.INPUT_ERROR
}
