package com.moregames.playtime.user.challenge

import com.google.inject.Singleton
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.applovin.APPLOVIN_BANNER_REVENUE_ALIGNER
import com.moregames.base.bus.MessageBus
import com.moregames.base.user.FakeMetaEarnings
import com.moregames.base.util.TimeService
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.RewardEarningsAddedEventDto
import com.moregames.playtime.user.challenge.dto.UserChallengeEvent
import java.math.BigDecimal
import javax.inject.Inject

@Singleton
class ChallengeRewardingService @Inject constructor(
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val cashoutService: CashoutService,
  private val cashoutSettingsService: CashoutSettingsService,
  private val cashoutStatusService: CashoutStatusService,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
  private val challengeRevenueService: ChallengeRevenueService,
) {

  companion object {
    private val MIN_EVENT_REWARD = BigDecimal("0.01")
  }

  fun calculateChallengeReward(challengeId: ChallengeId, userId: String): Int {
    //currently we don't need to add any coins
    return 0
  }

  suspend fun calculateChallengeEventRewardUsd(event: UserChallengeEvent): BigDecimal {
    // as later reward will be added directly to Earnings, calculation should try to comply as much as possible to
    //     com.moregames.playtime.earnings.UserEarningsEm2Service.convertRevenueToEarnings

    val previousUnpaidUserEarnings =
      cashoutService.getNonCashedUserCurrencyEarnings(event.userId).amountUsd
    val userMaxEarnings = cashoutSettingsService.getUserMaxEarningsAmount(event.userId)
    val maxEarningsLimit = (userMaxEarnings - previousUnpaidUserEarnings).max(BigDecimal.ZERO)

    return (event.applovinNonBannerRevenue ?: BigDecimal.ZERO)
      .multiply(APPLOVIN_BANNER_REVENUE_ALIGNER) // as only inters are at hands
      .multiply(challengeRevenueService.challengeEventRevenueShare())
      .max(MIN_EVENT_REWARD)
      .min(maxEarningsLimit)
  }

  /**
   * @return false - when reward was already added to DB. true - otherwise.
   */
  suspend fun giveChallengeEventReward(event: UserChallengeEvent, reward: CurrencyExchangeResultDto): Boolean {
    val rewardAdded = userEarningsPersistenceService.addChallengeEventReward(
      event.userId,
      event.eventId.value,
      rewardUsd = reward.usdAmount,
      currency = reward.userCurrency,
      reward = reward.amount,
    )

    if (rewardAdded) {
      cashoutStatusService.enableCashout(event.userId)
      emitRewardEarningsAdded(event, reward)
    }

    return rewardAdded
  }

  private suspend fun emitRewardEarningsAdded(event: UserChallengeEvent, reward: CurrencyExchangeResultDto) {
    val earningsAddedEvent = RewardEarningsAddedEventDto(
      metaId = FakeMetaEarnings.FAKE_META_ID_CHALLENGE_REWARD.id,
      userId = event.userId,
      amount = reward.usdAmount,
      amountUserCurrency = reward.amount,
      userCurrencyCode = reward.userCurrency.currencyCode,
      challengeEventId = event.eventId.value,
      createdAt = timeService.now(),
      challengeType = ChallengeType.REGULAR,
    )

    messageBus.publish(earningsAddedEvent)
  }
}