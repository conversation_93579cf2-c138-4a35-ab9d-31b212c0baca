package com.moregames.playtime.user.promotion.event.manager

import com.google.inject.Inject
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.promotion.event.manager.api.admin.PromotionEventAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.admin.PromotionEventsAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.*
import com.moregames.playtime.user.promotion.event.manager.dto.AnnouncementDetailsDto
import com.moregames.playtime.user.promotion.event.manager.dto.CountDownBannersDto
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEvent
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventTranslationLink
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventUiConfiguration
import com.moregames.playtime.user.timezone.UserTimeZoneService
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.Instant
import java.util.*
import javax.inject.Singleton

@Singleton
class PromotionEventService @Inject constructor(
  private val promotionEventPersistenceService: PromotionEventPersistenceService,
  private val timeService: TimeService,
  private val json: Json,
  private val translationService: UserTranslationService,
  private val userTimeZoneService: UserTimeZoneService,
  private val promotionEventConfigResolver: PromotionEventConfigResolver,
  private val promotionTranslationService: PromotionTranslationService,
  private val promotionEventValidator: PromotionEventValidator,
) {

  companion object {
    val EMPTY_PROMOTION_CONFIG = PromotionEventConfigApiDto.empty()
  }

  suspend fun getPromotionEventConfig(userId: String, locale: Locale): PromotionEventConfigApiDto {
    val now = timeService.now()
    val currentPromotion = promotionEventConfigResolver.resolveCurrentPromotion(userId) ?: return EMPTY_PROMOTION_CONFIG
    val translations = promotionTranslationService.getTranslationsForPromoConfig(currentPromotion.id, locale)
    val timeRange = getEpochSecondRangeForUser(userId, now, currentPromotion)
    val uiConfiguration = parseUiConfiguration(currentPromotion.uiConfiguration) ?: return EMPTY_PROMOTION_CONFIG
    val offerModifiers = uiConfiguration.offerModifier?.map { it.modify(locale, userId) }
    val countDownBannersTimeRange = now.epochSecond..timeRange.last
    val countDownBanners = createCountDownBannersApiDto(uiConfiguration.countDownBanners, locale, userId, countDownBannersTimeRange)
    val announcementDetails = prepareAnnouncementDetails(uiConfiguration, userId, locale)
    return PromotionEventConfigApiDto(
      id = currentPromotion.id,
      startTime = timeRange.first,
      endTime = timeRange.last,
      timestamp = now.epochSecond,
      offerModifier = offerModifiers,
      translations = translations,
      top = uiConfiguration.top,
      mainTop = uiConfiguration.mainTop,
      challengesTop = uiConfiguration.challengesTop,
      countDownBanners = countDownBanners,
      infoBar = uiConfiguration.infoBar,
      expectedAppVersion = uiConfiguration.expectedAppVersion,
      announcementDetails = announcementDetails,
    )
  }

  private suspend fun prepareAnnouncementDetails(uiConfiguration: PromotionEventUiConfiguration, userId: String, locale: Locale): AnnouncementDetailsDto? {
    val announcementDetails = uiConfiguration.announcementDetails
    if (announcementDetails == null) {
      return null
    }
    val buttonText = announcementDetails.buttonText?. let { translationService.tryTranslate(announcementDetails.buttonText, locale, userId) }
      ?: translationService.translateOrDefault(TranslationResource.PROMOTION_UPDATE_VERSION_BUTTON_TEXT, locale, userId)
    return announcementDetails.copy(
      title = translationService.tryTranslate(announcementDetails.title, locale, userId),
      description = translationService.tryTranslate(announcementDetails.description, locale, userId),
      buttonText = buttonText
    )
  }

  private suspend fun getEpochSecondRangeForUser(userId: String, now: Instant, event: PromotionEvent): LongRange = with(event) {
    if (!useUserTimeZone) return dateFrom.epochSecond..dateTo.epochSecond
    return userTimeZoneService.getEpochSecondRangeForUser(userId, now, dateFrom, dateTo)
  }

  suspend fun getPromotionConfigList(dateFrom: Instant, dateTo: Instant, enabled: Boolean?): PromotionEventsAdminApiDto {
    val events = promotionEventPersistenceService.getPromotionEventList(dateFrom, dateTo, enabled)
    val translations = promotionTranslationService.getTranslationLinks(events.map { it.id} ).groupBy { it.eventId }

    return events.map { event ->
      PromotionEventAdminApiDto(
        id = event.id,
        dateTo = event.dateTo,
        dateFrom = event.dateFrom,
        uiConfiguration = json.decodeFromString(event.uiConfiguration),
        useUserTimeZone = event.useUserTimeZone,
        experimentKey = event.experimentKey,
        variationKey = event.variationKey,
        priorityKey = event.priorityKey,
        translationResources = translations[event.id]?.map { it.toAdminApiDto() },
        eventType = event.eventType
      )
    }.let {
      PromotionEventsAdminApiDto(it)
    }
  }

  suspend fun savePromotionConfig(request: PromotionEventAdminApiDto) {
    promotionEventValidator.validate(request)
    val promotionEvent = PromotionEvent(
      id = request.id,
      dateFrom = request.dateFrom,
      dateTo = request.dateTo,
      useUserTimeZone = request.useUserTimeZone,
      uiConfiguration = json.encodeToString(request.uiConfiguration),
      experimentKey = request.experimentKey,
      variationKey = request.variationKey,
      priorityKey = request.priorityKey,
      eventType = request.eventType
    )
    promotionEventPersistenceService.savePromotion(promotionEvent)
    request.translationResources?.map { PromotionEventTranslationLink(request.id, it.resourceName, it.originalResourceName) }?.let {
      promotionTranslationService.savePromotionEventTranslationLinks(it)
    }
  }

  private suspend fun createCountDownBannersApiDto(
    countDownBannersDto: CountDownBannersDto?,
    locale: Locale,
    userId: String,
    timeRange: LongRange,
  ): CountDownBannersApiDto? {
    if (countDownBannersDto == null) {
      return null
    } else {
      return CountDownBannersApiDto(
        startPosition = countDownBannersDto.startPosition,
        step = countDownBannersDto.step,
        max = countDownBannersDto.max,
        title = translationService.tryTranslate(countDownBannersDto.title, locale, userId),
        backgroundImage = countDownBannersDto.backgroundImage,
        infoImages = countDownBannersDto.infoImages,
        infoTitle = translationService.tryTranslate(countDownBannersDto.infoTitle, locale, userId),
        infoSections = translateInfoSections(countDownBannersDto.infoSections, locale, userId),
        infoButtonClickAction = countDownBannersDto.infoButtonClickAction,
        infoButtonText = countDownBannersDto.infoButtonText?. let { translationService.tryTranslate(it, locale, userId) },
        endTime = calculateTimerEndTime(timeRange, countDownBannersDto.durationInSeconds)
      )
    }
  }

  private fun calculateTimerEndTime(timeRange: LongRange, timerDuration: Long?): Long? {
    if (timerDuration == null) {
      return null
    }
    return timeRange.reversed().step(timerDuration).last
  }

  private suspend fun translateInfoSections(
    infoSections: List<CountDownInfoSectionDto>,
    locale: Locale,
    userId: String
  ): List<CountDownInfoSectionApiDto> {
    return infoSections.map {
      CountDownInfoSectionApiDto(
        subTitle = translationService.tryTranslate(it.subTitle, locale, userId),
        subText = translationService.tryTranslate(it.subText, locale, userId)
      )
    }
  }

  private fun parseUiConfiguration(uiConfiguration: String): PromotionEventUiConfiguration? {
    return try {
      json.decodeFromString<PromotionEventUiConfiguration>(uiConfiguration)
    } catch (e: Exception) {
      logger().error("Unable to parse promotion UI configuration", e)
      return null
    }
  }

  private suspend fun OfferModifierConfigApiDto.modify(locale: Locale, userId: String): OfferModifierConfigApiDto {
    val badgeText = this.badge?.text ?: return this
    val translatedBadgeText = translationService.tryTranslate(badgeText, locale, userId)
    if (badgeText == translatedBadgeText) {
      return this
    }
    return this.copy(badge = badge.copy(text = translatedBadgeText))
  }
}
