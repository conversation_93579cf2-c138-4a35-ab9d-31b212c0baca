package com.moregames.playtime.user.gamerank

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.playtime.translations.TranslationResource.GAME_RANK_RULES_TITLE
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto.Companion.openLinkInPopUp
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuItem
import com.moregames.playtime.user.gamerank.GameRank.*
import java.util.*
import javax.inject.Inject

interface UserGameRankService {
  suspend fun userEligibleForGameRanking(userId: String): Boolean
  suspend fun getUserGameRank(userId: String, gameId: Int): GameRank?
  suspend fun getThreeDotMenuItem(userId: String, locale: Locale): ThreeDotMenuItem?
  suspend fun getOfferModifiers(userId: String, gameId: Int): GameRankOfferModifiers?
}

class UserGameRankServiceImpl @Inject constructor(
  private val abTestingService: AbTestingService,
  private val userGameRankPersistenceService: UserGameRankPersistenceService,
  private val translationService: UserTranslationService,
) : UserGameRankService {
  override suspend fun userEligibleForGameRanking(userId: String): Boolean {
    if (arrayOf(
        ClientExperiment.ANDROID_TASKS_IN_PRE_GAME,
        ClientExperiment.ANDROID_GAME_STORIES,
        ClientExperiment.ANDROID_PRE_GAME_SCREEN
      ).any { abTestingService.isUserExperimentParticipant(userId, it) }
    ) return false

    return abTestingService.isUserExperimentParticipant(userId, ClientExperiment.ANDROID_USER_GAME_RANKING)
  }

  override suspend fun getUserGameRank(userId: String, gameId: Int): GameRank? {
    if (!GameRankConfigHolder.gamesConfig.containsKey(gameId) || !userEligibleForGameRanking(userId)) {
      return null
    }
    return userGameRankPersistenceService.getUserGameRankEntity(userId, gameId).rank
  }

  override suspend fun getThreeDotMenuItem(userId: String, locale: Locale): ThreeDotMenuItem? {
    return if (userEligibleForGameRanking(userId)) {
      ThreeDotMenuItem(
        action = openLinkInPopUp("https://justplayapps.com/rank-rules/"),
        label = translationService.translateOrDefault(GAME_RANK_RULES_TITLE, locale, userId),
      )
    } else null
  }

  override suspend fun getOfferModifiers(userId: String, gameId: Int): GameRankOfferModifiers? {
    val currentRank = getUserGameRank(userId, gameId)
    if (currentRank == null) return null

    return gameRank2Modifiers[GameIdAndRank(gameId, currentRank)]
  }

  companion object {
    private val gameRank2Modifiers = mapOf(
      // solitaire
      GameIdAndRank(200044, ZERO)
        to GameRankOfferModifiers("game_ranks/solitaire_verse_rank_0.jpg", "\$_solitaire_verse_text_install_bottom_rank_rules"),
      GameIdAndRank(200044, ONE)
        to GameRankOfferModifiers("game_ranks/solitaire_verse_rank_1.jpg", "\$_solitaire_verse_text_install_bottom_rank_rules"),
      GameIdAndRank(200044, TWO)
        to GameRankOfferModifiers("game_ranks/solitaire_verse_rank_2.jpg", "\$_solitaire_verse_text_install_bottom_rank_rules"),
      GameIdAndRank(200044, THREE)
        to GameRankOfferModifiers("game_ranks/solitaire_verse_rank_3.jpg", "\$_solitaire_verse_text_install_bottom_rank_rules"),
      // sugar rush
      GameIdAndRank(200052, ZERO)
        to GameRankOfferModifiers("game_ranks/sugar_rush_rank_0.jpg", "\$_sugar_rush_text_install_bottom_rank_rules"),
      GameIdAndRank(200052, ONE)
        to GameRankOfferModifiers("game_ranks/sugar_rush_rank_1.jpg", "\$_sugar_rush_text_install_bottom_rank_rules"),
      GameIdAndRank(200052, TWO)
        to GameRankOfferModifiers("game_ranks/sugar_rush_rank_2.jpg", "\$_sugar_rush_text_install_bottom_rank_rules"),
      GameIdAndRank(200052, THREE)
        to GameRankOfferModifiers("game_ranks/sugar_rush_rank_3.jpg", "\$_sugar_rush_text_install_bottom_rank_rules"),
      // blockbuster
      GameIdAndRank(200074, ZERO)
        to GameRankOfferModifiers("game_ranks/blockbuster_rank_0.jpg", "\$_blockbuster_text_install_bottom_rank_rules"),
      GameIdAndRank(200074, ONE)
        to GameRankOfferModifiers("game_ranks/blockbuster_rank_1.jpg", "\$_blockbuster_text_install_bottom_rank_rules"),
      GameIdAndRank(200074, TWO)
        to GameRankOfferModifiers("game_ranks/blockbuster_rank_2.jpg", "\$_blockbuster_text_install_bottom_rank_rules"),
      GameIdAndRank(200074, THREE)
        to GameRankOfferModifiers("game_ranks/blockbuster_rank_3.jpg", "\$_blockbuster_text_install_bottom_rank_rules"),
      // pinmaster
      GameIdAndRank(200075, ZERO)
        to GameRankOfferModifiers("game_ranks/pin_master_rank_0.jpg", "\$_android_pin_master_text_install_bottom_rank_rules"),
      GameIdAndRank(200075, ONE)
        to GameRankOfferModifiers("game_ranks/pin_master_rank_1.jpg", "\$_android_pin_master_text_install_bottom_rank_rules"),
      GameIdAndRank(200075, TWO)
        to GameRankOfferModifiers("game_ranks/pin_master_rank_2.jpg", "\$_android_pin_master_text_install_bottom_rank_rules"),
      GameIdAndRank(200075, THREE)
        to GameRankOfferModifiers("game_ranks/pin_master_rank_3.jpg", "\$_android_pin_master_text_install_bottom_rank_rules"),
      // bubblepop
      GameIdAndRank(200057, ZERO)
        to GameRankOfferModifiers("game_ranks/bubble_pop_rank_0.jpg", "\$_bubble_pop_text_install_bottom_rank_rules"),
      GameIdAndRank(200057, ONE)
        to GameRankOfferModifiers("game_ranks/bubble_pop_rank_1.jpg", "\$_bubble_pop_text_install_bottom_rank_rules"),
      GameIdAndRank(200057, TWO)
        to GameRankOfferModifiers("game_ranks/bubble_pop_rank_2.jpg", "\$_bubble_pop_text_install_bottom_rank_rules"),
      GameIdAndRank(200057, THREE)
        to GameRankOfferModifiers("game_ranks/bubble_pop_rank_3.jpg", "\$_bubble_pop_text_install_bottom_rank_rules"),
      // mad smash
      GameIdAndRank(200041, ZERO)
        to GameRankOfferModifiers("game_ranks/mad_smash_rank_0.jpg", "\$_mad_smash_text_install_bottom_rank_rules"),
      GameIdAndRank(200041, ONE)
        to GameRankOfferModifiers("game_ranks/mad_smash_rank_1.jpg", "\$_mad_smash_text_install_bottom_rank_rules"),
      GameIdAndRank(200041, TWO)
        to GameRankOfferModifiers("game_ranks/mad_smash_rank_2.jpg", "\$_mad_smash_text_install_bottom_rank_rules"),
      GameIdAndRank(200041, THREE)
        to GameRankOfferModifiers("game_ranks/mad_smash_rank_3.jpg", "\$_mad_smash_text_install_bottom_rank_rules"),
      // word seeker
      GameIdAndRank(200046, ZERO)
        to GameRankOfferModifiers("game_ranks/word_seeker_rank_0.jpg", "\$_word_seeker_text_install_bottom_rank_rules"),
      GameIdAndRank(200046, ONE)
        to GameRankOfferModifiers("game_ranks/word_seeker_rank_1.jpg", "\$_word_seeker_text_install_bottom_rank_rules"),
      GameIdAndRank(200046, TWO)
        to GameRankOfferModifiers("game_ranks/word_seeker_rank_2.jpg", "\$_word_seeker_text_install_bottom_rank_rules"),
      GameIdAndRank(200046, THREE)
        to GameRankOfferModifiers("game_ranks/word_seeker_rank_3.jpg", "\$_word_seeker_text_install_bottom_rank_rules"),
      // merge blast
      GameIdAndRank(200048, ZERO)
        to GameRankOfferModifiers("game_ranks/merge_blast_rank_0.jpg", "\$_merge_blast_text_install_bottom_rank_rules"),
      GameIdAndRank(200048, ONE)
        to GameRankOfferModifiers("game_ranks/merge_blast_rank_1.jpg", "\$_merge_blast_text_install_bottom_rank_rules"),
      GameIdAndRank(200048, TWO)
        to GameRankOfferModifiers("game_ranks/merge_blast_rank_2.jpg", "\$_merge_blast_text_install_bottom_rank_rules"),
      GameIdAndRank(200048, THREE)
        to GameRankOfferModifiers("game_ranks/merge_blast_rank_3.jpg", "\$_merge_blast_text_install_bottom_rank_rules"),
      // hex match
      GameIdAndRank(200050, ZERO)
        to GameRankOfferModifiers("game_ranks/hex_match_rank_0.jpg", "\$_hex_match_text_install_bottom_rank_rules"),
      GameIdAndRank(200050, ONE)
        to GameRankOfferModifiers("game_ranks/hex_match_rank_1.jpg", "\$_hex_match_text_install_bottom_rank_rules"),
      GameIdAndRank(200050, TWO)
        to GameRankOfferModifiers("game_ranks/hex_match_rank_2.jpg", "\$_hex_match_text_install_bottom_rank_rules"),
      GameIdAndRank(200050, THREE)
        to GameRankOfferModifiers("game_ranks/hex_match_rank_3.jpg", "\$_hex_match_text_install_bottom_rank_rules"),
      // block hole clash
      GameIdAndRank(200054, ZERO)
        to GameRankOfferModifiers("game_ranks/block_hole_clash_rank_0.jpg", "\$_block_hole_clash_text_install_bottom_rank_rules"),
      GameIdAndRank(200054, ONE)
        to GameRankOfferModifiers("game_ranks/block_hole_clash_rank_1.jpg", "\$_block_hole_clash_text_install_bottom_rank_rules"),
      GameIdAndRank(200054, TWO)
        to GameRankOfferModifiers("game_ranks/block_hole_clash_rank_2.jpg", "\$_block_hole_clash_text_install_bottom_rank_rules"),
      GameIdAndRank(200054, THREE)
        to GameRankOfferModifiers("game_ranks/block_hole_clash_rank_3.jpg", "\$_block_hole_clash_text_install_bottom_rank_rules"),
      // word kitchen
      GameIdAndRank(200061, ZERO)
        to GameRankOfferModifiers("game_ranks/word_kitchen_rank_0.jpg", "\$_word_kitchen_text_install_bottom_rank_rules"),
      GameIdAndRank(200061, ONE)
        to GameRankOfferModifiers("game_ranks/word_kitchen_rank_1.jpg", "\$_word_kitchen_text_install_bottom_rank_rules"),
      GameIdAndRank(200061, TWO)
        to GameRankOfferModifiers("game_ranks/word_kitchen_rank_2.jpg", "\$_word_kitchen_text_install_bottom_rank_rules"),
      GameIdAndRank(200061, THREE)
        to GameRankOfferModifiers("game_ranks/word_kitchen_rank_3.jpg", "\$_word_kitchen_text_install_bottom_rank_rules"),
      // water sorter
      GameIdAndRank(200062, ZERO)
        to GameRankOfferModifiers("game_ranks/water_sorter_rank_0.jpg", "\$_water_sorter_text_install_bottom_rank_rules"),
      GameIdAndRank(200062, ONE)
        to GameRankOfferModifiers("game_ranks/water_sorter_rank_1.jpg", "\$_water_sorter_text_install_bottom_rank_rules"),
      GameIdAndRank(200062, TWO)
        to GameRankOfferModifiers("game_ranks/water_sorter_rank_2.jpg", "\$_water_sorter_text_install_bottom_rank_rules"),
      GameIdAndRank(200062, THREE)
        to GameRankOfferModifiers("game_ranks/water_sorter_rank_3.jpg", "\$_water_sorter_text_install_bottom_rank_rules"),
      // spiral drop
      GameIdAndRank(200067, ZERO)
        to GameRankOfferModifiers("game_ranks/spiral_drop_rank_0.jpg", "\$_spiral_drop_text_install_bottom_rank_rules"),
      GameIdAndRank(200067, ONE)
        to GameRankOfferModifiers("game_ranks/spiral_drop_rank_1.jpg", "\$_spiral_drop_text_install_bottom_rank_rules"),
      GameIdAndRank(200067, TWO)
        to GameRankOfferModifiers("game_ranks/spiral_drop_rank_2.jpg", "\$_spiral_drop_text_install_bottom_rank_rules"),
      GameIdAndRank(200067, THREE)
        to GameRankOfferModifiers("game_ranks/spiral_drop_rank_3.jpg", "\$_spiral_drop_text_install_bottom_rank_rules"),
      // space connect
      GameIdAndRank(200069, ZERO)
        to GameRankOfferModifiers("game_ranks/space_connect_rank_0.jpg", "\$_space_connect_text_install_bottom_rank_rules"),
      GameIdAndRank(200069, ONE)
        to GameRankOfferModifiers("game_ranks/space_connect_rank_1.jpg", "\$_space_connect_text_install_bottom_rank_rules"),
      GameIdAndRank(200069, TWO)
        to GameRankOfferModifiers("game_ranks/space_connect_rank_2.jpg", "\$_space_connect_text_install_bottom_rank_rules"),
      GameIdAndRank(200069, THREE)
        to GameRankOfferModifiers("game_ranks/space_connect_rank_3.jpg", "\$_space_connect_text_install_bottom_rank_rules"),
      // bubble chef
      GameIdAndRank(200077, ZERO)
        to GameRankOfferModifiers("game_ranks/bubble_chef_rank_0.jpg", "\$_bubble_chef_text_install_bottom_rank_rules"),
      GameIdAndRank(200077, ONE)
        to GameRankOfferModifiers("game_ranks/bubble_chef_rank_1.jpg", "\$_bubble_chef_text_install_bottom_rank_rules"),
      GameIdAndRank(200077, TWO)
        to GameRankOfferModifiers("game_ranks/bubble_chef_rank_2.jpg", "\$_bubble_chef_text_install_bottom_rank_rules"),
      GameIdAndRank(200077, THREE)
        to GameRankOfferModifiers("game_ranks/bubble_chef_rank_3.jpg", "\$_bubble_chef_text_install_bottom_rank_rules"),
      // atlantis bounce
      GameIdAndRank(200078, ZERO)
        to GameRankOfferModifiers("game_ranks/atlantis_bounce_rank_0.jpg", "\$_atlantis_bounce_text_install_bottom_rank_rules"),
      GameIdAndRank(200078, ONE)
        to GameRankOfferModifiers("game_ranks/atlantis_bounce_rank_1.jpg", "\$_atlantis_bounce_text_install_bottom_rank_rules"),
      GameIdAndRank(200078, TWO)
        to GameRankOfferModifiers("game_ranks/atlantis_bounce_rank_2.jpg", "\$_atlantis_bounce_text_install_bottom_rank_rules"),
      GameIdAndRank(200078, THREE)
        to GameRankOfferModifiers("game_ranks/atlantis_bounce_rank_3.jpg", "\$_atlantis_bounce_text_install_bottom_rank_rules"),
      // solitaire classic
      GameIdAndRank(200079, ZERO)
        to GameRankOfferModifiers("game_ranks/solitaire_classic_rank_0.jpg", "\$_solitaire_classic_text_install_bottom_rank_rules"),
      GameIdAndRank(200079, ONE)
        to GameRankOfferModifiers("game_ranks/solitaire_classic_rank_1.jpg", "\$_solitaire_classic_text_install_bottom_rank_rules"),
      GameIdAndRank(200079, TWO)
        to GameRankOfferModifiers("game_ranks/solitaire_classic_rank_2.jpg", "\$_solitaire_classic_text_install_bottom_rank_rules"),
      GameIdAndRank(200079, THREE)
        to GameRankOfferModifiers("game_ranks/solitaire_classic_rank_3.jpg", "\$_solitaire_classic_text_install_bottom_rank_rules"),
      // slice puzzle
      GameIdAndRank(200086, ZERO)
        to GameRankOfferModifiers("game_ranks/slice_puzzle_rank_0.jpg", "\$_slice_puzzle_text_install_bottom_rank_rules"),
      GameIdAndRank(200086, ONE)
        to GameRankOfferModifiers("game_ranks/slice_puzzle_rank_1.jpg", "\$_slice_puzzle_text_install_bottom_rank_rules"),
      GameIdAndRank(200086, TWO)
        to GameRankOfferModifiers("game_ranks/slice_puzzle_rank_2.jpg", "\$_slice_puzzle_text_install_bottom_rank_rules"),
      GameIdAndRank(200086, THREE)
        to GameRankOfferModifiers("game_ranks/slice_puzzle_rank_3.jpg", "\$_slice_puzzle_text_install_bottom_rank_rules"),
      // slide & roll
      GameIdAndRank(200087, ZERO)
        to GameRankOfferModifiers("game_ranks/slide_roll_rank_0.jpg", "\$_slide_roll_text_install_bottom_rank_rules"),
      GameIdAndRank(200087, ONE)
        to GameRankOfferModifiers("game_ranks/slide_roll_rank_1.jpg", "\$_slide_roll_text_install_bottom_rank_rules"),
      GameIdAndRank(200087, TWO)
        to GameRankOfferModifiers("game_ranks/slide_roll_rank_2.jpg", "\$_slide_roll_text_install_bottom_rank_rules"),
      GameIdAndRank(200087, THREE)
        to GameRankOfferModifiers("game_ranks/slide_roll_rank_3.jpg", "\$_slide_roll_text_install_bottom_rank_rules"),
    )
  }
}

enum class GameRank {
  ZERO, ONE, TWO, THREE
}

data class GameIdAndRank(
  val gameId: Int,
  val rank: GameRank,
)

data class GameRankOfferModifiers(
  val imageFilename: String,
  val infoTextInstallBottom: String,
)