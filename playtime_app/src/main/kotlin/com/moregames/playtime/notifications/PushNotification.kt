package com.moregames.playtime.notifications

import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.variations.AndroidCashout2xOfferVariation
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.PushNotificationAnalyticalLabel.*
import com.moregames.playtime.user.cashout.dto.CashoutTransactionDto
import com.moregames.playtime.user.gamerank.GameRank
import java.time.Instant
import java.util.*

sealed interface PushNotification {
  val userId: String
  val label: String

  fun toEffect() = PushNotificationEffect(this)

  sealed interface AndroidPushNotification : PushNotification {

    data class FirstCoinsForGamePushNotification(
      override val userId: String, override val label: String = AFTER_FIRST_COINS_FOR_GAME.apiName, val gameName: String
    ) : AndroidPushNotification

    data class FirstCashoutOfDayNotification(
      override val userId: String, override val label: String = AFTER_FIRST_CASHOUT_OF_DAY.apiName
    ) : AndroidPushNotification

    data class XMinutesToCashoutPeriodEndNotification(
      override val userId: String, override val label: String = THIRTY_MINUTES_TO_CASHOUT.apiName
    ) : AndroidPushNotification

    data class ReachCoinGoalNotification(
      override val userId: String, override val label: String = REACH_COIN_GOAL.apiName, val coinGoalReached: Boolean
    ) : AndroidPushNotification

    data class MissedEarningsNotification(
      override val userId: String, override val label: String = MISSED_EARNINGS.apiName
    ) : AndroidPushNotification

    data class OnOfferwallNowAllowedNotification(
      override val userId: String, override val label: String = OFFERWALL_NOW_ALLOWED.apiName
    ) : AndroidPushNotification

    data class InactivityReminder(
      override val userId: String, val userFirstName: String?, val notificationConfig: UserNotificationConfig
    ) : AndroidPushNotification {
      override val label: String = INACTIVITY_REMINDER.apiName + notificationConfig.id
    }

    data class CashoutFailedNotification(
      override val userId: String, override val label: String = CASHOUT_FAILED.apiName, val cashoutTransaction: CashoutTransactionDto
    ) : AndroidPushNotification

    data class WelcomeCoinsOfferAvailableNotification(
      override val userId: String, override val label: String = WELCOME_COINS_OFFER_AVAILABLE.apiName
    ) : AndroidPushNotification

    data class GooglePlayOpenedNotification(
      override val userId: String, override val label: String = GOOGLE_PLAY_OPENED.apiName, val gameName: String
    ) : AndroidPushNotification

    data class SurveyNotification(
      override val userId: String, override val label: String = ANDROID_SURVEY.apiName, val surveyId: String
    ) : AndroidPushNotification

    data class GameUnlockedNotification(
      override val userId: String, override val label: String = GAME_UNLOCKED.apiName, val gameName: String, val widgetId: String
    ) : AndroidPushNotification

    data class RemindToPlayNotification(
      override val userId: String, override val label: String = REMIND_TO_PLAY.apiName, val title: String, val description: String
    ) : AndroidPushNotification

    data class ContinueIncompleteCashoutNotification(
      override val userId: String, override val label: String = CONTINUE_INCOMPLETE_CASHOUT.apiName, val earnings: UserCurrencyEarnings
    ) : AndroidPushNotification

    data class InstallGameReminder(
      override val userId: String,
      override val label: String = INSTALL_GAME_REMINDER.apiName,
      val title: String,
      val text: String,
      val icon: String?,
      val image: String?,
      val onClickAction: OnClickActionApiDto,
      val buttonAction: ButtonAction,
    ) : AndroidPushNotification

    data class RewardEarningsAddedNotification(
      override val userId: String,
      val earnings: UserCurrencyEarnings,
      val locale: Locale,
    ) : AndroidPushNotification {
      override val label: String = REWARD_EARNINGS_ADDED.apiName
    }

    data class RewardSpecialChallengesEarningsAddedNotification(
      override val userId: String,
      val earnings: UserCurrencyEarnings,
      val locale: Locale,
    ) : AndroidPushNotification {
      override val label: String = SPECIAL_CHALLENGE_EARNINGS_ADDED.apiName
    }

    data class Cashout2xOfferActivatedNotification(
      override val userId: String,
      override val label: String = CASHOUT_X2_OFFER_ACCEPTED.apiName,
      val offerDuration: Long,
      val variation: AndroidCashout2xOfferVariation
    ) : AndroidPushNotification

    data class LuckyHourNotification(
      override val userId: String, val endTime: Instant? = null, override val label: String = LUCKY_HOUR.apiName,
    ) : AndroidPushNotification

    data class CashoutOfferStarted(override val userId: String, val gameName: String) : AndroidPushNotification {
      override val label: String
        get() = CASHOUT_OFFER_ACTIVATED.apiName
    }

    data class CashoutOfferBalanceUpdate(override val userId: String, val coinsBalance: Long, val hideCoins: Boolean) : AndroidPushNotification {
      override val label: String
        get() = SPECIAL_CASHOUT_OFFERS_EXP_BALANCE_UPDATED.apiName
    }

    data class ChallengeCompletedNotification(
      override val userId: String,
      val locale: Locale,
    ) : AndroidPushNotification {
      override val label: String
        get() = CHALLENGE_COMPLETED.apiName
    }

    data class ChallengeSpecialOfferActivatedNotification(
      override val userId: String,
      val offerDuration: Long,
    ) : AndroidPushNotification {
      override val label: String
        get() = CHALLENGE_CLAIM_BOOSTED_MODE_OFFER_ACTIVATED.apiName
    }

    data class SpecialChallengeCompletedNotification(
      override val userId: String,
      val locale: Locale,
    ) : AndroidPushNotification {
      override val label: String
        get() = SPECIAL_CHALLENGE_COMPLETED.apiName
    }

    data class SpecialChallengeClaimedNotification(
      override val userId: String,
      val locale: Locale,
    ) : AndroidPushNotification {
      override val label: String
        get() = SPECIAL_CHALLENGE_CLAIMED.apiName
    }

    data class KeepDoingChallengesNotification(
      override val userId: String,
      val title: String,
      val body: String,
    ) : AndroidPushNotification {
      override val label: String
        get() = KEEP_DOING_CHALLENGES.apiName
    }

    data class DayStreakRewardReadyNotification(override val userId: String, override val label: String = DAY_STREAK_REWARD_READY.apiName) :
      AndroidPushNotification

    data class GameRankUpdatedNotification(
      override val userId: String,
      val gameName: String,
      val updatedRank: GameRank,
      override val label: String = GAME_RANK_UPDATED.apiName
    ) : AndroidPushNotification
  }

  sealed interface IosPushNotification : PushNotification {

    data class PlayFirstGameReminder(
      override val userId: String, override val label: String = PLAY_FIRST_GAME.apiName
    ) : IosPushNotification

    data class ShareYourExperienceNotification(
      override val userId: String, override val label: String = SHARE_YOUR_EXPERIENCE.apiName
    ) : IosPushNotification
  }

  sealed interface CrossPlatformPushNotification : PushNotification {

    data class BalanceUpdatedNotification(
      val userIdParam: String,
      val coins: Long,
      val coinsAdded: Long = 0,
      val hideCoins: Boolean = false,
      val isIosBoostedGameCoins: Boolean = false
    ) : CrossPlatformPushNotification {
      override val userId: String
        get() = userIdParam
      override val label: String
        get() = BALANCE_UPDATED.apiName
    }

    data class CashoutProcessedNotification(
      override val userId: String, override val label: String = CASHOUT_PROCESSED.apiName, val cashoutTransaction: CashoutTransactionDto
    ) : CrossPlatformPushNotification

    data class EarningsAddedNotification(
      override val userId: String, override val label: String = EARNINGS_ADDED.apiName, val earnings: UserCurrencyEarnings, val userHasCashouts: Boolean
    ) : CrossPlatformPushNotification

    data class RatingPromptCommand(
      override val userId: String, override val label: String = RATING_PROMPT.apiName
    ) : CrossPlatformPushNotification

    data class GameCoinGoalReachedNotification(
      override val userId: String,
      override val label: String = GAME_COIN_GOAL_REACHED.apiName,
      val text: String,
      val title: String,
    ) : CrossPlatformPushNotification

    data class UnclaimedEarningNotification(
      override val userId: String, val title: String, val text: String, override val label: String = EARNINGS_ADDED.apiName
    ) : CrossPlatformPushNotification
  }
}

enum class PushNotificationAnalyticalLabel(val apiName: String) {
  AFTER_FIRST_COINS_FOR_GAME("after_first_coins_for_game"),
  AFTER_FIRST_CASHOUT_OF_DAY("after_first_cashout_of_day"),
  THIRTY_MINUTES_TO_CASHOUT("30min_to_cashout"),
  REACH_COIN_GOAL("reach_coin_goal"),
  MISSED_EARNINGS("missed_earnings"),
  INACTIVITY_REMINDER("inactivity_reminder_"),
  PLAY_FIRST_GAME("play_first_game"),
  BALANCE_UPDATED("balance_updated"),
  CASHOUT_FAILED("cashout_failed"),
  CASHOUT_PROCESSED("cashout_processed"),
  SHARE_YOUR_EXPERIENCE("share_your_experience"),
  EARNINGS_ADDED("earnings_added"),
  REWARD_EARNINGS_ADDED("reward_earnings_added"),
  CASHOUT_X2_OFFER_ACCEPTED("cashout_x2_offer_accepted"),
  LUCKY_HOUR("lucky_hour"),
  RATING_PROMPT("rating_prompt"),
  WELCOME_COINS_OFFER_AVAILABLE("welcome_coins_offer_available"),
  GOOGLE_PLAY_OPENED("google_play_opened"),
  ANDROID_SURVEY("android_survey"),
  GAME_UNLOCKED("game_unlocked"),
  REMIND_TO_PLAY("remind_to_play"),
  GAME_COIN_GOAL_REACHED("game_coin_goal_reached"),
  CONTINUE_INCOMPLETE_CASHOUT("continue_incomplete_cashout"),
  INSTALL_GAME_REMINDER("install_game_reminder"),
  MASS_NOTIFICATION("mass_notification"),
  QA_NOTIFICATION("qa_notification"),
  CASHOUT_OFFER_ACTIVATED("cashout_offer_activated"),
  KEEP_DOING_CHALLENGES("keep-doing-challenges"),
  CHALLENGE_COMPLETED("challenge-completed"),
  SPECIAL_CHALLENGE_COMPLETED("special-challenge-completed"),
  SPECIAL_CHALLENGE_CLAIMED("special-challenge-claimed"),
  SPECIAL_CHALLENGE_EARNINGS_ADDED("special-challenge-earnings-added"),
  SPECIAL_CASHOUT_OFFERS_EXP_BALANCE_UPDATED("specialCashoutOffers_exp_balance_updated"),
  DONT_LOSE_YOUR_STREAK_REMINDER("dont_lose_your_streak_reminder"),
  DAY_STREAK_REWARD_READY("day_streak_reward_ready"),
  OFFERWALL_NOW_ALLOWED("offerwall_now_allowed"),
  CHALLENGE_CLAIM_BOOSTED_MODE_OFFER_ACTIVATED("challenge_claim_boosted_mode_offer_activated"),
  GAME_RANK_UPDATED("game_rank_updated"),
}