package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents.AddUserCoinsMessage.*
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents.AddUserCoinsMessage.DataCase.*
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.additionalOfferCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.offerWallCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.userGameCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.isEm3
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.fromProto
import java.math.BigDecimal

class AddUserCoinsMessageHandler @Inject constructor(
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService,
  private val abTestingFacade: AbTestingFacade,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val emExperimentBaseService: EmExperimentBaseService,
  private val messageBus: MessageBus,
) {

  @MessageHandler
  suspend fun handle(message: RewardingEvents.AddUserCoinsMessage) {
    val platform: AppPlatform = message.platform.fromProto()
    val trackVisibleCoins = abTestingFacade.isCoinsDoNotResetParticipant(message.userId, platform)
    val abInflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(message.userId)

    when (message.dataCase) {
      GAME_COINS -> processGameCoins(message.userId, platform, trackVisibleCoins, message.gameCoins, abInflatingCoinsMultiplier)
      ADDITIONAL_OFFER_COINS -> processAdditionalOfferCoins(message.userId, platform, trackVisibleCoins, message.additionalOfferCoins, abInflatingCoinsMultiplier)
      OFFER_WALL_COINS -> processOfferWallCoins(message.userId, platform, trackVisibleCoins, message.offerWallCoins, abInflatingCoinsMultiplier)
      null, DATA_NOT_SET -> {
        throw IllegalArgumentException("Unsupported add coins message type ${message.dataCase}")
      }
    }
  }

  private suspend fun processGameCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddGameCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    val coinsEarned = data.coinsEarned.fromProto()
    if (coinsEarned == BigDecimal.ZERO) return

    val em2Participation = abTestingFacade.getEm2Participation(userId)
    when {
      em2Participation is AbTestingService.IsExperimentParticipant.No ->
        userCurrentCoinsBalancePersistenceService.updateCurrentGamesCoinsBalance(userId, coinsEarned.toInt(), trackVisibleCoins)

      // em3 coins has its own flow, see com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
      em2Participation.isEm3() -> return

      else -> {
        userCurrentCoinsBalancePersistenceService.updateEm2CurrentGamesCoinsBalance(userId, coinsEarned, trackVisibleCoins)
      }
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = (coinsEarned * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
        this.gameCoinsData = userGameCoins {
          this.forceCommandNotification = data.forceCommandNotification
          this.gameId = data.gameId
          if (data.hasCommandId()) {
            this.commandId = data.commandId
          }
        }
      }
    )
  }

  private suspend fun processAdditionalOfferCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddAdditionalOfferCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    val coinsAdded: Long
    if (abTestingFacade.isEm2Participant(userId)) {
      userCurrentCoinsBalancePersistenceService.updateEm2AdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto(), trackVisibleCoins)
      coinsAdded = (data.coinsEarned.fromProto() * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
    } else {
      userCurrentCoinsBalancePersistenceService.updateAdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto().toInt(), data.nonInflatedCoinsEarned.value, trackVisibleCoins)
      coinsAdded = data.coinsEarned.fromProto().toLong()
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = coinsAdded
        this.additionalOfferCoinsData = additionalOfferCoins {
          this.offerId = data.offerId
        }
      }
    )
  }

  private suspend fun processOfferWallCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddOfferWallCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    val coinsAdded: Long
    if (abTestingFacade.isEm2Participant(userId)) {
      userCurrentCoinsBalancePersistenceService.updateEm2AdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto(), trackVisibleCoins)
      coinsAdded = (data.coinsEarned.fromProto() * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
    } else {
      userCurrentCoinsBalancePersistenceService.updateAdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto().toInt(), data.nonInflatedCoinsEarned.value, trackVisibleCoins)
      coinsAdded = data.coinsEarned.fromProto().toLong()
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = coinsAdded
        this.offerWallCoinsData = offerWallCoins {  }
      }
    )
  }
}