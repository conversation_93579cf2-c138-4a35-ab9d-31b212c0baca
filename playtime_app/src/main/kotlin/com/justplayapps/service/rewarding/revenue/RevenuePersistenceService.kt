package com.justplayapps.service.rewarding.revenue

import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.service.rewarding.earnings.table.*
import com.moregames.base.applovin.JP_VIDEO_AD_ITEM_IDS
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.table.CurrentGenericRevenueTable
import com.moregames.base.table.GenericRevenueTotalsTable
import com.moregames.base.table.UserApplovinRevenueByPeriodsTable
import com.moregames.base.table.UserTable
import com.moregames.base.user.FakeMetaEarnings
import com.moregames.base.user.RevenueTotals
import com.moregames.base.util.*
import com.moregames.playtime.revenue.applovin.dto.ApplovinRevenue
import com.moregames.playtime.revenue.applovin.dto.MetaApplovinRevenue
import com.moregames.playtime.revenue.applovin.exception.NoApplovinRevenuesAvailableException
import com.moregames.playtime.revenue.applovin.table.MetaReportedApplovinRevenuesTable
import com.moregames.playtime.revenue.applovin.table.ReportedApplovinRevenuesTable
import com.moregames.playtime.util.useWithCommitAndRollback
import kotlinx.coroutines.withContext
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.`java-time`.date
import org.jetbrains.exposed.sql.transactions.TransactionManager
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.math.BigDecimal
import java.sql.Connection
import java.time.*
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton

@Singleton
class RevenuePersistenceService @Inject constructor(
  private val database: Database,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val timeService: TimeService
) : BasePersistenceService(database) {

  companion object {
    const val DAYS_TO_STORE_DAILY_REVENUE_TOTALS = 180L
  }

  @Deprecated("moved to reporting")
  suspend fun loadLatestMetaApplovinRevenue(applicationId: String) =
    dbQuery {
      MetaReportedApplovinRevenuesTable
        .select { MetaReportedApplovinRevenuesTable.applicationId eq applicationId }
        .orderBy(MetaReportedApplovinRevenuesTable.day, SortOrder.DESC)
        .limit(1)
        .map {
          MetaApplovinRevenue(
            it[MetaReportedApplovinRevenuesTable.id],
            it[MetaReportedApplovinRevenuesTable.applicationId],
            it[MetaReportedApplovinRevenuesTable.day],
            it[MetaReportedApplovinRevenuesTable.recordsLoaded]
          )
        }
        .firstOrNull()
    }

  @Deprecated("moved to reporting")
  suspend fun saveMetaApplovinRevenues(data: MetaApplovinRevenue) =
    dbQuery {
      MetaReportedApplovinRevenuesTable
        .insert {
          it[applicationId] = data.applicationId
          it[day] = data.day
          it[recordsLoaded] = data.recordsLoaded
        }
    }

  suspend fun saveApplovinRevenues(revenues: List<ApplovinRevenue>, importDay: LocalDate, applicationId: String) =
    withContext(coroutineScope.get().coroutineContext) {
      val file = File.createTempFile("applovin-import", null)

      CSVPrinter(BufferedWriter(FileWriter(file)), CSVFormat.DEFAULT.withAutoFlush(true)).use { printer ->
        revenues.forEach { printer.printRecord(it.adUnitId, it.googleAdId, it.revenueUsd, importDay, applicationId) }
      }

      val sql = "" +
        "LOAD DATA LOCAL INFILE '${file.absolutePath.replace('\\', '/')}' " +
        "INTO TABLE ${ReportedApplovinRevenuesTable.tableName} " +
        "FIELDS TERMINATED BY ',' " +
        "LINES TERMINATED BY '\\r\\n'" +
        "(${ReportedApplovinRevenuesTable.adUnitId.name}, ${ReportedApplovinRevenuesTable.googleAdId.name}, ${ReportedApplovinRevenuesTable.revenueUsd.name}, @var1, ${ReportedApplovinRevenuesTable.applicationId.name})" +
        "set ${ReportedApplovinRevenuesTable.day.name} = STR_TO_DATE(@var1, '%Y-%m-%d')"
      (database.connector().connection as Connection).useWithCommitAndRollback { conn ->
        conn.createStatement().use {
          try {
            it.execute(sql)
          } finally {
            file.delete()
          }
        }
      }
    }

  @Throws(NoApplovinRevenuesAvailableException::class)
  suspend fun transferApplovinRevenuesFromExternal() {
    cronLogger().info("Transferring applovin revenues. Start")
    var insertedTotal = 0
    val validUUIDChars = ('0'..'9').map { it.toString() } + ('a'..'f').map { it.toString() }
    validUUIDChars.forEach { validUUIDChar ->
      cronLogger().info("Transferring applovin revenues. Transferring data for users id starting with $validUUIDChar")
      dbQuery {
        val reportedApplovinRevenuesQuery = ReportedApplovinRevenuesTable
          .join(
            UserTable,
            JoinType.INNER,
            ReportedApplovinRevenuesTable.googleAdId,
            UserTable.googleAdId
          )
          .slice(
            UserTable.id,
            ReportedApplovinRevenuesTable.revenueUsd,
            ReportedApplovinRevenuesTable.adUnitId,
            ReportedApplovinRevenuesTable.day,
            ReportedApplovinRevenuesTable.applicationId
          )
          .select {
            (UserTable.id.like("$validUUIDChar%")) and
              (UserTable.isDeleted eq false) and
              (UserTable.createdAt.date().lessEq(ReportedApplovinRevenuesTable.day.date()))
          }
        val inserted = ApplovinRevenuesTable
          .insert(
            reportedApplovinRevenuesQuery,
            listOf(
              ApplovinRevenuesTable.userId,
              ApplovinRevenuesTable.revenueUsd,
              ApplovinRevenuesTable.adUnitId,
              ApplovinRevenuesTable.day,
              ApplovinRevenuesTable.applicationId
            )
          )
        insertedTotal += (inserted ?: 0)
        cronLogger().info("Transferring applovin revenues. Transferred $inserted applovin revenues")
      }
    }
    if (insertedTotal < 1)
      throw NoApplovinRevenuesAvailableException()
    cronLogger().info("Transferring applovin revenues. Total rows transferred $insertedTotal")
    cronLogger().info("Transferring applovin revenues. Deleting all data from external.reported_applovin_revenues")
    dbQuery {
      TransactionManager.current().exec("TRUNCATE TABLE ${ReportedApplovinRevenuesTable.tableName}")
    }
    cronLogger().info("Transferring applovin revenues. Transferring applovin revenues completed")
  }

  suspend fun getRevenueTotals(userId: String): RevenueTotals? =
    dbQuery {
      GenericRevenueTotalsTable
        .slice(
          GenericRevenueTotalsTable.revenueAmount,
          GenericRevenueTotalsTable.offerwallRevenue,
          GenericRevenueTotalsTable.day2Revenue,
          GenericRevenueTotalsTable.day0Revenue
        )
        .select { GenericRevenueTotalsTable.userId eq userId }
        .firstOrNull()?.let {
          RevenueTotals(
            revenue = it[GenericRevenueTotalsTable.revenueAmount],
            offerwallRevenue = it[GenericRevenueTotalsTable.offerwallRevenue],
            day2Revenue = it[GenericRevenueTotalsTable.day2Revenue],
            day0Revenue = it[GenericRevenueTotalsTable.day0Revenue]
          )
        }
    }

  suspend fun loadImportedApplovinApplicationIds() =
    dbQuery {
      ReportedApplovinRevenuesTable
        .slice(ReportedApplovinRevenuesTable.applicationId)
        .selectAll()
        .withDistinct(true)
        .map { it[ReportedApplovinRevenuesTable.applicationId] }
        .toSet()
    }

  suspend fun loadApplovinRevenueByApplicationId(day: LocalDate) =
    dbQuery {
      ApplovinRevenuesTable
        .slice(ApplovinRevenuesTable.applicationId, ApplovinRevenuesTable.revenueUsd.sum())
        .select { ApplovinRevenuesTable.day eq day }
        .groupBy(ApplovinRevenuesTable.applicationId)
        .associate { it[ApplovinRevenuesTable.applicationId] to (it[ApplovinRevenuesTable.revenueUsd.sum()] ?: BigDecimal.ZERO) }
    }

  suspend fun getLatestRevenueTime(userId: String): Instant? = dbQuery {
    GenericRevenueTotalsTable
      .slice(GenericRevenueTotalsTable.updatedAt)
      .select {
        (GenericRevenueTotalsTable.userId eq userId) and
          (GenericRevenueTotalsTable.revenueAmount greater BigDecimal.ZERO)
      }
      .firstOrNull()
      ?.get(GenericRevenueTotalsTable.updatedAt)
  }

  suspend fun resetCurrentRevenue(userId: String) = dbQuery {
    CurrentGenericRevenueTable.update({
      (CurrentGenericRevenueTable.userId eq userId) and
        (CurrentGenericRevenueTable.metaUserEarningsId.isNull())
    })
    {
      it[metaUserEarningsId] = FakeMetaEarnings.FAKE_META_ID_REVENUE_RESET.id
    }
  }

  suspend fun createZeroRevenue(userId: String) = dbQuery {
    GenericRevenueTotalsTable.insert {
      it[GenericRevenueTotalsTable.userId] = userId
      it[revenueAmount] = BigDecimal.ZERO
    }
  }

  suspend fun removeDailyRevenueOldTotalsBatch(batchSize: Int): Int = dbQuery {
    val longAgo = timeService.now().minus(DAYS_TO_STORE_DAILY_REVENUE_TOTALS, ChronoUnit.DAYS).localUtcDate()
    GenericRevenueDailyTotalsMk2Table
      .deleteWhere(limit = batchSize) {
        GenericRevenueDailyTotalsMk2Table.day less longAgo
      }
  }

  suspend fun getApplovinInterRevenue(userId: String, from: Instant, to: Instant): BigDecimal = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq from) and
          (UserApplovinRevenueByPeriodsTable.periodStart less to)
      }
      .firstOrNull()
      ?.let { it[UserApplovinRevenueByPeriodsTable.revenue.sum()] }
      ?: BigDecimal.ZERO
  }

  suspend fun getApplovinInterRevenue(userId: String, after: Instant): BigDecimal = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq after) and
          (UserApplovinRevenueByPeriodsTable.gameId notInList JP_VIDEO_AD_ITEM_IDS)
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.userId)
      .firstOrNull()
      ?.let { it[UserApplovinRevenueByPeriodsTable.revenue.sum()] }
      ?: BigDecimal.ZERO
  }

  suspend fun getUserGameRevenueByPeriod(userId: String, periodStart: Instant, periodEnd: Instant): BigDecimal = dbQuery {
    CurrentGenericRevenueTable
      .slice(CurrentGenericRevenueTable.revenueAmount.sum())
      .select {
        (CurrentGenericRevenueTable.userId eq userId) and
          (CurrentGenericRevenueTable.timestamp greaterEq periodStart) and
          (CurrentGenericRevenueTable.timestamp less periodEnd) and
          (CurrentGenericRevenueTable.gameId notInList JP_VIDEO_AD_ITEM_IDS) and
          (CurrentGenericRevenueTable.revenueSource eq RevenueReceivedEventDto.RevenueSource.APPLOVIN.name)
      }
      .firstOrNull()
      ?.let { it.getOrNull(CurrentGenericRevenueTable.revenueAmount.sum()) ?: BigDecimal.ZERO }
      ?: BigDecimal.ZERO
  }

  suspend fun getGameRevenueByPeriod(periodStart: Instant, periodEnd: Instant): BigDecimal = dbQuery {
    CurrentGenericRevenueTable
      .slice(CurrentGenericRevenueTable.revenueAmount.sum())
      .select {
        (CurrentGenericRevenueTable.timestamp greaterEq periodStart) and
          (CurrentGenericRevenueTable.timestamp less periodEnd) and
          (CurrentGenericRevenueTable.gameId notInList JP_VIDEO_AD_ITEM_IDS) and
          (CurrentGenericRevenueTable.revenueSource eq RevenueReceivedEventDto.RevenueSource.APPLOVIN.name)
      }
      .firstOrNull()
      ?.getOrNull(CurrentGenericRevenueTable.revenueAmount.sum())
      ?: BigDecimal.ZERO
  }

  suspend fun getApplovinRevenue5minPeriodsCount(userId: String, since: Instant) = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.gameId, UserApplovinRevenueByPeriodsTable.periodStart.count())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq since.toBeginningOf5MinInterval())
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.gameId)
      .associate {
        it[UserApplovinRevenueByPeriodsTable.gameId] to it[UserApplovinRevenueByPeriodsTable.periodStart.count()]
      }
  }

  suspend fun getUserPerGameRevenue(userId: String) = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.gameId, UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.gameId notInList JP_VIDEO_AD_ITEM_IDS)
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.gameId)
      .map {
        it.toUserGameRevenue()
      }
  }

  suspend fun getUserRevenueAfter(userId: String, afterDay: LocalDate) = dbQuery {
    GenericRevenueDailyTotalsMk2Table
      .slice(GenericRevenueDailyTotalsMk2Table.revenueAmount.sum())
      .select {
        (GenericRevenueDailyTotalsMk2Table.userId eq userId) and
          (GenericRevenueDailyTotalsMk2Table.day greaterEq afterDay)
      }
      .singleOrNull()
      ?.get(GenericRevenueDailyTotalsMk2Table.revenueAmount.sum()) ?: BigDecimal.ZERO
  }

  private fun ResultRow.toUserGameRevenue() =
    UserGameRevenue(
      gameId = this[UserApplovinRevenueByPeriodsTable.gameId],
      revenue = this[UserApplovinRevenueByPeriodsTable.revenue.sum()] ?: BigDecimal.ZERO
    )

  data class UserGameRevenue(
    val gameId: Int,
    val revenue: BigDecimal
  )

}
