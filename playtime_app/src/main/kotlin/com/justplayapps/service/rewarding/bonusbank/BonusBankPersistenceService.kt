package com.justplayapps.service.rewarding.bonusbank

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.insertOrUpdate
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.select
import javax.inject.Singleton

@Singleton
class BonusBankPersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun save(bank: BonusBank) = dbQuery {
    BonusBankTable.insertOrUpdate(BonusBankTable.bankBalance, BonusBankTable.lastClaimedBalance) {
      it[userId] = bank.userId
      it[bankBalance] = bank.bankBalance
      it[lastClaimedBalance] = bank.lastClaimedBalance
    }
  }

  suspend fun get(userId: String): BonusBank = dbQuery {
    BonusBankTable
      .slice(BonusBankTable.userId, BonusBankTable.bankBalance, BonusBankTable.lastClaimedBalance)
      .select(BonusBankTable.userId eq userId)
      .map { BonusBank(
        userId = it[BonusBankTable.userId],
        bankBalance = it[BonusBankTable.bankBalance],
        lastClaimedBalance = it[BonusBankTable.lastClaimedBalance]
      )}
      .firstOrNull()
      ?: BonusBank(
        userId = userId,
        bankBalance = 0.toBigDecimal(),
        lastClaimedBalance = 0.toBigDecimal(),
      )
  }
}