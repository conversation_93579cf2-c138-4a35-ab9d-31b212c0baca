<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:jp="http://www.liquibase.org/xml/ns/dbchangelog-ext/justplay"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
>
    <changeSet id="add-ios-online-users-exp" author="vitalii.sirotkin"
            labels="https://app.asana.com/0/1155692811605665/1206820454573665/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosPlayersOnline', '99');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosPlayersOnline'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelCenterGrey', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosPlayersOnline'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelCenterLongBlue', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosPlayersOnline'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="exp-custom-balance-notification-additions" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206766681698725/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id) VALUES
            (0.0, 'customBalanceKeepGoing', (SELECT id FROM playtime.ab_experiments WHERE `key`='customBalanceNotification')),
            (0.0, 'customBalanceCoinBoost', (SELECT id FROM playtime.ab_experiments WHERE `key`='customBalanceNotification'));
        </sql>
    </changeSet>

    <changeSet id="cashout-revenue-tracking-table" author="cip.malos"
               labels="https://app.asana.com/0/1206535341612306/1206715716543113">
        <createTable tableName="earnings_calculation_data" schemaName="playtime">
            <column name="meta_user_earnings_id" type="INT">
                <constraints foreignKeyName="fk_generic_revenue_meta_user_earnings_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="meta_user_earnings"
                             referencedColumnNames="id"
                             primaryKey="true"
                             nullable="false"
                />
            </column>
            <column name="revenue" type="DECIMAL(16,12)">
                <constraints nullable="false"/>
            </column>
            <column name="offerwall_revenue" type="DECIMAL(16,12)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>

        </createTable>
    </changeSet>

    <changeSet id="update-lat-games-list-2024-03-27" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206932157890244/f">
        <sql>
            UPDATE playtime.games
            SET show_for_lat = case
            when application_id in ('com.gimica.hexapuzzlefun', 'com.gimica.ballbounce',
                'com.gimica.blockholeclash', 'com.gimica.blockslider',
                'com.gimica.brickdoku', 'com.gimica.bubblepop',
                'com.gimica.carsmerge', 'com.gimica.colorlogic',
                'com.gimica.crystalcrush', 'com.gimica.emojiclickers',
                'com.gimica.hexmatch', 'com.gimica.madsmash',
                'com.gimica.maketen', 'com.gimica.marblemadness',
                'com.gimica.mergeblast', 'com.gimica.mixblox',
                'com.gimica.puzzlepopblaster', 'com.gimica.solitaireverse',
                'com.gimica.spaceconnect', 'com.gimica.helixdash',
                'com.gimica.sudoku', 'com.gimica.sugarmatch',
                'com.gimica.tilematchpro', 'com.gimica.treasuremaster',
                'com.gimica.triviamadness', 'com.gimica.watersorter',
                'com.gimica.zentiles', 'com.gimica.wordkitchen',
                'com.gimica.wordseeker') then true
            else false
            end
            where platform = 'ANDROID';
        </sql>
    </changeSet>

    <changeSet id="add-ios-claim-button-exp" author="vitalii.sirotkin"
            labels="https://app.asana.com/0/1155692811605665/1206764732770310/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosClaimButton', '99');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosClaimButton'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('cashout', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosClaimButton'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add-ios-main-screen-exp" author="vitalii.sirotkin"
            labels="https://app.asana.com/0/1155692811605665/1206809895837267/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosMainScreenTimer', '99');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosMainScreenTimer'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('showTimer', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosMainScreenTimer'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add-ios-best-coin-badge-exp" author="vitalii.sirotkin"
            labels="https://app.asana.com/0/1155692811605665/1206824763983004/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosBestCoinsBadge', '99');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosBestCoinsBadge'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('showBadge', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosBestCoinsBadge'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="em2-add-randomness-to-coins" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1206709097957331/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id) VALUES
            (0.0, 'em2RndToEachCoinTransaction', (SELECT id FROM playtime.ab_experiments WHERE `key`='earningsModelV2')),
            (0.0, 'em2Rnd1_3onRnd11thTransaction', (SELECT id FROM playtime.ab_experiments WHERE `key`='earningsModelV2')),
            (0.0, 'em2RndToEachAndRnd1_3onRnd11thTransaction', (SELECT id FROM playtime.ab_experiments WHERE `key`='earningsModelV2'));
        </sql>
    </changeSet>

    <changeSet id="missed-games-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206940138658765/f">
        <sqlFile path="sql/missing_game_translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="turn-off-iosCashoutV2-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206989697590919/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('iosCashoutV2')
                AND started_at is not NULL AND finished_at is NULL </where>
        </update>
    </changeSet>

    <changeSet id="exp-tutorial-explore-now-with-offer" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206999555992451/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id) VALUES
            (0.0, 'tutorialExploreNowWithOffer', (SELECT id FROM playtime.ab_experiments WHERE `key`='tutorialExploreNow'));
        </sql>
    </changeSet>

    <changeSet id="update-reward-link-for-brazil" author="cip.malos"
               labels="https://app.asana.com/0/home/<USER>/1207001367208810"
               context="latam or latam-test or unit-test">
        <sqlFile path="sql/payment-providers-reward-link-brazil.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="highlight-paypal-experiment-more-variations" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1206840439902270/f">
        <sql>
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES
            ('noMinThenOneUSDPaypal', (SELECT id FROM playtime.ab_experiments WHERE `key`='highlightPaypal'), 0.0),
            ('oneUSDPaypal', (SELECT id FROM playtime.ab_experiments WHERE `key`='highlightPaypal'), 0.0),
            ('onlyPaypalMinOneUsd', (SELECT id FROM playtime.ab_experiments WHERE `key`='highlightPaypal'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="latam-translations-preparing-remove-obsolete-app-keys" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206635999061303/f">
        <sql>
            delete from playtime.app_translation
            where resource_name in (
            'daily_balance_title',
            'dialogCashoutBonusThresholdNotAchievedNew',
            'dialogRateButtonLater',
            'dialogRateButtonNo',
            'dialogRateButtonYes',
            'dialogRateText',
            'dialogRateTitle',
            'errorMessageFacebookLoginFailed',
            'notifications_permission_fullscreen_button_text',
            'notifications_permission_fullscreen_description',
            'notifications_permission_fullscreen_title',
            'locked_by_games',
            'locked_by_time',
            'unlock_game_now');
        </sql>
    </changeSet>

    <changeSet id="ios-app-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203947511126261/f">
        <sqlFile path="sql/ios-app-translations-initial.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ios-be-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203947511126261/f">
        <sqlFile path="sql/ios-be-translations-initial.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ios-news-table" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203947511126261/f">
        <createTable schemaName="playtime" tableName="ios_news">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_ios_news"/>
            </column>
            <column name="title" type="VARCHAR(100)">
                <constraints nullable="false" />
            </column>
            <column name="text" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="image" type="VARCHAR(100)"/>
            <column name="order_key" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="fill-ios-news-table" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203947511126261/f">
        <sqlFile path="sql/ios-news-table-filling.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="custom-game-pages-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1206889228467924/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('customGamePagesV1', '48');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'customGamePagesV1'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('customPagesDefaultControl', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'customGamePagesV1'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('customPagesV1', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'customGamePagesV1'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="translations-2024-03-19" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206866923635137/f,
                       https://app.asana.com/0/1203013731295607/1206684768666827/f,
                       https://app.asana.com/0/1203013731295607/1203397982823900/f">
        <sqlFile path="sql/translations_2024_03_19_update.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-games-description-fix" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1205887055722864/f">
        <sqlFile path="sql/ios-games-description-fix.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="latam-be-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206635999061303/f">
        <sqlFile path="sql/latam-be-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="latam-app-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206635999061303/f">
        <sqlFile path="sql/latam-app-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id="em2-game-equalizing-2024-04-19-us" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206920104262260/f"
               context="us-test OR us OR us-staging OR us-staging-test">
        <sql>
            INSERT INTO playtime.game_equalizer_coeffs (application_id, multiplier)
            VALUES ('com.gimica.fairytalematch', 17) ON DUPLICATE KEY
            UPDATE multiplier =
            VALUES (multiplier);
        </sql>
    </changeSet>

    <changeSet id="em2-game-equalizing-2024-04-19-ios-us" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206920104262260/f"
               context="ios-us-test OR ios-us">
        <sql>
            INSERT INTO playtime.game_equalizer_coeffs (application_id, multiplier)
            VALUES ('com.gimica.fairytalematch', 8) ON DUPLICATE KEY
            UPDATE multiplier =
            VALUES (multiplier);
        </sql>
    </changeSet>

    <changeSet id="show-ad-after-cashout-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1203293022323368/1207017527635556/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosShowAdAfterCashout', '100');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosShowAdAfterCashout'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('interstitial', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosShowAdAfterCashout'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('rewarded', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosShowAdAfterCashout'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-justplay-app-ad-unit-ids" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1203293022323368/1207017527635556/f"
               runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.game_ad_unit_ids
                WHERE package_id = 'com.justplay.app'
                AND app_platform = 'IOS';
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.game_ad_unit_ids
            (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES
            ('com.justplay.app', 'BANNER', '1cb72efd7a22989d', 1, 'IOS'),
            ('com.justplay.app', 'REWARDED', 'b5e4e6be1577e315', 1, 'IOS'),
            ('com.justplay.app', 'INTERSTITIAL', 'd1eef1ea993e5de0', 1, 'IOS'),
            ('com.justplay.app', 'BANNER', '1cb72efd7a22989d', 0, 'IOS'),
            ('com.justplay.app', 'REWARDED', '8d0b8c84fb033379', 0, 'IOS'),
            ('com.justplay.app', 'INTERSTITIAL', '15096c52d39651aa', 0, 'IOS');
        </sql>
    </changeSet>

    <changeSet id="playstore-tracking-notifications-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206940026377339/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('playstoreTrackingNotifications', '56');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'playstoreTrackingNotifications'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('playstoreTrackingNotifications', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'playstoreTrackingNotifications'), 0.0);
        </sql>

        <sqlFile path="sql/translations_2024_04_22_playstore_notification.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-be-email-validation-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207176099320442/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosBeEmailValidation', '101');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosBeEmailValidation'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('iosBeEmailValidation', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosBeEmailValidation'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-custom-game-pages-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206876190690189/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosCustomGamePagesV1', '102');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosCustomGamePagesV1'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('iosCustomPagesDefaultControl', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosCustomGamePagesV1'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('iosCustomPagesV1', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosCustomGamePagesV1'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add_encrypted_columns" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1206532536432378">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions ADD COLUMN encrypted_name TEXT;
            ALTER TABLE playtime.user_cashout_transactions ADD COLUMN encrypted_email VARCHAR(750);
            ALTER TABLE playtime.user_cashout_transactions ADD COLUMN encrypted_address TEXT;
            ALTER TABLE playtime.user_cashout_transactions ADD INDEX idx_user_cashout_transactions__encrypted_email (encrypted_email);

            ALTER TABLE playtime.blacklist_email ADD COLUMN encrypted_email VARCHAR(750);
            ALTER TABLE playtime.blacklist_email ADD INDEX idx_blacklist_email__encrypted_email (encrypted_email);

        </sql>
    </changeSet>

    <changeSet id="drop-lock-tables" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1155692811605665/1207192629730696/f"
               runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="playtime" tableName="user_game_lock"/>
        </preConditions>
        <dropTable tableName="user_game_lock" schemaName="playtime"/>
        <dropTable tableName="cfg_locked_games" schemaName="playtime"/>
        <dropForeignKeyConstraint baseTableName="games" baseTableSchemaName="playtime" constraintName="fk_games_game_genre_id"/>
        <dropTable tableName="user_selected_genre" schemaName="playtime"/>
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('lockedGames', 'genreSelection')
                AND started_at is not NULL AND finished_at is NULL
            </where>
        </update>
    </changeSet>

    <changeSet id="em2-coins-retention-by-rev-exp" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206889236457544/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (0.0, 'em2CoinsRetention', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2'));
        </sql>
    </changeSet>

    <changeSet id="venmo-user-handle-columns" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1206763624520993">
        <addColumn tableName="payment_providers" schemaName="playtime">
            <column name="identifier_type" type="varchar(20)">
                <constraints nullable="true"/>
            </column>
            <column name="identifier_hint" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="default-values-for-identifier-type-and-identifier-hint" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1206763624520993">
        <sql>
            UPDATE playtime.payment_providers
            SET identifier_type = 'user_handle'
            WHERE provider = 'venmo';
            UPDATE playtime.payment_providers
            SET identifier_type = 'email'
            WHERE provider != 'venmo';
            UPDATE playtime.payment_providers
            SET identifier_hint = 'Venmo Handle*'
            WHERE provider = 'venmo';
            UPDATE playtime.payment_providers
            SET identifier_hint = payment_providers.email_hint
            WHERE provider in ('paypal', 'amazon', 'tremendous_paypal', 'tremendous_amazon');
        </sql>
    </changeSet>

    <changeSet id="venmo-user-handle-column-for-cashout-transactions-table" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1206763624520993">
        <addColumn schemaName="playtime" tableName="user_cashout_transactions">
            <column name="user_handle" type="VARCHAR(30)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="user-temporal-restrictions-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207112053737871/f">
        <createTable schemaName="playtime" tableName="user_temporal_restrictions">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_temporal_restrictions_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="type" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="active_until" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_temporal_restrictions" columnNames="user_id,type"
                       constraintName="pk_user_temporal_restrictions"/>
    </changeSet>

    <changeSet id="ios-be-ja-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207089793728703/f">
        <sqlFile path="sql/ios-be-ja-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-one-click-cashout-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207172412180363/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidOneClickCashout', '57');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidOneClickCashout'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('confirmationOnly', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidOneClickCashout'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('oneClickOnly', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidOneClickCashout'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('confirmationAndOneClick', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidOneClickCashout'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="em2-double-coins-for-1st-cp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207022769960547/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES
            ('em2BoostCoinsX2On1stCp', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0),
            ('em2BoostCoinsX2On1stCpHigherLimits', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0),
            ('em2HigherLimitsV2', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add-reward-link-for-mexico" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1207244265717177"
               context="latam or latam-test or unit-test">
        <sqlFile path="sql/payment-providers-reward-link-mexico.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ios-email-hit-translation" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207089793728703/f">
        <sqlFile path="sql/ios-email-hit-translation.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="exp-relaxed-sim-fraud-policy" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207131424598119/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('relaxedSimFraudPolicy', null);
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'relaxedSimFraudPolicy')),
                   (0.0, 'relaxedSimCountryEuropean', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'relaxedSimFraudPolicy')),
                   (0.0, 'relaxedSimCountryWw', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'relaxedSimFraudPolicy'));
        </sql>
    </changeSet>

    <changeSet id="gps-verification-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1206876341313389/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidGpsVerification', '50');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidGpsVerification'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('active', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidGpsVerification'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('ofwRevOnly', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidGpsVerification'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('earnings10', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidGpsVerification'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('earnings20', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidGpsVerification'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="complete-genre-cleanup-column" author="vitalii.sirotkin" labels="https://app.asana.com/0/1155692811605665/1207192629730696/f">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="games" columnName="genre_id" schemaName="playtime"/>
        </preConditions>
        <dropColumn tableName="games" schemaName="playtime" columnName="genre_id"/>
    </changeSet>

    <changeSet id="complete-genre-cleanup-table" author="vitalii.sirotkin" labels="https://app.asana.com/0/1155692811605665/1207192629730696/f">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="game_genre" schemaName="playtime"/>
        </preConditions>
        <dropTable tableName="game_genre" schemaName="playtime"/>
    </changeSet>

    <changeSet id="playtime-to-games-tables-grants-2024-05-08" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206889236457544/f"
               context="!unit-test">
        <sql>
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`user_applovin_revenue_by_periods` TO `games` @`%`;
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`user_game_coins_balances_by_periods_em2` TO `games` @`%`;
        </sql>
    </changeSet>

    <changeSet id="sugar-rush-text-fix" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207274619485576/f">
        <sql>
            UPDATE playtime.string_resource_translation
            SET translation = 'Play Sugar Rush Adventure'
            WHERE resource_name = '$_sugar_rush_text_install_top'
            and language = 'en';
        </sql>
    </changeSet>

    <changeSet id="tangram-game--ad-unit-ids" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207334128577014/f"
               runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.game_ad_unit_ids
                WHERE package_id = 'com.gimica.tangram'
                AND app_platform = 'ANDROID';
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.game_ad_unit_ids
            (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES
            ('com.gimica.tangram', 'BANNER', 'ee28f0f718756c23', 1, 'ANDROID'),
            ('com.gimica.tangram', 'INTERSTITIAL', '226e42c1c5603b0e', 1, 'ANDROID'),
            ('com.gimica.tangram', 'REWARDED', 'af5c3ec7bc698e56', 1, 'ANDROID'),
            ('com.gimica.tangram', 'BANNER', 'aa3605384724fe6a', 0, 'ANDROID'),
            ('com.gimica.tangram', 'INTERSTITIAL', '71678bc1c2143182', 0, 'ANDROID'),
            ('com.gimica.tangram', 'REWARDED', '6a2980c9938eaa98', 0, 'ANDROID');
        </sql>
    </changeSet>

    <changeSet id="android-justplay-app-ad-unit-ids" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207171274378434/f">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.game_ad_unit_ids
                WHERE package_id = 'com.justplay.app'
                AND app_platform = 'ANDROID';
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.game_ad_unit_ids
            (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES
            ('com.justplay.app', 'BANNER', '1cb72efd7a22989d', 1, 'ANDROID'),
            ('com.justplay.app', 'REWARDED', '8b69d8e39d5b25a1', 1, 'ANDROID'),
            ('com.justplay.app', 'INTERSTITIAL', '8409660f06628c06 ', 1, 'ANDROID'),
            ('com.justplay.app', 'BANNER', '1cb72efd7a22989d', 0, 'ANDROID'),
            ('com.justplay.app', 'REWARDED', '8b69d8e39d5b25a1', 0, 'ANDROID'),
            ('com.justplay.app', 'INTERSTITIAL', '8409660f06628c06 ', 0, 'ANDROID');
        </sql>
    </changeSet>

    <changeSet id="show-ad-after-cashout-android-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207171274378434/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('showAdAfterCashoutAndroid', null);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'showAdAfterCashoutAndroid'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('interstitial', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'showAdAfterCashoutAndroid'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('rewarded', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'showAdAfterCashoutAndroid'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="exp-less-ads-games-on-boarding" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1199937798008521/1206876453963067/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('lessAdsGamesOnboarding', null);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'lessAdsGamesOnboarding')),
                   (0.0, 'lessAds3h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'lessAdsGamesOnboarding')),
                   (0.0, 'lessAds3hBoost50', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'lessAdsGamesOnboarding')),
                   (0.0, 'lessAds3hBoost100', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'lessAdsGamesOnboarding'));
        </sql>
    </changeSet>

    <changeSet id="android-justplay-app-ad-unit-ids-fix" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207171274378434/f">
        <sql>
            UPDATE playtime.game_ad_unit_ids
            SET ad_unit_id = '8409660f06628c06'
            WHERE package_id = 'com.justplay.app' and ad_type ='INTERSTITIAL' and ad_unit_id = '8409660f06628c06 '
        </sql>
    </changeSet>

    <changeSet id="updated-at-column-for-email-verifications" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1207336053122484">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="email_verification_result" columnName="updated_at"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="email_verification_result">
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ios-preselected-pp-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207299010722381/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosPreselectedPayPal', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPreselectedPayPal'), 1.0),
            ('limitedProviders', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPreselectedPayPal'), 0.0),
            ('preselectPaypal', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPreselectedPayPal'), 0.0),
            ('preselectPaypalSplitLogo', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPreselectedPayPal'),0.0),
            ('preselectPaypalAddArrow', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPreselectedPayPal'),0.0);
        </sql>
    </changeSet>

    <changeSet id="offboard-de-coingoal-and-em2coingoal-experiments" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1155692811605665/1207245677810403/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('deHigherCoinGoals', 'em2coinGoal')
                AND started_at is not NULL AND finished_at is NULL </where>
        </update>
    </changeSet>

    <changeSet id="translations-2024-05-17" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207299210443239/f,
                       https://app.asana.com/0/1203013731295607/1207110861157695/f">
        <sqlFile path="sql/translations_2024_05_17_sync_android.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="disable-tutorial-steps-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1203870303867095/f">
        <sql>
            UPDATE playtime.additional_offers
            SET is_disabled = 1
            WHERE action = 'googleLogin';
        </sql>
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('tutorialSteps')
                AND started_at is not NULL AND finished_at is NULL </where>
        </update>
    </changeSet>


    <changeSet id="turn-off-firstEarningsTopUp-experiment" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1204972173499939/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('firstEarningsTopup')
                AND started_at is not NULL AND finished_at is NULL
            </where>
        </update>
    </changeSet>

    <changeSet id="ios-preselected-pp-min-app-version" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207373122611873/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 106 WHERE `key` = 'iosPreselectedPayPal';
        </sql>
    </changeSet>

    <changeSet id="tapjoy-currency-sale-param" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207334238923686/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="tapjoy_coins_report" columnName="currency_sale"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="tapjoy_coins_report">
            <column name="currency_sale" type="DECIMAL(6,4)"/>
        </addColumn>
    </changeSet>

    <changeSet id="offerwall-promotions-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207334238923686/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('offerwallPromotionsV2', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'offerwallPromotionsV2'), 1.0),
            ('promotions', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'offerwallPromotionsV2'), 0.0),
            ('noPromotions', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'offerwallPromotionsV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-preselected-pp-exp-short-text" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207373122611873/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="payment_providers" columnName="short_text"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="payment_providers">
            <column name="short_text" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <sql>
            UPDATE playtime.payment_providers SET short_text = 'PayPal' where provider = 'paypal';
            UPDATE playtime.payment_providers SET short_text = 'Amazon' where provider = 'amazon';
            UPDATE playtime.payment_providers SET short_text = 'Google Play' where provider = 'google_play';
            UPDATE playtime.payment_providers SET short_text = 'Target GiftCard' where provider = 'target';
            UPDATE playtime.payment_providers SET short_text = 'Burger King eGift' where provider = 'burger_king';
            UPDATE playtime.payment_providers SET short_text = 'Walmart eGift' where provider = 'walmart';
            UPDATE playtime.payment_providers SET short_text = 'Doctors Without Borders' where provider = 'doctors_without_borders';
            UPDATE playtime.payment_providers SET short_text = 'Fight Climate Change' where provider = 'clean_air_task_force';
            UPDATE playtime.payment_providers SET short_text = 'The Hunger Project' where provider = 'the_hunger_project';
            UPDATE playtime.payment_providers SET short_text = 'Ukraine Crisis Relief' where provider = 'ukraine';
        </sql>
    </changeSet>

    <changeSet id="ios-preselected-pp-min-app-version-2" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207373122611873/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 108 WHERE `key` = 'iosPreselectedPayPal';
        </sql>
    </changeSet>

    <changeSet id="ios-preselected-pp-exp-short-text-2" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207373122611873/f">
        <dropNotNullConstraint schemaName="playtime" tableName="payment_providers" columnName="short_text" columnDataType="VARCHAR(30)"/>
    </changeSet>


    <changeSet id="em2-ecpm-coin-goal-variations" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207245677810403/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id) VALUES
            (0.0, 'em2CoinGoalCPMPow1', (SELECT id FROM playtime.ab_experiments WHERE `key`='earningsModelV2')),
            (0.0, 'em2CoinGoalCPMPow05', (SELECT id FROM playtime.ab_experiments WHERE `key`='earningsModelV2'));
        </sql>
    </changeSet>

    <changeSet id="em2-1cboost-and-retention" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207414878022456/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (0.0, '1CPBoostThenRetention', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2')),
                   (0.0, '1CPBoostRetentionTogether', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2'));
        </sql>
    </changeSet>

    <changeSet id="em2-tm-coins-flattening" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207245677810404/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (0.0, 'em2tmSqrt', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2')),
                   (0.0, 'em2tmStdDevSqrt', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2')),
                   (0.0, 'em2tmFlat', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2'));
        </sql>
    </changeSet>

    <changeSet id="ios-interview-invitation-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207358440835723/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosUsersInterview', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosUsersInterview'), 1.0),
            ('invitedToInterview', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosUsersInterview'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="user-interview-selection-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207358440835723/f">
        <createTable schemaName="playtime" tableName="user_interview_selection">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_interview_selection_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="exp-ios-cashout-form-name" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207452550036294/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('iosCashoutFormNameMode', 109);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutFormNameMode')),
                   (0.0, 'iosCashoutFormName', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutFormNameMode')),
                   (0.0, 'iosCashoutFormPopup', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutFormNameMode'));
        </sql>
    </changeSet>

    <changeSet id="translations-2024-06-05" author="eduard.trushnikov"
               labels="https://app.asana.com/0***************************************************/f">
        <sqlFile path="sql/translations_2024_06_05_ios_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="exp-offboarding-first-earnings-topup-cleanup" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207398923476583/f">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="user_earnings_topup" schemaName="playtime"/>
        </preConditions>
        <dropTable tableName="user_earnings_topup" schemaName="playtime"/>
    </changeSet>

    <changeSet id="exp-ios-games-order-new-variations" author="vitalii.sirotkin" labels="https://app.asana.com/0/1206346880021737/1207392747140796/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`) VALUES
                ('retention1Plus', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key`='iosGamesOrder'), 0),
                ('retention6Plus', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key`='iosGamesOrder'), 0);
        </sql>
    </changeSet>

    <changeSet id="add-android-online-users-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207379830537809/f,
                       https://app.asana.com/0/1203013731295607/1207379830537810/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidPlayersOnline', '58');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidPlayersOnline'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelToolbarBlue', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidPlayersOnline'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelToolbarGrey', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidPlayersOnline'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelCenterLongBlue', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidPlayersOnline'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-games-att-consent-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207277300940132/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosGamesAttConsent', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosGamesAttConsent'), 1.0),
            ('consentVariant1', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosGamesAttConsent'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-inapp-applovin-revenue" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207489459408828/f">
        <sql>
            INSERT INTO `playtime`.`games`(id, application_id, name, description, icon_filename, image_filename, order_key,
            applovin_api_key, activity_name, order_key_alt_1, order_key_alt_2, install_image_filename, video_preview_filename,
            info_text_install_top, info_text_install_bottom, exp_image_filename, downloads_number, rating, earning_power,
            back_ground_color, is_disabled, do_not_show, publisher_id, is_highlighted, downloads_number_str, show_for_lat,
            ios_application_id, ios_game_url, platform)
            VALUES (1000002, 'com.gimica.justplay', 'Do not delete!', 'Tech entry. Used in current_generic_revenue.', '', '', 1000002,
            '', '', 1000002, 1000002, '', null,
            '', '', null, -1, -1.00, '-1',
            null, 0, 1, 5, false, '', false,
            null, null, 'IOS');
        </sql>
        <sql>
            INSERT INTO playtime.game_ad_unit_ids (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES
            ('com.gimica.justplay', 'BANNER', '1cb72efd7a22989d', 1, 'IOS'),
            ('com.gimica.justplay', 'REWARDED', 'b5e4e6be1577e315', 1, 'IOS'),
            ('com.gimica.justplay', 'INTERSTITIAL', 'd1eef1ea993e5de0', 1, 'IOS'),
            ('com.gimica.justplay', 'BANNER', '1cb72efd7a22989d', 0, 'IOS'),
            ('com.gimica.justplay', 'REWARDED', '8d0b8c84fb033379', 0, 'IOS'),
            ('com.gimica.justplay', 'INTERSTITIAL', '15096c52d39651aa', 0, 'IOS');
        </sql>
    </changeSet>

    <changeSet id="game-coin-goals-exp" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207102921127862/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidСoinGoalsV2', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 1.0),
            ('controldefault', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 0.0),
            ('androidGeneralPlusTwo8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 0.0),
            ('androidGeneralPlusFive8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 0.0),
            ('androidThreeGames8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 0.0),
            ('androidSixGames8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidСoinGoalsV2'), 0.0);

            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosСoinGoalsV2', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 1.0),
            ('controldefault', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 0.0),
            ('iosGeneralPlusTwo8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 0.0),
            ('iosGeneralPlusFive8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 0.0),
            ('iosThreeGames8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 0.0),
            ('iosSixGames8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosСoinGoalsV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="game-coin-goal-tables" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207159821384179/f">
        <createTable tableName="game_coin_goal_set" schemaName="playtime">
            <column name="id" type="VARCHAR(36)">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             foreignKeyName="fk_game_coin_goals_user_id"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="completed_at" type="TIMESTAMP"/>
            <column name="next_set_timestamp" type="TIMESTAMP"/>
        </createTable>
        <createTable tableName="game_coin_goals" schemaName="playtime">
            <column name="id" type="VARCHAR(36)">
                <constraints primaryKey="true"/>
            </column>
            <column name="coin_goal_set_id" type="VARCHAR(36)">
                <constraints referencedTableSchemaName="playtime"
                             referencedTableName="game_coin_goal_set"
                             referencedColumnNames="id"
                             foreignKeyName="fk_game_coin_goals_game_coin_goal_set"/>
            </column>
            <column name="order" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="game_id" type="int">
                <constraints referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"
                             foreignKeyName="fk_game_coin_goals_games_id"/>
            </column>
            <column name="goal" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="goal_balance" type="DECIMAL(18,6)"/>
            <column name="notified_on_completion" type="bool" defaultValue="false">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="claimed_at" type="TIMESTAMP"/>
        </createTable>
    </changeSet>

    <changeSet id="best-coins-badge-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207400495614608/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('bestCoinsBadge', 58);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'bestCoinsBadge'), 1.0),
            ('showBadgeAndroid', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'bestCoinsBadge'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add_more_encrypted_columns" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1206532536432378">
        <sql>
            ALTER TABLE playtime.user_emails ADD COLUMN encrypted_email VARCHAR(750);
        </sql>
    </changeSet>

    <changeSet id="games-to-playtime-tables-grants-2024-06-19" author="vitaliy.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207159821384179/f"
               context="!unit-test">
        <sql>
            GRANT SELECT, INSERT, UPDATE ON `playtime`.`game_coin_goals` TO `games`@`%`;
            GRANT SELECT, INSERT, UPDATE ON `playtime`.`game_coin_goal_set` TO `games`@`%`;
        </sql>
    </changeSet>

    <changeSet id="ht-persistence" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207292621024916/f">
        <createTable tableName="user_highly_trusted_status" schemaName="playtime">
            <column name="user_id" type="VARCHAR(36)">
                <constraints
                        primaryKey="true"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                        foreignKeyName="fk_user_highly_trusted_status_user_id"/>
            </column>
            <column name="is_highly_trusted" type="BOOL">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="adjoe-callback-db-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207582969942636/f">
        <createTable schemaName="playtime" tableName="adjoe_coins_report">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_adjoe_coins_report_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="coins" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="signature" type="VARCHAR(40)">
                <constraints nullable="false"/>
            </column>
            <column name="currency_name" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="sdk_app_id" type="VARCHAR(100)"/>
            <column name="device_id" type="VARCHAR(100)"/>
            <column name="app_id" type="VARCHAR(100)"/>
            <column name="app_name" type="VARCHAR(100)"/>
            <column name="reward_level" type="INT"/>
            <column name="reward_type" type="VARCHAR(100)"/>
            <column name="ua_network" type="VARCHAR(100)"/>
            <column name="ua_channel" type="VARCHAR(100)"/>
            <column name="ua_sub_publisher_encrypted" type="VARCHAR(100)"/>
            <column name="ua_sub_publisher_clear_text" type="VARCHAR(100)"/>
            <column name="placement" type="VARCHAR(100)"/>
            <column name="publisher_sub_id1" type="VARCHAR(100)"/>
            <column name="publisher_sub_id2" type="VARCHAR(100)"/>
            <column name="publisher_sub_id3" type="VARCHAR(100)"/>
            <column name="publisher_sub_id4" type="VARCHAR(100)"/>
            <column name="publisher_sub_id5" type="VARCHAR(100)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addColumn schemaName="playtime" tableName="cfg_cashout">
            <column name="offerwall_coins_to_usd_conversion_ratio" type="DECIMAL(10,6)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <update schemaName="playtime" tableName="cfg_cashout">
            <column name="offerwall_coins_to_usd_conversion_ratio" value="220.0"/>
        </update>
    </changeSet>

    <changeSet id="locked-games-explanation-exp-part1" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1203340576394976/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidLockedGamesExplanation', '59');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidLockedGamesExplanation'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidLockedGamesWidget', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidLockedGamesExplanation'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="update-payment-providers-translations-asia" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1207265595877660"
               context="asia or asia-test or unit-test">
        <sqlFile path="sql/pp-asia.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="update-payment-providers-translations-latam" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1207265595877660"
               context="latam or latam-test or unit-test">
        <sqlFile path="sql/pp-latam.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="update-payment-providers-translations-gb" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1207265595877660"
               context="gb or gb-test or unit-test">
        <sqlFile path="sql/pp-gb.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-coins-amount-separator" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207588249651232/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosCoinsAmountSeparator', 109);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCoinsAmountSeparator'), 1.0),
            ('localeRelated', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCoinsAmountSeparator'), 0.0),
            ('customSeparator', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCoinsAmountSeparator'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="admin-generic-scheduled-notifications-1" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207443675765551/f">
        <createTable schemaName="playtime" tableName="scheduled_generic_notification">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_scheduled_generic_notification"/>
            </column>
            <column name="title" type="VARCHAR(100)"/>
            <column name="text" type="VARCHAR(500)"/>
            <column name="send_after" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="admin-generic-scheduled-notifications-2" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207443675765551/f">
        <createTable schemaName="playtime" tableName="scheduled_generic_notifications_sending">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_sched_notifications_sending"/>
            </column>
            <column name="notification_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_user_sched_notif_sending_notification_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="scheduled_generic_notification"
                             referencedColumnNames="id"
                />
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_sched_notifications_sending_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="sent_at" type="TIMESTAMP"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="playtime" tableName="scheduled_generic_notification" indexName="idx_sched_notification__send_after">
            <column name="send_after"/>
        </createIndex>
    </changeSet>

    <changeSet id="turn-off-iosNotifications-experiment" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1205869225072027/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('iosNotifications')
                AND started_at is not NULL AND finished_at is NULL
            </where>
        </update>
    </changeSet>

    <changeSet id="hunger-project-video" author="andrei.khripushin" labels="https://app.asana.com/0/1155692811605665/1207610445613773/f">
        <sql>
            UPDATE playtime.payment_providers
            SET video_url = 'https://www.youtube.com/watch?v=cY_iAEGL-1g'
            WHERE provider = 'the_hunger_project';
        </sql>
    </changeSet>

    <changeSet id="remove-old-translations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207655687684417/f">
        <sql>
            DELETE
            FROM playtime.app_translation
            WHERE resource_name in ('cashout_user_data_full_name_hint', 'cashout_user_data_full_address_hint')
            and app_platform = 'ANDROID';
        </sql>
    </changeSet>

    <changeSet id="ios-puzzlepopblaster-game" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207221365152659/f">
        <sqlFile path="sql/add-puzzle-pop-blaster-ios-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-puzzlepopblaster-fix" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207221365152659/f">
        <sql>
            UPDATE playtime.games
            SET do_not_show = 1
            WHERE application_id = 'com.gimica.puzzlepopblaster'
            and platform = 'IOS';

            UPDATE playtime.ios_pre_game_screen SET tags = 'Matching,Casual,Puzzle' WHERE game_id = 500014;
        </sql>
    </changeSet>

    <changeSet id="encryption-cron-jobs" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('fillCashoutsTableEncryptedFields', '0', now());

            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('fillUserEmailsTableEncryptedFields', '0', now());
        </sql>
    </changeSet>

    <changeSet id="translations-playstore-notification-update-2024-06-26" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207571726931621/f">
        <sqlFile path="sql/translations_2024_06_26_playstore_notification.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-puzzlepopblaster-show" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207221365152659/f">
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE application_id = 'com.gimica.puzzlepopblaster'
            and platform = 'IOS';
        </sql>
    </changeSet>

    <changeSet id="exp-ios-highlighted-games" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207633566388536/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('iosHighlightGamesOnLowEarnings', 109);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosHighlightGamesOnLowEarnings')),
            (0.0, 'iosHighlightGamesOnLowEarnings', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosHighlightGamesOnLowEarnings'));
        </sql>
    </changeSet>

    <changeSet id="ios-cashout-for-coins" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207633566388533/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosCashoutForCoins', 110);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutForCoins'), 1.0),
            ('cashoutNow', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutForCoins'), 0.0),
            ('cashoutForCoins', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutForCoins'), 0.0),
            ('homeScreenCoins', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutForCoins'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-new-games-amount-exp" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207612516685304/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`)
            VALUES ('iosNewGamesAmount');

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosNewGamesAmount'), 1.0),
            ('amount3', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosNewGamesAmount'), 0.0),
            ('amount5', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosNewGamesAmount'), 0.0);
        </sql>
    </changeSet>
    <changeSet id="games-dynamic-ad-unit-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207645107559631/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`)
            VALUES ('gamesDynamicAdUnitAllocation');

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'gamesDynamicAdUnitAllocation'), 1.0),
            ('dynamicAdUnitAllocation', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'gamesDynamicAdUnitAllocation'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="games-additional-ad-unit" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207645107559631/f">
        <sql>
            INSERT INTO playtime.game_ad_unit_ids (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES ('com.gimica.sugarmatch', 'INTERSTITIAL_HIGH', '2857247079fcdb01', 1, 'ANDROID'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_MEDIUM', '06a4d03648e2fe9e', 1, 'ANDROID'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_HIGH', 'a7cea7a7f257a29e', 0, 'ANDROID'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_MEDIUM', '9e41f2148d590b45', 0, 'ANDROID');
        </sql>
    </changeSet>

    <changeSet id="offboard-iosCashoutConfirmation-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1205992148638917/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosCashoutConfirmation' AND started_at is not NULL AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="offboard-iosWelcomeCoins-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1205962786096072/f">
        <sql>
            UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosWelcomeCoins' AND started_at is not NULL AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="offboard-iosMoreGames-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206562844951343/f">
        <sql>
            UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosMoreGames' AND started_at is not NULL AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="offboard-iosCoinGoalSigns-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206714140579640/f">
        <sql>
            UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosCoinGoalSigns' AND started_at is not NULL AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="offboard-iosBestCoinsBadge-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1206714140579640/f">
        <sql>
            UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosBestCoinsBadge' AND started_at is not NULL AND finished_at is NULL;
            UPDATE playtime.games SET image_filename = 'treasure_master_preview.jpg' WHERE application_id = 'com.gimica.treasuremaster' AND platform = 'IOS';
        </sql>
    </changeSet>

    <!--Shame on me-->
    <changeSet id="fix-cyrillic-in-gcg-key" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207102921127862/f">
        <sql>
            UPDATE playtime.ab_experiments SET `key` = 'androidCoinGoalsV2' WHERE `key` = 'androidСoinGoalsV2';
            UPDATE playtime.ab_experiments SET `key` = 'iosCoinGoalsV2' WHERE `key` = 'iosСoinGoalsV2';
        </sql>
    </changeSet>

    <changeSet id="em2-beta01-variation" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207703506233898/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES (0.0, 'em2beta01', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'earningsModelV2'));
        </sql>
    </changeSet>


    <changeSet id="ios-tilematchpro-game" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1207292714570279/f">
        <sqlFile path="sql/add-tilematchpro-ios-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="app-full-rollout-52-again" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1719824945362909">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.app_versions_full_rollout_dates
                WHERE app_version = 52;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (52, '2024-02-19');
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-55-58" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1719824945362909">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (55, '2024-04-08'),
            (56, '2024-05-03'),
            (57, '2024-06-03'),
            (58, '2024-06-19');
        </sql>
    </changeSet>

    <changeSet id="exp-offboard-fairytaleV2" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206521769244041/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'fairytaleV2'
            AND started_at is not NULL
            AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="exp-offboard-highlightPaypal" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1205353528839839/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'highlightPaypal'
            AND started_at is not NULL
            AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="turn-off-noAddressOnCashout-experiment" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1206250055333535/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('noAddressOnCashout')
                AND started_at is not NULL AND finished_at is NULL
            </where>
        </update>
    </changeSet>

    <changeSet id="turn-off-tutorialExploreNow-experiment" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1206250055333535/f">
        <update schemaName="playtime" tableName="ab_experiments">
            <column name="finished_at" valueComputed="now()"/>
            <where>`key` in ('tutorialExploreNow')
                AND started_at is not NULL AND finished_at is NULL
            </where>
        </update>
    </changeSet>

    <changeSet id="offboard-iosBestCoinsBadge-experiment" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207131424598119/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'relaxedSimFraudPolicy'
            AND started_at is not NULL
            AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="ios-iosCashoutForCoins-min-app-version-2" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207768383453403/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 114 WHERE `key` = 'iosCashoutForCoins';
        </sql>
    </changeSet>

    <changeSet id="locked-games-explanation-exp-notification-table" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207571726931621/f">
        <createTable tableName="user_game_unlock_reminder_dismiss" schemaName="playtime">
            <column name="user_id" type="VARCHAR(36)">
                <constraints primaryKey="true"
                             primaryKeyName="pk_user_game_unlock_reminder_dismiss"
                             foreignKeyName="pk_user_game_unlock_reminder_dismiss_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_dismissed_game" type="INT">
                <constraints nullable="true"
                             foreignKeyName="fk_user_game_unlock_reminder_dismiss_last_dismiss_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"/>
            </column>
            <column name="last_notification_game" type="INT">
                <constraints nullable="true"
                             foreignKeyName="fk_user_game_unlock_reminder_dismiss_last_notification_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="locked-games-explanation-exp-notification-variations" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207571726931621/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidGameUnlockNotification', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidLockedGamesExplanation'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidGameUnlockNotificationOpen', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidLockedGamesExplanation'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="locked-games-explanation-exp-notification-translations" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207571726931621/f">
        <sqlFile path="sql/translations_2024_06_26_game_unlock_reminder.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="placement-ids-update" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207776770862648/f">
        <sql>
            UPDATE playtime.game_ad_unit_ids
            SET ad_unit_id = 'cf3c3f61f45f6da4'
            WHERE package_id in ('com.justplay.app', 'com.gimica.justplay')
            AND app_platform = 'IOS'
            AND is_highly_trusted_user = false
            AND ad_type = 'INTERSTITIAL';

            UPDATE playtime.game_ad_unit_ids
            SET ad_unit_id = '5a478b01441a9fe5'
            WHERE package_id in ('com.justplay.app', 'com.gimica.justplay')
            AND app_platform = 'IOS'
            AND is_highly_trusted_user = false
            AND ad_type = 'REWARDED';
        </sql>
    </changeSet>

    <changeSet id="android-game-coin-goal-and-locked-games-exp-min-app-version-2" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1207785616309381/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 60 WHERE `key` = 'androidCoinGoalsV2';
            UPDATE playtime.ab_experiments SET minimum_app_version = 60 WHERE `key` = 'androidLockedGamesExplanation';
        </sql>
    </changeSet>

    <changeSet id="emoji-support-for-scheduled-notifications" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1199943047294494/1207813871330329/f">
        <sql>
            ALTER TABLE `playtime`.`scheduled_generic_notification`
            MODIFY title VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
            MODIFY text VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

    <changeSet id="offboard-deGamesOrderRPI-experiment" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1205146872391673/f">
        <sql>
            UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'deGamesOrderRPI' AND started_at is not NULL AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="add-android-online-users-green-exp" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1207823266439086/f">
        <sql>
            UPDATE `playtime`.`ab_experiments` set minimum_app_version = 61 where `key` = 'androidPlayersOnline';

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('panelToolbarGreen', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidPlayersOnline'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="survey-feature-convert-to-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207633566388546/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidCashoutSurvey', '53');

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCashoutSurvey'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidCashoutSurvey', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCashoutSurvey'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="android-preselected-pp-exp" author="pavel.novikov"
               labels="https://app.asana.com/0/1203315556469817/1207299010722381/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidPreselectedPayPal', 61);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPreselectedPayPal'), 1.0),
                   ('limitedProviders', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPreselectedPayPal'), 0.0),
                   ('preselectPaypal', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPreselectedPayPal'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="offboard-experiment-sendInfoOffer" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1203870303867067/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'sendInfoOffer'
            AND started_at is not NULL
            AND finished_at is NULL;
        </sql>
        <dropTable tableName="info_offers_message" schemaName="playtime"/>
    </changeSet>

    <changeSet id="ios-tilematchpro-show" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207292714570279/f">
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE application_id = 'com.gimica.tilematchpro'
            and platform = 'IOS';
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-59" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1721639552330459">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.app_versions_full_rollout_dates
                WHERE app_version = 59;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (59, '2024-07-11');
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-60" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1721639552330459">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.app_versions_full_rollout_dates
                WHERE app_version = 60;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (60, '2024-07-22');
        </sql>
    </changeSet>

    <changeSet id="add-default-values-to-user_cashout_transactions-address" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions MODIFY COLUMN address VARCHAR(250) NOT NULL DEFAULT '';
        </sql>
    </changeSet>

    <changeSet id="add-default-values-to-user_cashout_transactions-name" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions MODIFY COLUMN name VARCHAR(100) NOT NULL DEFAULT '';
        </sql>
    </changeSet>

    <changeSet id="add-default-values-to-blacklist_email" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            ALTER TABLE playtime.blacklist_email MODIFY COLUMN email VARCHAR(255) NOT NULL DEFAULT '';
            ALTER TABLE playtime.blacklist_email MODIFY COLUMN encrypted_email VARCHAR(750) NOT NULL;
            ALTER TABLE playtime.blacklist_email DROP PRIMARY KEY, ADD PRIMARY KEY pk_blacklist_email (encrypted_email);
        </sql>
    </changeSet>

    <changeSet id="offboard-sendGameStats-experiment" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203870303867052/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'sendGameStats' AND started_at is not NULL AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="games-redundant-fields-nullable" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203870303867052/f">
        <sql>
            ALTER TABLE playtime.games MODIFY COLUMN order_key_alt_1 int;
            ALTER TABLE playtime.games MODIFY COLUMN order_key_alt_2 int;
            ALTER TABLE playtime.games MODIFY COLUMN downloads_number bigint;
            ALTER TABLE playtime.games MODIFY COLUMN downloads_number_str varchar(20) default '';
            ALTER TABLE playtime.games MODIFY COLUMN rating decimal(3, 2);
            ALTER TABLE playtime.games MODIFY COLUMN earning_power varchar(20);
        </sql>
    </changeSet>

    <changeSet id="android-solitaire-game-part1" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207810934377748/f">
        <sqlFile path="sql/add-new-solitaire.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="exp-android-hide-earnings" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207147362311377/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidHideEarnings', 61);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidHideEarnings')),
            (0.0, 'androidHideEarningsGray', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidHideEarnings')),
            (0.0, 'androidHideEarningsGrayGreen', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidHideEarnings'));
        </sql>
    </changeSet>

    <changeSet id="ios-all-games-button-new-variations" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1155692811605665/1207810480201318/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 115 WHERE `key` = 'iosAllGamesButton';

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('lastPlayedGames', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosAllGamesButton'), 0.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('nextGame', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosAllGamesButton'), 0.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('bottom', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosAllGamesButton'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="exp-android-notify-to-play" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207274569010363/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidNotifyToPlay', 61);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidNotifyToPlay')),
            (0.0, 'androidNotifyToPlay', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidNotifyToPlay'));
        </sql>
    </changeSet>

    <changeSet id="user-pre-game-screen-opened" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207274569010363/f">
        <createTable schemaName="playtime" tableName="user_pre_game_screen_opened">
            <column name="user_id" type="VARCHAR(36)">
                <constraints primaryKey="true"
                             primaryKeyName="pk_user_pre_game_screen_opened"
                             foreignKeyName="pk_user_pre_game_screen_opened_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="exp-boosted-sing" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207831810921962/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidBoostedSign', 61);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidBoostedSign')),
            (0.0, 'boostedSignAlways', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidBoostedSign')),
            (0.0, 'boostedSignEvenCp', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidBoostedSign')),
            (0.0, 'boostedSignFirstHour', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidBoostedSign')),
            (0.0, 'boostedSignFirstLastHours', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidBoostedSign'));
        </sql>
    </changeSet>

    <changeSet id="exp-close-newOfferwallStyleV2" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C05M01SL9DY/p1722361603650829">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = now()
            WHERE `key` = 'newOfferwallStyleV2'
            AND started_at is not NULL
            AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="android-game-widget-tracker" author="pavel.novikov"
               labels="https://app.asana.com/0/1203315556469817/1207299010722381/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidGameWidgetTracker', 61);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidGameWidgetTracker'), 1.0),
                   ('androidGwtCoinsOnly', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidGameWidgetTracker'), 0.0),
                   ('androidGwtCoinsHighEarnings', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidGameWidgetTracker'), 0.0),
                   ('androidGwtCoinsHighEarningsPlayers', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidGameWidgetTracker'), 0.0),
                   ('androidGwtMoneyOnly', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidGameWidgetTracker'), 0.0);

        </sql>
    </changeSet>



    <changeSet id="ios-cashout-progress-bar" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1155692811605665/1207588249651232/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('iosCashoutProgressBar', 115);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar'), 1.0),
            ('progressBarWithoutText', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar'), 0.0),
            ('progressBarWithText', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar'), 0.0);
        </sql>
    </changeSet>


    <changeSet id="exp-ios-games-order-new-variations-balanced" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207840604769783/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`) VALUES
            ('retention1Balanced', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key`='iosGamesOrder'), 0),
            ('retention6Balanced', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key`='iosGamesOrder'), 0);
        </sql>
    </changeSet>

    <changeSet id="offboard-custom-game-pages-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1206889228467924/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'customGamePagesV1' AND started_at is not NULL AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="offboard-custom-balance-notification-experiment" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1206158222746477/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'customBalanceNotification' AND started_at is not NULL AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="multiple-offerwall-additional-offers" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207420488251873/f,
                       https://app.asana.com/0/1203013731295607/1207420488251874/f">
        <sql>
            INSERT INTO playtime.additional_offers
            (id, action, image_filename, coins_to_earn, is_one_time_use_only, offer_completion_notification_delay_seconds, order_key,
            is_disabled, subtext, is_highlighted) VALUES
            (100011, 'TAPJOY', 'offerwall.jpg', null, 0, null, 27, 0,
            null, false);
            INSERT INTO playtime.additional_offers
            (id, action, image_filename, coins_to_earn, is_one_time_use_only, offer_completion_notification_delay_seconds, order_key,
            is_disabled, subtext, is_highlighted) VALUES
            (100012, 'FYBER', 'offerwall.jpg', null, 0, null, 27, 0,
            null, false);
        </sql>
    </changeSet>

    <changeSet id="add-adjoe-to-tapjoy-exp" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207420488251873/f,
                       https://app.asana.com/0/1203013731295607/1207420488251874/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidAdjoeOfferwall', 62);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidAdjoeOfferwall'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('adjoeOnly', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidAdjoeOfferwall'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('addAdjoe', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidAdjoeOfferwall'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="add-adjoe-offer" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207420488251873/f,
                       https://app.asana.com/0/1203013731295607/1207420488251874/f">
        <sql>
            INSERT INTO playtime.additional_offers
            (id, action, image_filename, coins_to_earn, is_one_time_use_only, offer_completion_notification_delay_seconds, order_key,
            is_disabled, subtext, is_highlighted) VALUES
            (100013, 'ADJOE', 'offerwall.jpg', null, 0, null, 35, 0,
            null, false);
        </sql>
    </changeSet>

    <changeSet id="games-redundant-fields-delete" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1203870303867052/f">
        <sql>
            ALTER TABLE playtime.games DROP COLUMN order_key_alt_1;
            ALTER TABLE playtime.games DROP COLUMN order_key_alt_2;
            ALTER TABLE playtime.games DROP COLUMN downloads_number;
            ALTER TABLE playtime.games DROP COLUMN downloads_number_str;
            ALTER TABLE playtime.games DROP COLUMN rating;
            ALTER TABLE playtime.games DROP COLUMN earning_power;
        </sql>
    </changeSet>

    <changeSet id="android-spidersolitaire-game-part1" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207895442924294/f">
        <sqlFile path="sql/add-new-spidersolitaire.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="ios-show-ad-after-cashout-placement-ids-update" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207017527635556/f">
        <sql>
            UPDATE playtime.game_ad_unit_ids
            SET ad_unit_id = 'cf3c3f61f45f6da4'
            WHERE package_id in ('com.justplay.app', 'com.gimica.justplay')
            AND app_platform = 'IOS'
            AND ad_type = 'INTERSTITIAL';

            UPDATE playtime.game_ad_unit_ids
            SET ad_unit_id = '5a478b01441a9fe5'
            WHERE package_id in ('com.justplay.app', 'com.gimica.justplay')
            AND app_platform = 'IOS'
            AND ad_type = 'REWARDED';
        </sql>
    </changeSet>

    <changeSet id="offboard-custom-inactivity-reminders-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1203542563629361/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'customInactivityReminders' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="exp-android-top-part-widgets" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207811086076539/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidTopPartWidgets', 61);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidTopPartWidgets')),
            (0.0, 'singlePartWidget', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidTopPartWidgets')),
            (0.0, 'doublePartWidget', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidTopPartWidgets')),
            (0.0, 'doubleAndSinglePartWidgets', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidTopPartWidgets'));
        </sql>
    </changeSet>

    <changeSet id="android-in-app-install-digital-turbine-ignite" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1207866374061140/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidDtiInstall', 62);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidDtiInstall'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidDtiInstall', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidDtiInstall'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-sugar-rush-additional-ad-unit" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207840612711747/f">
        <sql>
            INSERT INTO playtime.game_ad_unit_ids (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES ('com.gimica.sugarmatch', 'INTERSTITIAL_HIGH', '373a7e56b759d38b', 1, 'IOS'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_MEDIUM', '416459f9f4412b76', 1, 'IOS'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_HIGH', '70d0b20b287fe2b9', 0, 'IOS'),
            ('com.gimica.sugarmatch', 'INTERSTITIAL_MEDIUM', '4dda1b0a6e9b9209', 0, 'IOS');
        </sql>
    </changeSet>

    <changeSet id="offers-redundant-fields-delete" author="andrei.khripushin"
               labels="https://app.asana.com/0/1207965871080504/1207964875807333/f">
        <sql>
            ALTER TABLE playtime.games DROP is_highlighted;
            ALTER TABLE playtime.additional_offers DROP is_highlighted;
            ALTER TABLE playtime.cfg_offerwall_rule DROP is_highlighted;
            ALTER TABLE playtime.cfg_offerwall DROP is_highlighted;
        </sql>
    </changeSet>

    <changeSet id="ios-showAdAfterCashoutAndroid-increase-mav" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203293022323368/1207017527635556/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 123 WHERE `key` = 'showAdAfterCashoutAndroid';
        </sql>
    </changeSet>

    <changeSet id="add_email_hash_columns" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1206532536432378">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions ADD COLUMN email_hash VARCHAR(64);
            ALTER TABLE playtime.user_cashout_transactions ADD INDEX idx_user_cashout_transactions__email_hash (email_hash);

            ALTER TABLE playtime.blacklist_email ADD COLUMN email_hash VARCHAR(64);
            ALTER TABLE playtime.blacklist_email ADD INDEX idx_blacklist_email__email_hash (email_hash);

        </sql>
    </changeSet>


    <changeSet id="payment-provider-survey" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207794700599293/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`)
            VALUES ('iosPaymentProviderSurvey');

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPaymentProviderSurvey'), 1.0),
            ('specificProvidersOnClose', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPaymentProviderSurvey'), 0.0),
            ('specificProvidersInsideList', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPaymentProviderSurvey'), 0.0),
            ('categoriesProvidersOnClose', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPaymentProviderSurvey'), 0.0),
            ('categoriesProvidersInsideList', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPaymentProviderSurvey'), 0.0);

            INSERT INTO `playtime`.`ab_experiments` (`key`)
            VALUES ('androidPaymentProviderSurvey');

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPaymentProviderSurvey'), 1.0),
            ('specificProvidersOnClose', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPaymentProviderSurvey'), 0.0),
            ('specificProvidersInsideList', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPaymentProviderSurvey'), 0.0),
            ('categoriesProvidersOnClose', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPaymentProviderSurvey'), 0.0),
            ('categoriesProvidersInsideList', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidPaymentProviderSurvey'), 0.0);
        </sql>
    </changeSet>
    <changeSet id="horizontal-list-for-new-games" author="'pavel'.novikov"
               labels="https://app.asana.com/0/1203013731295607/1207420488251873/f,
                       https://app.asana.com/0/1203013731295607/1207420488251874/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidHorizontalLists', 62);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidHorizontalLists'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('horizontalView', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidHorizontalLists'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('horizontalViewRecentlyPlayed', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidHorizontalLists'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('horizontalViewMultipleLists', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidHorizontalLists'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-cashout-progress-bar-v2" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1155692811605665/1207904683141555/f">
        <sql>
            UPDATE playtime.ab_variations SET `key` = 'linearProgressBar'
            WHERE experiment_id = (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar')
            AND `key` = 'progressBarWithText';

            UPDATE playtime.ab_variations SET `key` = 'progressBarWithCircles'
            WHERE experiment_id = (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar')
            AND `key` = 'progressBarWithoutText';

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('dashedLineProgressBar', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutProgressBar'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="game-coin-goal-easy-goals" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1207966961591503/f">
        <sql>
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('androidGeneralPlusTwoEasyGoals8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCoinGoalsV2'), 0.0);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('iosGeneralPlusTwoEasyGoals8h', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCoinGoalsV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="offboard-tip-part-to-charity-experiment" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1204978000258091/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'tipPartToCharity' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="ios-user-interview-new-variation" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208061189723950/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 125 WHERE `key` = 'iosUsersInterview';

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('invitedToSurvey', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosUsersInterview'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-incomplete-cashout-restore-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207794700599303/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('iosIncompleteCashoutRestoring', 124);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosIncompleteCashoutRestoring'), 1.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('iosRestoreCashout', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosIncompleteCashoutRestoring'),0.0);
        </sql>
    </changeSet>

    <changeSet id="payment-provider-survey-completed-table" author="vitalii.sirotkin">
        <createTable tableName="payment_provider_survey_completed">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_payment_provider_survey_completed_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="android-cashout-progress-bar" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1208010761400490/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidCashoutProgressBar', 63);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutProgressBar'), 1.0),
            ('linear', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutProgressBar'), 0.0),
            ('dashed', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutProgressBar'), 0.0),
            ('circles', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutProgressBar'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="image-only-game-widget" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1208010793260801/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidImagesOnly', 63);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidImagesOnly'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('standardImages', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidImagesOnly'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('customImages', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidImagesOnly'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-61" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1724058715042429">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (61, '2024-08-05');
        </sql>
    </changeSet>

    <changeSet id="goal-coins-balance-em1" author="alex.potolitcyn" labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="user_coin_goal_current_coins_balance" columnName="goal_coins"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="user_coin_goal_current_coins_balance">
            <column name="goal_coins" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ios-play-first-game-push-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1206450540641151/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('iosPlayFirstGamePush', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPlayFirstGamePush'), 1.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('iosPlayFirstGamePush', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosPlayFirstGamePush'),0.0);
        </sql>
    </changeSet>

    <changeSet id="android-incomplete-cashout-restore-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1208010761400493/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidIncompleteCashoutRestoring', 63);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidIncompleteCashoutRestoring'), 1.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('androidRestoreCashout', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidIncompleteCashoutRestoring'),0.0);
        </sql>
    </changeSet>

    <changeSet id="android-coins-amount-separator-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1208074689941999/f">
        <sql>
            INSERT INTO `playtime`.`ab_experiments` (`key`, `minimum_app_version`)
            VALUES ('androidCoinsAmountSeparator', 63);

            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('default', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinsAmountSeparator'), 1.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('locale', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinsAmountSeparator'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('comma', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinsAmountSeparator'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('period', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinsAmountSeparator'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('space', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinsAmountSeparator'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="em2betaFlat" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1208136322600209/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('em2betaFlat', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('em2BoiledFrogSoft', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('em2BoiledFrog', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'earningsModelV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="user-multi-sim-support" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208040134879203/f">
        <createTable schemaName="playtime" tableName="user_multi_sim_data">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_user_multi_sim_data"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints
                        nullable="false"
                        foreignKeyName="fk_user_multi_sim_data_user_id"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"/>
            </column>
            <column name="network_country" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="network_operator_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="sim_country" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="sim_operator_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="sim_slot_index" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="fix-date-from-in-cfg-offerwall-rule" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208188280454677/f">
        <sql>
            ALTER TABLE playtime.cfg_offerwall_rule MODIFY COLUMN date_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="ios-att-consent-more-variations" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208180078577755/f">
        <sql>
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('simple', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosGamesAttConsent'), 0.0),
            ('aggressive', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosGamesAttConsent'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="new-variant-generalOnceThen3games8h" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1208136322600211/f">
        <sql>
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('iosGeneralOnceThen3games8h', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'iosCoinGoalsV2'), 0.0);
            INSERT INTO `playtime`.`ab_variations` (`key`, `experiment_id`, `allocation_ratio`)
            VALUES ('androidGeneralOnceThen3games8h', (SELECT `id` FROM `playtime`.`ab_experiments` WHERE `key` = 'androidCoinGoalsV2'), 0.0);
        </sql>
    </changeSet>

    <changeSet id="goal-coins-balance-em2" author="alex.potolitcyn" labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="user_coin_goal_current_coins_balance_em2" columnName="goal_coins"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="user_coin_goal_current_coins_balance_em2">
            <column name="goal_coins" type="DECIMAL(18,6)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="visible-coins-table" author="alex.potolitcyn" labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="playtime" tableName="user_visible_coins"/>
            </not>
        </preConditions>
        <createTable schemaName="playtime" tableName="user_visible_coins">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_visible_coins_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="coins" type="DECIMAL(18, 6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-cooldown-for-coin-notification" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1208088077097854/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('coinNotificationCoolDown', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinNotificationCoolDown'), 1.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('cooldown5Sec', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinNotificationCoolDown'),0.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('cooldown15Sec', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinNotificationCoolDown'),0.0);
        </sql>
    </changeSet>

    <changeSet id="fix-showad-exp-min-version" author="alex.potolitcyn">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 123 WHERE `key` = 'iosShowAdAfterCashout';
        </sql>
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 57 WHERE `key` = 'showAdAfterCashoutAndroid';
        </sql>
    </changeSet>

    <changeSet id="android-show-ofw-after-first-successful-cashout-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1207089635459955/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidShowOfwAfterFirstSC', null);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidShowOfwAfterFirstSC'), 1.0);
            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('showOfwAfterFirstSC', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidShowOfwAfterFirstSC'),0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-cashout-tutorial-exp" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208182997724968/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('iosCashoutTutorial', 134);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutTutorial'), 1.0),
            ('gold', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutTutorial'),0.0),
            ('blue', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'iosCashoutTutorial'),0.0);
        </sql>
    </changeSet>

    <changeSet id="add-usa-state-to-ip-registry-table" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1208221154589839/f">
        <addColumn tableName="ip_registry_data" schemaName="playtime">
            <column name="region_code" type="varchar(10)"/>
        </addColumn>
    </changeSet>

    <changeSet id="offboard-show-ad-after-cashout-android-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1207056311328894/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'showAdAfterCashoutAndroid' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="offboard-android-players-online-experiment" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1207599434668733/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'androidPlayersOnline' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="android-players-online-translations" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1207599434668733/f">
        <sqlFile path="sql/android_players_online.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-tangram-game" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1207810934377746/f"
               runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1)
                FROM playtime.games
                WHERE application_id = 'com.gimica.tangram'
                AND platform = 'ANDROID';
            </sqlCheck>
        </preConditions>
        <sqlFile path="sql/add-tangram-game.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ios-rescue-game-time-spent-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208253377220756/f">
        <createTable schemaName="playtime" tableName="user_game_time_spent">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_game_time_spent_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="game_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_user_game_time_spent_game_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"
                />
            </column>
            <column name="time_spent_seconds" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_game_time_spent"
                       columnNames="user_id, game_id"
                       constraintName="pk_user_game_time_spent"/>
    </changeSet>

    <changeSet id="ios-user-game-ranks-ja-translations" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208253377220756/f">
        <sqlFile path="sql/translations_2024_09_11_user_game_ranks.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id="add-amount-and-coins-to-cash-out-process" author="pavel.novikov"
               labels="https://app.asana.com/0/1199943047294494/1208233267036944/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('androidCashoutForCoins', 64);

            INSERT INTO playtime.ab_variations (`key`, experiment_id, allocation_ratio)
            VALUES ('default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutForCoins'), 1.0),
                   ('cashoutNow', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutForCoins'),0.0),
                   ('cashoutForCoins', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutForCoins'),0.0),
                   ('homeScreenCoins', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'androidCashoutForCoins'),0.0);
        </sql>
    </changeSet>

    <changeSet id="ios-achievement-table" author="alex.potolitcyn">
        <createTable schemaName="playtime" tableName="achievement">
            <column name="id" type="VARCHAR(100)">
                <constraints nullable="false"
                             primaryKey="true"
                             primaryKeyName="pk_achievement"
                />
            </column>
            <column name="title" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="incomplete_icon_url" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="complete_icon_url" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ios-user-completed-achievement-table" author="alex.potolitcyn">
        <createTable schemaName="playtime" tableName="user_completed_achievement">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_completed_achievement_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="achievement_id" type="VARCHAR(100)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_completed_achievement_achievement_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="achievement"
                             referencedColumnNames="id"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_completed_achievement" columnNames="user_id,achievement_id"
                       constraintName="pk_user_completed_achievement"/>
    </changeSet>

    <changeSet id="achievements-types-insert" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208253377220766/f">
        <sqlFile path="sql/achievement-types.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="fix-badges-titles" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C07LBB42R6J/p1726151174457109?thread_ts=1725874939.554339&amp;cid=C07LBB42R6J">
        <sql>
            UPDATE playtime.achievement
            SET title = CASE id
            WHEN 'PLAYED_7_GAMES' THEN 'Weekend Warrior'
            WHEN 'EARNED_POINTS_1KK' THEN 'Combo King'
            WHEN 'ACHIEVEMENT_14' THEN 'High Roller'
            WHEN 'ACHIEVEMENT_15' THEN 'Strategist'
            WHEN 'ACHIEVEMENT_16' THEN 'Puzzle Pro'
            WHEN 'ACHIEVEMENT_17' THEN 'Endurance Master'
            WHEN 'ACHIEVEMENT_18' THEN 'Streak Runner'
            WHEN 'ACHIEVEMENT_19' THEN 'Lucky Break'
            WHEN 'ACHIEVEMENT_20' THEN 'Risk Taker'
            WHEN 'ACHIEVEMENT_21' THEN 'Challenge Crusher'
            WHEN 'ACHIEVEMENT_22' THEN 'Legend in the Making'
            WHEN 'ACHIEVEMENT_23' THEN 'Zen Master'
            WHEN 'ACHIEVEMENT_24' THEN 'Ace Player'
            WHEN 'ACHIEVEMENT_25' THEN 'Puzzle Genius'
            WHEN 'ACHIEVEMENT_26' THEN 'Victory Streak'
            WHEN 'ACHIEVEMENT_27' THEN 'Goal Getter'
            WHEN 'ACHIEVEMENT_28' THEN 'Speed Demon'
            WHEN 'ACHIEVEMENT_29' THEN 'Champion of Time'
            WHEN 'ACHIEVEMENT_30' THEN 'Ultimate Master'
            ELSE title END ;
        </sql>
    </changeSet>

    <changeSet id="ios-news-part-2" author="andrei.khripushin" labels="https://app.asana.com/0/1203315556469817/1208253377220770/f" runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="ios_news" columnName="detailed_image" schemaName="playtime"/>
            </not>
        </preConditions>
        <sql>
            DELETE
            FROM playtime.ios_news
            WHERE order_key >= 8;
        </sql>
        <sqlFile path="sql/ios-news-part-2.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="exp-dont-reset-coins" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <sql>
            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('coinsDoNotResetAndroid', 63);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetAndroid')),
            (0.0, 'coinsDoNotResetSimple', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetAndroid')),
            (0.0, 'coinsDoNotResetComplete', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetAndroid')),
            (0.0, 'coinsDoNotResetAtAll', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetAndroid')),
            (0.0, 'coinsDoNotResetPartially', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetAndroid'));

            INSERT INTO playtime.ab_experiments (`key`, minimum_app_version)
            VALUES ('coinsDoNotResetIos', 125);

            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (1.0, 'default', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetIos')),
            (0.0, 'coinsDoNotResetSimple', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetIos')),
            (0.0, 'coinsDoNotResetComplete', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetIos')),
            (0.0, 'coinsDoNotResetAtAll', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetIos')),
            (0.0, 'coinsDoNotResetPartially', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'coinsDoNotResetIos'));
        </sql>
    </changeSet>

    <changeSet id="exp-dont-reset-coins-fake-meta-id" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <sql>
            INSERT INTO playtime.meta_user_earnings (id) VALUES (-1);
        </sql>
    </changeSet>

    <changeSet id="badges-1kk-achievement" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C07LBB42R6J/p1726151174457109?thread_ts=1725874939.554339&amp;cid=C07LBB42R6J">
        <sql>
            UPDATE playtime.achievement
            SET title = CASE id
            WHEN 'PLAYED_7_GAMES' THEN 'Combo King'
            WHEN 'EARNED_POINTS_1KK' THEN 'Weekend Warrior'
            ELSE title
            END,
            description = CASE id
            WHEN 'EARNED_POINTS_1KK' THEN 'You have earned the 1 Million loyalty points!'
            ELSE description
            END
            ;
        </sql>
    </changeSet>

    <changeSet id="badges-icons-update" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C07LBB42R6J/p1726235590249809">
        <sql>
            UPDATE playtime.achievement
            SET complete_icon_url = CASE id
            WHEN 'EARNED_POINTS_1KK' THEN 'achievements/badge_7.png'
            ELSE complete_icon_url
            END
            ;
        </sql>
        <sql>
            UPDATE playtime.achievement
            SET complete_icon_url = CASE complete_icon_url
            WHEN 'achievements/badge_6_shadowed.png' THEN 'achievements/badge_missing.png'
            ELSE complete_icon_url
            END,
            incomplete_icon_url = CASE incomplete_icon_url
            WHEN 'achievements/badge_6_shadowed.png' THEN 'achievements/badge_missing.png'
            ELSE incomplete_icon_url
            END
            ;
        </sql>
    </changeSet>

    <changeSet id="android-play-store-notification-exp-min-app-version-64" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1208156965011658/1208292598482070/f">
        <sql>
            UPDATE playtime.ab_experiments SET minimum_app_version = 64 WHERE `key` = 'playstoreTrackingNotifications';
        </sql>
    </changeSet>

    <changeSet id="add-unified-id-experiment" author="vitalii.sirotkin">
        <jp:createExperiment key="unifiedId">
            <jp:variation key="unifiedId"/>
        </jp:createExperiment>
    </changeSet>


    <changeSet id="offboard-games-preview-images-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1202151503181746/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'gamesPreviewImages' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="playtime-to-games-tables-grants-2024-09-17" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208088168760674/f"
               context="!unit-test">
        <sql>
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`user_visible_coins` TO `games` @`%`;
        </sql>
    </changeSet>

    <changeSet id="new-ios-translations-pack" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208302911920409/f" runOnChange="true">
        <sql>ALTER TABLE playtime.app_translation CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;</sql>
        <sqlFile path="sql/new-ios-translations-pack.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-androidPaypalHints-experiment" author="andrei.khripushin" labels="https://app.asana.com/0/1208156965011658/1208233267036947/f">
        <jp:createExperiment key="androidPaypalHints" minimumAppVersion="64">
            <jp:variation key="changeEmail"/>
            <jp:variation key="changeEmailAndFollowingText"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="clear-personal-data-cron-jobs" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('clearCashoutsTablePersonalData', '0', now());
        </sql>
    </changeSet>

    <changeSet id="user_emails_drop_email" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            ALTER TABLE playtime.user_emails DROP COLUMN email;
        </sql>
    </changeSet>

    <changeSet id="blacklist_email_drop_email" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1207529802053398">
        <sql>
            ALTER TABLE playtime.blacklist_email DROP COLUMN email;
        </sql>
    </changeSet>

    <changeSet id="ios-reviewer-tracking" author="andrei.khripushin"
               labels="https://app.asana.com/0/1203315556469817/1208318173874758/f">
        <addColumn schemaName="playtime" tableName="user_additional_data">
            <column name="is_reviewer" type="BOOLEAN">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="offboard-games-preview-images-exp-drop-table" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1202151503181746/f">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="games_exp_images" schemaName="playtime"/>
        </preConditions>
        <dropTable tableName="games_exp_images" schemaName="playtime"/>
    </changeSet>

    <changeSet id="deadline-on-cashout" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1208244273602616/f,https://app.asana.com/0/1207810850102739/1207855768888209/f">
        <sqlFile path="sql/dead-line-on-cashout.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="app-full-rollout-62-63" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1727082294511709">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (62, '2024-08-29');
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (63, '2024-09-16');
        </sql>
    </changeSet>

    <changeSet id="android-incomplete-cashout-restore-exp-table" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1208225233082347/f">
        <createTable tableName="user_cashout_initiated" schemaName="playtime">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_user_cashout_initiated_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_cashout_initiated" columnNames="user_id"
                       constraintName="pk_user_cashout_initiated"/>
    </changeSet>

    <changeSet id="android-pick-preferred-games-tutorial-step" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1208010761400484/f">
        <createTable schemaName="playtime" tableName="user_preferred_games">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_preferred_games_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="game_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_user_preferred_games_game_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"
                />
            </column>
            <column name="is_liked" type="BOOL">
                <constraints nullable="false"/>
            </column>

            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_preferred_games" columnNames="user_id,game_id"
                       constraintName="pk_user_preferred_games"/>

        <jp:createExperiment key="androidPickPreferredGames" minimumAppVersion="64">
            <jp:variation key="preferredTop"/>
            <jp:variation key="preferredTopDislikedBottom"/>
            <jp:variation key="preferredImagesTop"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="exp-short-1st-cp" author="alex.potolitcyn" labels="https://app.asana.com/0/1155692811605665/1207260007097365/f">
        <jp:createExperiment key="Short1PeriodTopUpSEONAndroid">
            <jp:variation key="period15TopUp50Seon"/>
            <jp:variation key="period15TopUp50SeonCompensated"/>
        </jp:createExperiment>

        <jp:createExperiment key="Short1PeriodTopUpSEONIos">
            <jp:variation key="period15TopUp50Seon"/>
            <jp:variation key="period15TopUp50SeonCompensated"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="lessAdsGamesOnboarding-advariant2" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208393592093823/f">
        <sql>
            INSERT INTO playtime.ab_variations (allocation_ratio, `key`, experiment_id)
            VALUES
            (0.0, 'lessAds3hadv2', (SELECT id FROM playtime.ab_experiments WHERE `key` = 'lessAdsGamesOnboarding'));
        </sql>
    </changeSet>

    <changeSet id="add-faceScanPreScreen-experiment" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1208233267036954/f">
        <jp:createExperiment key="androidFaceScanPreScreen" minimumAppVersion="64">
            <jp:variation key="fullScreenAllow"/>
            <jp:variation key="fullScreenHuman"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-ios-players-online-users-exp" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1206562844951339/f">
        <sql>UPDATE playtime.ab_experiments SET finished_at = NOW() WHERE `key` = 'iosPlayersOnline' AND finished_at is NULL;</sql>
    </changeSet>

    <changeSet id="cleanup-old-experiments" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208418946501984/f" runOnChange="true">
        <sqlFile path="sql/cleanup-old-experiments.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="user-offerwall-type-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900976/f">
        <createTable schemaName="playtime" tableName="user_offerwall_type">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_offerwall_type_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="offerwall_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_offerwall_type" columnNames="user_id,offerwall_type"
                       constraintName="pk_user_offerwall_type"/>
    </changeSet>

    <changeSet id="last-user-cashout-email-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900978/f">
        <createTable schemaName="playtime" tableName="user_last_cashout_data">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_user_last_cashout_data_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="encrypted_email" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="encrypted_name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="cashout_transaction_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_last_cashout_data_cashout_transaction_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="user_cashout_transactions"
                             referencedColumnNames="id"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-notifications-id-to-promotions-setup" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900976/f">
        <addColumn tableName="cfg_offerwall_rule" schemaName="playtime">
            <column name="notification_id" type="INT">
                <constraints nullable="true"
                             foreignKeyName="fk_cfg_offerwall_rule_notification_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="scheduled_generic_notification"
                             referencedColumnNames="id"
                />
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="cron-temp-data-row-promotions-notifications" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900976/f">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('schedulePromotionsNotifications', '0', now());
        </sql>
    </changeSet>

    <changeSet id="exp-offboard-ios-paypal-message" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1206398053292156/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'iosPaymentsIssueMessage' AND finished_at is NULL;
        </sql>
    </changeSet>
    <changeSet id="add-unified_id_optout_user-table" author="vitalii.sirotkin"
               labels="https://app.asana.com/0/1206346880021737/1208436115250033/f">
        <createTable tableName="unified_id_optout_user" schemaName="playtime">
            <column name="user_id" type="varchar(36)">
                <constraints foreignKeyName="fk_unified_id_optout_user_user_id"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-aa-test-experiment" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1207078233540147/1208540897213404/f">
        <jp:createExperiment key="aaTest">
            <jp:variation key="groupA"/>
            <jp:variation key="groupB"/>
            <jp:variation key="groupC"/>
            <jp:variation key="groupD"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="adjust-promotions-ofw-config-name" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208468407200846/f">
        <modifyDataType schemaName="playtime" tableName="cfg_offerwall_rule" columnName="name" newDataType="varchar(50)"/>
    </changeSet>


    <changeSet id="add-treasuremaster-webgl-games" author="andrei.khripushin" labels="PLAT-2226">
        <sqlFile path="sql/add-treasuremaster-webgl.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-android-paypal-limits-experiment" author="vyacheslav.yasinskiy" labels="https://app.asana.com/0/1207078233540147/1208387029978466/f">
        <jp:createExperiment key="androidPaypalLimits">
            <jp:variation key="androidNoLimits"/>
            <jp:variation key="androidMinimum3usd"/>
            <jp:variation key="androidMinimum4usd"/>
            <jp:variation key="androidMinimum5usd"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="images-only-game-widget-tm-new-image" author="pavel.novikov"
               labels="https://app.asana.com/0/1207810850102739/1208412129378518/f">
        <jp:addVariations experimentKey="androidImagesOnly">
            <jp:variation key="tmNewImage" allocationRatio="0.0"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-game-order-experiment" author="pavel.novikov" labels="PLAT-2305">
        <jp:createExperiment key="androidGamesOrder">
            <jp:variation key="fixedFirst11GamesOrder"/>
            <jp:variation key="coinsFirst11GamesOrder"/>
        </jp:createExperiment>
        <sql>
            UPDATE playtime.ab_experiments
            SET finished_at = NOW()
            WHERE `key` = 'gamesOrder2' AND finished_at is NULL;
        </sql>
    </changeSet>

    <changeSet id="(Offboard) Exp: Horizontal list for games" author="pavel.novikov" labels="PLAT-2318">
        <jp:closeExperiment experimentKey="androidHorizontalLists"/>
    </changeSet>

    <changeSet id="add-offerwall-type-to-promotions-setup" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208379401255026/f">
        <addColumn tableName="cfg_offerwall_rule" schemaName="playtime">
            <column name="offerwall_type" type="VARCHAR(20)" defaultValue="TAPJOY"/>
        </addColumn>
        <update schemaName="playtime" tableName="cfg_offerwall_rule">
            <column name="offerwall_type" value="TAPJOY"/>
        </update>
    </changeSet>

    <changeSet id="cron-temp-data-promotions-notifications-fyber" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208379401255026/f">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('schedulePromotionsNotificationsFyber', '0', now());
        </sql>
    </changeSet>

    <changeSet id="ios-translations-sync-2024-oct-24" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1208626512311395/f">
        <sqlFile path="sql/ios-translations-2024-10-24.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="fyber-coins-report-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208379401255026/f">
        <createTable schemaName="playtime" tableName="fyber_coin_reports">
            <column name="transaction_id" type="VARCHAR(100)">
                <constraints nullable="false"
                             primaryKey="true"
                             primaryKeyName="pk_fyber_coin_reports"
                />
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_fyber_coin_reports_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="amount" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="currency_name" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="currency_id" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="payout" type="DECIMAL(16,6)">
                <constraints nullable="false"/>
            </column>
            <column name="vcs_enabled" type="bool">
                <constraints nullable="false"/>
            </column>
            <column name="signature" type="VARCHAR(40)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="offboard-experiment-preselected-paypal-on-android" author="vyacheslav.yasinskiy" labels="PLAT-2280">
        <jp:closeExperiment experimentKey="androidPreselectedPayPal"/>
    </changeSet>

    <changeSet id="android-blockbuster-game-rewarding-logic" author="andrei.khripushin"
               labels="https://app.asana.com/0/1155692811605665/1208621202703459/f">
        <sqlFile path="sql/add-blockbuster-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="halloween-2024-ofw-promotions-setup" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1199943047294494/1208599076729487/f">
        <sqlFile path="sql/halloween-2024-ofw.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="cashout-revenue-tracking-table-add-extra-revenue-column" author="cip.malos"
               labels="https://app.asana.com/0/1204984796219779/1208650292726041">
        <addColumn tableName="earnings_calculation_data" schemaName="playtime">
            <column name="extra_revenue" type="DECIMAL(16,12)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="balance-update-notification-experiment" author="vitalii.sirotkin"
               labels="PLAT-2276">
        <jp:createExperiment key="androidGamesBalanceUpdateNotifications">
            <jp:variation key="gameBalanceNotifications"/>
        </jp:createExperiment>
        <jp:createExperiment key="iosGamesBalanceUpdateNotifications">
            <jp:variation key="gameBalanceNotifications"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="app-full-rollout-android-64" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1729501548777969">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (64, '2024-10-09');
        </sql>
    </changeSet>

    <changeSet id="em2-game-equalizing-tangram-2024-11-02-us-and-default" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C026ZG18DNC/p1729512066830039"
               context="!au-test AND !au AND !gb-test AND !gb">
        <sql>
            INSERT INTO playtime.game_equalizer_coeffs (application_id, multiplier)
            VALUES ('com.gimica.tangram', 1)
            ON DUPLICATE KEY
            UPDATE multiplier = VALUES (multiplier);
        </sql>
    </changeSet>

    <changeSet id="em2-game-equalizing-tangram-2024-11-02-gb" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C026ZG18DNC/p1729512066830039"
               context="gb-test OR gb">
        <sql>
            INSERT INTO playtime.game_equalizer_coeffs (application_id, multiplier)
            VALUES ('com.gimica.tangram', 2)
            ON DUPLICATE KEY
            UPDATE multiplier = VALUES (multiplier);
        </sql>
    </changeSet>

    <changeSet id="veterans-day-2024-ofw-promotions-setup"
               author="pavel.novikov"
               labels="PLAT-2346"
               context="us or us-test or us-staging or us-staging-test or unit-test">
        <sqlFile path="sql/veterans-day-2024-ofw.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="veterans-day-2024-non-us-ofw-promotions-setup"
               author="pavel.novikov"
               labels="PLAT-2346"
               context="!us AND !us-test AND !us-staging AND !us-staging-test AND !ios-us AND !ios-us-test">
        <sqlFile path="sql/veterans-day-2024-non-us-ofw.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-ios-newsV2-experiment" author="andrei.khripushin" labels="PLAT-2344">
        <jp:createExperiment key="iosNewsV2" minimumAppVersion="136">
            <jp:variation key="iosNewsV2"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="fix-ios-facetec-translation" author="vitalii.sirotkin">
        <sqlFile path="sql/fix-ios-facetec-translation.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="new-variation-hide-cashout" author="pavel.novikov"
               labels="PLAT-2253">
        <jp:addVariations experimentKey="androidHideEarnings">
            <jp:variation key="androidHideEarningsForAllInFirstCp"/>
            <jp:variation key="androidHideEarningsForAllInAllCp"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="promotions-multi-push-notifications" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1207810850102739/1208599076729488/f">
        <dropForeignKeyConstraint baseTableSchemaName="playtime"
                                  baseTableName="cfg_offerwall_rule"
                                  constraintName="fk_cfg_offerwall_rule_notification_id"/>
        <createTable schemaName="playtime" tableName="promotions_push_notification">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_promotions_push_notification"/>
            </column>
            <column name="promotion_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_promotions_push_notification_promotion_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="cfg_offerwall_rule"
                             referencedColumnNames="id"
                />
            </column>
            <column name="scheduled_notification_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_promotions_push_notification_notification_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="scheduled_generic_notification"
                             referencedColumnNames="id"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-sendgrid-id-to-promotions-setup" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208610879521483/f">
        <addColumn tableName="cfg_offerwall_rule" schemaName="playtime">
            <column name="sendgrid_template_id" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>

    <changeSet id="promotions-emails-part-3" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900978/f">
        <createTable schemaName="playtime" tableName="promotions_email_sending">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_promotions_email_sending"/>
            </column>
            <column name="promotion_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_promotions_email_sending_promotion_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="cfg_offerwall_rule"
                             referencedColumnNames="id"
                />
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_promotions_email_sending_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="sent_at" type="TIMESTAMP"/>
        </createTable>
    </changeSet>

    <changeSet id="cron-temp-data-promotions-emails" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208043004900978/f">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('sendPromotionsEmails', '0', now());
        </sql>
    </changeSet>

    <changeSet id="android-install-game-reminder" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208576276195642/f">
        <jp:createExperiment key="androidInstallGamesReminders">
            <jp:variation key="intense24hGoogleStore"/>
            <jp:variation key="intense24hPregameScreen"/>
            <jp:variation key="6daysHomeScreen"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-recently-played-games" author="pavel.novikov" labels="PLAT-2119">
        <jp:closeExperiment experimentKey="playedGamesTab"/>
    </changeSet>

    <changeSet id="offboard-exp-android-game-widget-tracker" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207811409825823/f">
        <jp:closeExperiment experimentKey="androidGameWidgetTracker"/>
    </changeSet>

    <changeSet id="fix-coins-goal" author="pavel.novikov" labels="PLAT-2341">
        <sqlFile path="sql/fix-coins-goal.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="email_verification_result_encrypted_columns" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1208723246024986">
        <sql>
            ALTER TABLE playtime.email_verification_result ADD COLUMN encrypted_email VARCHAR(750);

            ALTER TABLE playtime.email_verification_result ADD COLUMN email_hash VARCHAR(64);
            ALTER TABLE playtime.email_verification_result ADD INDEX idx_email_verification_result__email_hash (email_hash);
        </sql>
    </changeSet>

    <changeSet id="new_pk_email_verification_result" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1208723246024986">
        <sql>
            ALTER TABLE playtime.email_verification_result DROP PRIMARY KEY, ADD PRIMARY KEY pk_email_verification_result__id_email_hash (user_id, email_hash);
        </sql>
    </changeSet>

    <changeSet id="remove_email_from_email_verification_result" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204314780553189/1208723246024986">
        <sql>
            ALTER TABLE playtime.email_verification_result MODIFY COLUMN email VARCHAR(100) NOT NULL DEFAULT '';

        </sql>
    </changeSet>

    <changeSet id="offboard-exp-android-coins-separator" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1207040258358553/f">
        <jp:closeExperiment experimentKey="androidCoinsAmountSeparator"/>
    </changeSet>

    <changeSet id="exp-higher-quotas" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208723596741529/f">
        <jp:createExperiment key="higherDailyQuotas">
            <jp:variation key="adjusted"/>
            <jp:variation key="50more"/>
            <jp:variation key="100more"/>
            <jp:variation key="200more"/>
            <jp:variation key="400more"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="add-ios-paypal-limits-experiment" author="vyacheslav.yasinskiy" labels="https://app.asana.com/0/1207078233540147/1208648200480128/f">
        <jp:createExperiment key="iosPaypalLimits">
            <jp:variation key="iosNoLimits"/>
            <jp:variation key="iosMinimum3usd"/>
            <jp:variation key="iosMinimum4usd"/>
            <jp:variation key="iosMinimum5usd"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="exp-visual-trust" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208436239105011/f">
        <jp:createExperiment key="visualTrustAndroid">
            <jp:variation key="allScreens"/>
            <jp:variation key="faceAndGeo"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="drop-old-coins-table-2" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208436239105011/f">
        <dropTable schemaName="playtime" tableName="user_game_coins_balances"/>
    </changeSet>

    <changeSet id="exp-dont-reset-coins-min-app-version-64" author="alex.potolitcyn"
               labels="https://app.asana.com/0/1155692811605665/1208088168760674/f">
        <sql>
            UPDATE playtime.ab_experiments
            SET minimum_app_version = 64
            WHERE `key`='coinsDoNotResetAndroid';
        </sql>
    </changeSet>

    <changeSet id="black_friday_2024" author="pavel.novikov" labels = "PLAT-2381">
        <createTable tableName="promotion_event_cfg">
            <column name="promotion_event_key" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="pk_promotion_event_cfg_key"/>
            </column>
            <column name="date_from" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="date_to" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="black_friday_2024_2" author="pavel.novikov" labels="PLAT-2381">
        <sqlFile path="sql/black-friday-2024.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ofw-promotions-2024-black-friday" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208599076729489/f">
        <sqlFile path="sql/promotions/thanksgiving-black-friday-2024.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ofw-promotions-2024-christmas" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208599076729490/f">
        <sqlFile path="sql/promotions/christmas-2024.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ofw-promotions-2024-new-year" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208599076729492/f">
        <sqlFile path="sql/promotions/new-year-2024.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="black_friday_2024_3" author="pavel.novikov" labels="PLAT-2381_hf">
        <sqlFile path="sql/black-friday-2024_2.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ios-fairytale-description" author="aleksey.romantsov" labels="PLAT-2415">
        <sql>
            UPDATE playtime.string_resource_translation
            SET string_resource_translation.translation =
            (IF(language = 'ja', '魔法のレベルをクリアして、おとぎ話の世界を楽しんで、ロイヤルティコインを手に入れましょう。',
            'Complete magical levels to enjoy a fairy tale and earn loyalty coins'))
            WHERE resource_name = '$_ios_fairytalematch_description';
        </sql>
    </changeSet>

    <changeSet id="ios-change-coin-update-text" author="pavel.novikov" labels="PLAT-2416">
        <sqlFile path="sql/ios-change-coin-update-text.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id="offboard-experiment-one-click-cashout-on-android" author="vyacheslav.yasinskiy" labels="PLAT-2159">
        <jp:closeExperiment experimentKey="androidOneClickCashout"/>
    </changeSet>

    <changeSet id="offboard-experiment-one-click-cashout-on-android-translations" author="vyacheslav.yasinskiy"
               labels="PLAT-2159">
        <sqlFile path="sql/android_onclick_cashout.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="new-variation-hide-cashout" author="alex.potolitcyn" labels="PLAT-2358">
        <jp:addVariations experimentKey="Short1PeriodTopUpSEONAndroid">
            <jp:variation key="period4hAllTopUpSeon"/>
            <jp:variation key="period5hAllTopUpSeon"/>
            <jp:variation key="period1hTopUpSeon"/>
        </jp:addVariations>

        <jp:addVariations experimentKey="Short1PeriodTopUpSEONIos">
            <jp:variation key="period4hAllTopUpSeon"/>
            <jp:variation key="period5hAllTopUpSeon"/>
            <jp:variation key="period1hTopUpSeon"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="ofw-promotions-2024-add-email-template-ids" author="aleksey.romantsov"
               labels="PLAT-2347, PLAT-2348, PLAT-2349">
        <update schemaName="playtime" tableName="cfg_offerwall_rule">
            <column name="sendgrid_template_id" value="d-55f83c6e493b4d4ea741c149dfb79104"/>
            <where>
                date_from = timestamp '2024-12-01 00:00:00' AND name = 'Black Friday BONANZA! Get 150% MORE PAYOUTS Today!'
            </where>
        </update>
        <update schemaName="playtime" tableName="cfg_offerwall_rule">
            <column name="sendgrid_template_id" value="d-a273d4fe74914a35b8e13ff7c2964cfd"/>
            <where>
                date_from = timestamp '2024-12-24 00:00:00' AND name = 'Unwrap 150% MORE PAYOUTS this Christmas Season!'
            </where>
        </update>
        <update schemaName="playtime" tableName="cfg_offerwall_rule">
            <column name="sendgrid_template_id" value="d-d4ab3f26973b46ce804081b2009ef904"/>
            <where>
                date_from = timestamp '2024-12-30 00:00:00' AND name = 'New Rewards! Start 2025 with 150% More PAYOUTS!'
            </where>
        </update>
    </changeSet>

    <changeSet id="lift-max-earnings-threshold" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208750179860753/f">
        <createTable schemaName="playtime" tableName="user_unpaid_earnings">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_unpaid_earnings_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="amount_usd" type="DECIMAL(10,6)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(16,6)">
                <constraints nullable="false"/>
            </column>
            <column name="currency_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-ipregistry-columns" author="vitalii.sirotkin" labels="PLAT-2385">
        <addColumn tableName="ip_registry_data" schemaName="playtime">
            <column name="country_code" type="varchar(10)"/>
            <column name="country_name" type="varchar(100)"/>
            <column name="region_name" type="varchar(100)"/>
            <column name="city_name" type="varchar(100)"/>
            <column name="time_zone" type="varchar(100)"/>
        </addColumn>
    </changeSet>


    <changeSet id = "liveops-support" author="pavel.novikov">
        <sqlFile path="sql/liveops-support.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-variant-7-games-and-all-ios" author="pabvel.novikov">
        <jp:addVariations experimentKey="iosNewGamesAmount">
            <jp:variation key="amount7"/>
            <jp:variation key="amountAll"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="ios-change-exp-description-text" author="pavel.novikov" labels="PLAT-2430">
        <sqlFile path="sql/ios-change-exp-description-text.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="remove-tm-exclusive-content-from-ios-news" author="andrei.khripushin" labels="PLAT-2421">
        <sql>
            DELETE FROM playtime.ios_news WHERE order_key = 14;
        </sql>
    </changeSet>

    <changeSet id="android-remove-privacy-policy-redirect" author="eduard.trushnikov" labels="PLAT-2460">
        <sql>
            UPDATE playtime.app_translation
            SET translation='https://justplayapps.com/privacy-policy/'
            WHERE resource_name='privacy_policy_url' AND app_platform='ANDROID' AND translation='https://www.justplayapps.com/privacy-policy';
        </sql>
    </changeSet>

    <changeSet id="online-experiment-assigning-mechanics" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208814668718622/f">
        <createTable schemaName="playtime" tableName="ab_forced_assignment">
            <column name="experiment_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_ab_forced_assignment_experiment_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="ab_experiments"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="is_active" type="BOOL">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-variations-exp-best-coins-badge-android-android" author="vyacheslav.yasinskiy"
               labels="PLAT-2406" >
        <jp:addVariations experimentKey="bestCoinsBadge">
            <jp:variation key="showFirstGameBadgeAndroid"/>
            <jp:variation key="showAllGamesBadgeAndroid"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="add-modified-unclaimed-earnings-notifications-exp" author="vitalii.sirotkin" labels="PLAT-2401">
        <jp:createExperiment key="modifiedUnclaimedEarningsNotification">
            <jp:variation key="enhancedDefaultCycle"/>
            <jp:variation key="enhanced24hCycle"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="live-ops-labels-support" author="pavel.novikov" labels="PLAT-2443">
        <createTable tableName="promotion_event_translation" schemaName="playtime">
            <column name="promotion_event_key" type="varchar(50)">
                <constraints foreignKeyName="fk_promotion_event_cfg_translation"
                             referencedTableSchemaName="playtime"
                             referencedTableName="promotion_event_cfg"
                             referencedColumnNames="promotion_event_key"
                             nullable="false"
                />
            </column>
            <column name="resource_name" type="varchar(100)">
                <constraints foreignKeyName="fk_promotion_event_resource_translation"
                             referencedTableSchemaName="playtime"
                             referencedTableName="string_resource"
                             referencedColumnNames="resource_name"
                             nullable="false"
                />
            </column>
            <column name="original_resource_name" type="varchar(100)">
                <constraints foreignKeyName="fk_promotion__event_original_resource_translation"
                             referencedTableSchemaName="playtime"
                             referencedTableName="string_resource"
                             referencedColumnNames="resource_name"
                             nullable="false"
                />
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime"
                       tableName="promotion_event_translation"
                       columnNames="promotion_event_key,original_resource_name"
                       constraintName="pk_promotion_event_translation"/>
    </changeSet>

    <changeSet id="exp-goal-messages" author="alex.potolitcyn" labels="PLAT-2386">
        <jp:createExperiment key="goalMessages">
            <jp:variation key="playMore"/>
            <jp:variation key="percentGoal"/>
            <jp:variation key="earnBonus"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="alternating-rewarding-experiment" author="pavel.novikov" labels="PLAT-2418_PLAT-2419">
        <jp:createExperiment key="gameScoringMakeTen">
            <jp:variation key="alternatingRewarding"/>
        </jp:createExperiment>
        <jp:createExperiment key="gameScoringColorLogic">
            <jp:variation key="alternatingRewarding"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="one-click-cashout-on-android-translations-fix" author="vyacheslav.yasinskiy"
               labels="PLAT-2470">
        <sqlFile path="sql/android_onclick_cashout_translations_fix.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="mad-smash-dynamic-ad-unit" author="pavel.novikov" labels="PLAT-2431_PLAT-2432">
        <sqlFile path="sql/mad-smash-dynamic-ad-unit.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen" author="alex.potolitcyn" labels="PLAT-2467">
        <jp:createExperiment key="tutorialFullScreen" minimumAppVersion="67">
            <jp:variation key="tutorialFullScreen"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-reviews" author="alex.potolitcyn" labels="https://app.asana.com/0/1155692811605665/1208920928450678/f">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="tutorialFullScreenAndReviews"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-cashout-quit-confirmation-bonus-coins" author="eduard.trushnikov"
               labels="PLAT-2388">
        <jp:updateMinAppVersion experimentKey="androidCashoutForCoins" newMinimumAppVersion="66"/>
        <jp:addVariations experimentKey="androidCashoutForCoins">
            <jp:variation key="cashoutQuitConfirmation"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-face-scan-full-pre-screen-text-update" author="eduard.trushnikov" labels="PLAT-2387">
        <sqlFile path="sql/translations_2024_12_02_face_scan_full_pre_screen.sql" relativeToChangelogFile="true"/>
        <jp:updateMinAppVersion experimentKey="androidFaceScanPreScreen" newMinimumAppVersion="66"/>
    </changeSet>

    <changeSet id = "images-only-offboard" author="pavel.novikov" labels="PLAT-2248-offboard">
        <jp:closeExperiment experimentKey="androidImagesOnly"/>
        <sqlFile path="sql/android-images-only-offboard.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="calculate-revenue-totals-on-fly" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208610879521483/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="playtime" tableName="generic_revenue_daily_totals" indexName="idx_generic_revenue_daily_totals__triple_uniq"/>
            </not>
        </preConditions>
        <createIndex schemaName="playtime" tableName="generic_revenue_daily_totals" indexName="idx_generic_revenue_daily_totals__triple_uniq" unique="true">
            <column name="user_id"/>
            <column name="for_day"/>
            <column name="source"/>
        </createIndex>
    </changeSet>

    <changeSet id="turn-off-offerwall-type-experiments" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1207877622501275/f">
        <jp:closeExperiment experimentKey="offerWallType"/>
        <jp:closeExperiment experimentKey="tapjoy"/>
        <jp:closeExperiment experimentKey="offerwallPromotionsV2"/>
    </changeSet>

    <changeSet id="add-webgl-games" author="vitalii.sirotkin"
               labels="PLAT-2479">
        <createTable tableName="games_webgl">
            <column name="id" type="int" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="game_id" type="int">
                <constraints referencedTableName="games" referencedColumnNames="id"
                             foreignKeyName="fk_games_webgl_game_id" nullable="false"/>
            </column>
            <column name="webgl_url" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="demo_for_game_id" type="int">
                <constraints referencedTableName="games" referencedColumnNames="id"
                             foreignKeyName="fk_games_webgl_demo_for_game_id"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="rename-existing-demo-games" author="vitalii.sirotkin"
               labels="PLAT-2479">
        <sql>
            UPDATE playtime.games SET application_id = 'com.gimica.treasuremaster.webgl.demo', name = 'Treasure Master WebGL Demo version' WHERE id in (200073, 500016);
        </sql>
    </changeSet>

    <changeSet id="prefill-webgl-games" author="vitalii.sirotkin"
               labels="PLAT-2479">
        <sqlFile path="sql/prefill-webgl-games.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-explore-variations" author="alex.potolitcyn" labels="PLAT-2468">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="exploreNowFullScreen"/>
            <jp:variation key="exploreNowFullScreenHand"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="exp-multi-offerwall" author="alex.potolitcyn" labels="PLAT-2480">
        <jp:closeExperiment experimentKey="androidAdjoeOfferwall"/>
        <jp:createExperiment key="multiOfferwall" minimumAppVersion="62">
            <jp:variation key="adjoeOnly"/>
            <jp:variation key="addAdjoe"/>
            <jp:variation key="tapjoyDt"/>
            <jp:variation key="tapjoyAdjoe"/>
            <jp:variation key="dtAdjoe"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-tinder-of-games-new-variations" author="andrei.khripushin" labels="PLAT-2513">
        <jp:addVariations experimentKey="androidPickPreferredGames">
            <jp:variation key="preferredTopDollar"/>
            <jp:variation key="preferredTopFunTags"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="coins-for-video-text-update" author="vyacheslav.yasinskiy" labels="PLAT-2507">
        <sqlFile path="sql/coins_for_video_text_update.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="exp-android-fullscreen-cashout" author="eduard.trushnikov"
               labels="PLAT-2465,https://app.asana.com/0/1203013731295607/1208903255578968/f">
        <jp:createExperiment key="androidFullscreenCashout" minimumAppVersion="68">
            <jp:variation key="fullscreenCashout"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="exp-updated-order-android" author="pavel.novikov" labels="PLAT-2540">
        <jp:closeExperiment experimentKey="androidGamesOrder"/>
        <jp:createExperiment key="newDefaultOrderAndroid">
            <jp:variation key="androidNdOrder"/>
            <jp:variation key="androidNd1Order"/>
            <jp:variation key="androidNd7Order"/>
            <jp:variation key="androidNd30Order"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="undeleted-faces-sql-optimization" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1734694785615209">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="playtime" tableName="user_verification_session" indexName="idx_user_verification_session_created_at_is_face_map_deleted"/>
            </not>
        </preConditions>
        <createIndex schemaName="playtime" tableName="user_verification_session" indexName="idx_user_verification_session_created_at_is_face_map_deleted">
            <column name="is_face_map_deleted"/>
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <changeSet id="app-full-rollout-android-65" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1731922169963789">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (65, '2024-11-18');
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-android-66" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1733654968079159">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (66, '2024-12-08');
        </sql>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-min-app-version-68" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C03FPUVJCRX/p1733395952979809">
        <jp:updateMinAppVersion experimentKey="tutorialFullScreen" newMinimumAppVersion="68"/>
    </changeSet>

    <changeSet id="weekly-challenges-part1" author="pavel.novikov">
        <jp:createExperiment key="androidChallenges">
            <jp:variation key="showChallenges"/>
            <jp:variation key="showChallengesPromo"/>
        </jp:createExperiment>
        <createTable tableName="challenge_event">
            <column name="id" type="varchar(36)">
                <constraints primaryKey="true" primaryKeyName="pk_challenge_event_id"/>
            </column>
            <column name="date_from" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="date_to" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="cfg" type="json">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="challenge">
            <column name="id" type="varchar(36)">
                <constraints primaryKey="true" primaryKeyName="pk_challenge_id"/>
            </column>
            <column name="event_id" type="varchar(36)">
                <constraints
                    foreignKeyName="fk_challenge_challenge_event_id"
                    referencedTableSchemaName="playtime"
                    referencedTableName="challenge_event"
                    referencedColumnNames="id"
                    nullable="false"
                />
            </column>
            <column name="title" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="icon" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="progress_max" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="game_id" type="integer">
                <constraints
                        foreignKeyName="fk_challenge_game_id"
                        referencedTableSchemaName="playtime"
                        referencedTableName="games"
                        referencedColumnNames="id"
                        nullable="false"

                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="user_challenge_event" schemaName="playtime">
            <column name="user_id" type="varchar(36)">
                <constraints
                        foreignKeyName="fk_user_challenge_event_user"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                        nullable="false"
                />
            </column>
            <column name="event_id" type="varchar(36)">
                <constraints
                        foreignKeyName="fk_user_challenge_event_event"
                        referencedTableSchemaName="playtime"
                        referencedTableName="challenge_event"
                        referencedColumnNames="id"
                        nullable="false"
                />
            </column>
            <column name="state" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="earnings_amount" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="user_challenge" schemaName="playtime">
            <column name="user_id" type="varchar(36)">
                <constraints
                        foreignKeyName="fk_user_challenge_user"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                        nullable="false"
                />
            </column>
            <column name="challenge_id" type="varchar(36)">
                <constraints
                        foreignKeyName="fk_user_challenge_challenge"
                        referencedTableSchemaName="playtime"
                        referencedTableName="challenge"
                        referencedColumnNames="id"
                        nullable="false"
                />
            </column>
            <column name="state" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="progress" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="coins" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="typed-offerwall-configuration" author="alex.potolitcyn" labels="PLAT-2480">
        <addColumn schemaName="playtime" tableName="cfg_offerwall">
            <column name="offerwall_type" type="varchar(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="blockbuster-translations" author="alex.potolitcyn" labels="PLAT-2408">
        <sqlFile path="sql/games/blockbuster-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="mass-email-notifications-support" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208952273005921/f">
        <createTable schemaName="playtime" tableName="scheduled_email_notification">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_scheduled_email_notification"/>
            </column>
            <column name="sendgrid_template_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="send_after" type="TIMESTAMP"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="playtime" tableName="scheduled_email_notification" indexName="idx_scheduled_email_notification__send_after">
            <column name="send_after"/>
        </createIndex>
        <createTable schemaName="playtime" tableName="scheduled_email_notifications_sending">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_sched_email_notifications_sending"/>
            </column>
            <column name="notification_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_user_sched_email_sending_notification_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="scheduled_email_notification"
                             referencedColumnNames="id"
                />
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_sched_email_sending_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="sent_at" type="TIMESTAMP"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="active-users-poc" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208936923730182/f">
        <createTable tableName="active_users" schemaName="playtime">
            <column name="user_id" type="varchar(36)">
                <constraints
                        foreignKeyName="fk_active_users_user"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                        nullable="false"
                        primaryKey="true"
                />
            </column>
            <column name="last_active_at_day" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="playtime" tableName="active_users" indexName="idx_active_users__last_active_at_day">
            <column name="last_active_at_day"/>
        </createIndex>
    </changeSet>

    <changeSet id="register-tmp-data-for-active-users-tidy-up-cron-job" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208936923730182/f">
        <sql>
            INSERT INTO `playtime`.`cron_temp_data` (`cron_job`, `last_chunk_user_id_first_letter`, `processed_at`)
            VALUES ('tidyUpActiveUsers', '0', now());
        </sql>
    </changeSet>

    <changeSet id="generic-revenue-daily-totals-mk2-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208610879521483/f">
        <createTable schemaName="playtime" tableName="generic_revenue_daily_totals_mk2">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_generic_revenue_daily_totals_mk2_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="revenue" type="DECIMAL(16,12)">
                <constraints nullable="false"/>
            </column>
            <column name="source" type="VARCHAR(12)">
                <constraints nullable="false"/>
            </column>
            <column name="for_day" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime"
                       tableName="generic_revenue_daily_totals_mk2"
                       columnNames="for_day,user_id,source"
                       constraintName="pk_generic_revenue_daily_totals_mk2"/>
    </changeSet>

    <changeSet id="small-offers-config-from-be-struct" author="alex.potolitcyn" labels="PLAT-2545">
        <addColumn schemaName="playtime" tableName="additional_offers">
            <column name="title" type="varchar(1000)"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="additional_offers">
            <column name="left_icon" type="varchar(1000)"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="additional_offers">
            <column name="right_icon" type="varchar(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="small-offers-config-from-be-resources" author="alex.potolitcyn" labels="PLAT-2545">
        <sqlFile path="sql/offerwall-small-items-resources.sql" relativeToChangelogFile="true"/>
        <sqlFile path="sql/addition-offers-offerwalls-be.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="update-order-key-for-tangram" author="pavel.novikov" labels="https://app.asana.com/0/1207810850102739/1209041194207142/fT">
        <sqlFile path="sql/update-tangram-order-key.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="tile-match-promo" author="pavel.novikov" labels="PLAT-2506">
        <jp:createExperiment key="androidHighlightTileMatchPro">
            <jp:variation key="simple"/>
            <jp:variation key="communication"/>
            <jp:variation key="promise"/>
            <jp:variation key="promiseAndDelivery"/>
        </jp:createExperiment>
        <jp:createExperiment key="iosHighlightTileMatchPro">
            <jp:variation key="simple"/>
            <jp:variation key="communication"/>
            <jp:variation key="promise"/>
            <jp:variation key="promiseAndDelivery"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-ios-preselected-paypal-exp" author="vyacheslav.yasinskiy"
               labels="PLAT-2162">
        <jp:closeExperiment experimentKey="iosPreselectedPayPal"/>
    </changeSet>

    <changeSet id="app-full-rollout-android-67" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1735722001659949">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (67, '2025-01-01');
        </sql>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-explore-1st-game-with-popup" author="eduard.trushnikov"
               labels="PLAT-2570,https://app.asana.com/0/1155692811605665/1209121512613870/">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="exploreNowPopup"/>
            <jp:variation key="exploreNowPopupHand"/>
        </jp:addVariations>
        <jp:updateMinAppVersion experimentKey="tutorialFullScreen" newMinimumAppVersion="67"/>
    </changeSet>

    <changeSet id="offboard-experiment-ios-custom-game-pages-v1" author="vyacheslav.yasinskiy" labels="PLAT-2158">
        <jp:closeExperiment experimentKey="iosCustomGamePagesV1"/>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-install-game-variation" author="alex.potolitcyn"
               labels="PLAT-2563">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="fullScreenLtiTm"/>
            <jp:variation key="fullScreenLtiSolitaire"/>
            <jp:variation key="fullScreenLtiCarousel"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="create-webgl-ad-info-table" author="vitalii.sirotkin" labels="PLAT-2479">
        <createTable tableName="webgl_games_ad_info" schemaName="playtime">
            <column name="game_id" type="int">
                <constraints nullable="false"
                             foreignKeyName="fk_webgl_games_ad_info__game_id"
                             referencedTableName="games"
                             referencedColumnNames="id"
                             referencedTableSchemaName="playtime"/>
            </column>
            <column name="banner_placement" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="interstitial_placement" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="rewarded_placement" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="banner_ad_unit_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="interstitial_ad_unit_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="rewarded_ad_unit_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_highly_trusted" type="BOOL">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="webgl_games_ad_info"
                             columnNames="game_id, is_highly_trusted"/>
    </changeSet>

    <changeSet id="fill-webgl-placements" author="vitalii.sirotkin" labels="PLAT-2479">
        <sqlFile path="sql/prefill-webgl-ad-info.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="delete-webgl-and-demo-games" author="vitalii.sirotkin" labels="PLAT-2479">
        <delete tableName="games_webgl" schemaName="playtime">
            <where>game_id in (200073, 500016, 500017, 500018, 500019, 500020, 500021, 500022, 500023)</where>
        </delete>
        <delete tableName="games" schemaName="playtime">
            <where>id in (200073, 500016, 500017, 500018, 500019, 500020, 500021, 500022, 500023)</where>
        </delete>
    </changeSet>

    <changeSet id="modify-and-refill-webgl-table" author="vitalii.sirotkin" labels="PLAT-2479">
        <dropForeignKeyConstraint baseTableName="games_webgl" baseTableSchemaName="playtime" constraintName="fk_games_webgl_demo_for_game_id"/>
        <dropColumn tableName="games_webgl" schemaName="playtime" columnName="demo_for_game_id"/>
        <addColumn tableName="games_webgl" schemaName="playtime">
            <column name="demo_game_url" type="varchar(1000)"/>
        </addColumn>
        <dropNotNullConstraint tableName="games_webgl" columnName="webgl_url" columnDataType="varchar(1000)" schemaName="playtime"/>
        <sqlFile path="sql/refill-webgl-games.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="small-offers-config-from-be-struct-2" author="alex.potolitcyn" labels="PLAT-2545">
        <addColumn schemaName="playtime" tableName="additional_offers">
            <column name="body_text" type="varchar(1000)"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="additional_offers">
            <column name="reward_text" type="varchar(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="small-offers-config-from-be-data" author="alex.potolitcyn" labels="PLAT-2545">
        <sqlFile path="sql/offerwall-small-items-resources-2.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="adjoe-offerwall-configuration" author="alex.potolitcyn" labels="PLAT-2572">
        <sql>
            INSERT INTO playtime.cfg_offerwall (offerwall_type, name, description, button_text, image_filename, icon_filename, order_key, back_ground_color)
            VALUES ('ADJOE', '$_offerwall_cfg_name', '$_offerwall_cfg_description', '$_ofw_cfg_btn_text', 'offerwalls/adjoe_ofw_big.jpg', '', 10, null)
            ON DUPLICATE KEY UPDATE offerwall_type = VALUES (offerwall_type);
        </sql>
    </changeSet>

    <changeSet id="exp-less-ads-20-60" author="alex.potolitcyn"
               labels="PLAT-2563">
        <jp:addVariations experimentKey="lessAdsGamesOnboarding">
            <jp:variation key="lessAds60m"/>
            <jp:variation key="lessAds20m"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="store-video-reward" author="alex.potolitcyn" labels="PLAT-2565">
        <createTable schemaName="playtime" tableName="user_first_video_reward" >
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_first_video_reward_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                             primaryKeyName="pk_user_first_video_reward"
                />
            </column>
            <column name="reward" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="webgl-games-0.0.4" author="vitalii.sirotkin">
        <sqlFile path="sql/webgl-games/webgl-games-0.0.4.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="offboard-experiment-android-top-part-widgets" author="vyacheslav.yasinskiy" labels="PLAT-2324">
        <jp:closeExperiment experimentKey="androidTopPartWidgets"/>
    </changeSet>

    <changeSet id="offboard-experiment-play-store-notification" author="vyacheslav.yasinskiy" labels="PLAT-2115">
        <jp:closeExperiment experimentKey="playstoreTrackingNotifications"/>
    </changeSet>

    <changeSet id="rename-old-daily-revenue-table" author="aleksey.romantsov" labels="PLAT-2326">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="playtime" tableName="generic_revenue_daily_totals_old"/>
            </not>
        </preConditions>
        <renameTable schemaName="playtime"
                     oldTableName="generic_revenue_daily_totals"
                     newTableName="generic_revenue_daily_totals_old"/>
        <createView schemaName="playtime" viewName="generic_revenue_daily_totals">
            SELECT * FROM playtime.generic_revenue_daily_totals_mk2
        </createView>
    </changeSet>

    <changeSet id="delete-daily-revenue-cron-tmp-data" author="aleksey.romantsov" labels="PLAT-2326">
        <delete tableName="cron_temp_data" schemaName="playtime">
            <where>cron_job = 'saveGenericRevenueDailyTotals'</where>
        </delete>
    </changeSet>

    <changeSet id="update-webgl-adunitids-non-ht" author="vitalii.sirotkin">
        <update tableName="webgl_games_ad_info" schemaName="playtime">
            <column name="banner_ad_unit_id" value="caeb9695e585b792"/>
            <column name="interstitial_ad_unit_id" value="50710ec591a02e9c"/>
            <column name="rewarded_ad_unit_id" value="4a1659f2b75758a3"/>
            <where>is_highly_trusted = false</where>
        </update>
    </changeSet>

    <changeSet id="small-offers-api-adjust" author="alex.potolitcyn" labels="PLAT-2545">
        <sql>
            UPDATE playtime.additional_offers
            SET image_filename = left_icon
            WHERE left_icon IS NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="small-offers-config-from-be-video-ad" author="alex.potolitcyn" labels="PLAT-2545">
        <sqlFile path="sql/offerwall-small-items-resources-video-ad.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ab-force-assigning-participants-table" author="aleksey.romantsov"
               labels="https://app.asana.com/0/1155692811605665/1208814668718622/f">
        <createTable schemaName="playtime" tableName="ab_force_assigning_participants">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_ab_force_assigning_participants_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="experiment_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_ab_force_assigning_participants_experiment_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="ab_experiments"
                             referencedColumnNames="id"
                />
            </column>
            <column name="variation_id" type="INT">
                <constraints
                        foreignKeyName="fk_ab_force_assigning_participants_variation_id"
                        referencedTableSchemaName="playtime"
                        referencedTableName="ab_variations"
                        referencedColumnNames="id"
                />
            </column>
            <column name="processed_at" type="TIMESTAMP"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="ab_force_assigning_participants" columnNames="user_id,experiment_id"
                       constraintName="pk_ab_force_assigning_participants"/>
        <createIndex schemaName="playtime" tableName="ab_force_assigning_participants" indexName="idx_ab_force_assigning_participants__processed_user_id">
            <column name="processed_at"/>
            <column name="user_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="android-demo-games" author="andrei.khripushin" labels="PLAT-2597">
        <jp:createExperiment key="androidDemoGames" minimumAppVersion="68">
            <jp:variation key="installationMessageBefore"/>
            <jp:variation key="installationMessageAfter"/>
        </jp:createExperiment>
        <sqlFile path="sql/android-tm-demo-games-url.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="adjust-webgl-release-games-batch" author="vitalii.sirotkin">
        <sqlFile path="sql/webgl-games/first-release-batch.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="libraries-consent" author="alex.potolitcyn" labels="PLAT-2562">
        <createTable schemaName="playtime" tableName="user_privacy_library_consent" >
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_privacy_library_consent_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="library_name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="is_consented" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_privacy_library_consent"
                       columnNames="user_id, library_name"/>
    </changeSet>

    <changeSet id="em1p5-variations-of-em2-exp" author="aleksey.romantsov" labels="PLAT-2462">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em1p5"/>
            <jp:variation key="em1p4"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="offboard-experiment-android_coin_goals_v2" author="vyacheslav.yasinskiy" labels="PLAT-2254">
        <jp:closeExperiment experimentKey="androidCoinGoalsV2"/>
    </changeSet>

    <changeSet id="translations-2025-01-20" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1209192612919729/f,
                       https://app.asana.com/0/1208880936353700/1209124548471491/f,
                       PLAT-2589">
        <sqlFile path="sql/translations_2025_01_20_android_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="challenges-min-app-version" author="pavel.novikov" labels="PLAT-2571">
        <sql>
            update playtime.ab_experiments set minimum_app_version = 68 where `key` = 'androidChallenges';
        </sql>
        <addUniqueConstraint schemaName="playtime" tableName="user_challenge_event" columnNames="user_id, event_id"/>
        <addUniqueConstraint schemaName="playtime" tableName="user_challenge" columnNames="user_id, challenge_id"/>
        <addColumn schemaName="playtime" tableName="user_challenge">
            <column name="achievement" type="varchar(50)"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="challenge">
            <column name="calculator" type="varchar(50)"/>
        </addColumn>
        <addNotNullConstraint
                defaultNullValue="UNKNOWN"
                schemaName="playtime"
                tableName="challenge"
                columnName="calculator"
                validate="true"
                columnDataType="varchar(50)"
        />
        <sqlFile path="sql/challenges/treasure-master-20-levels-challenge.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-boosted-sign-translations" author="vyacheslav.yasinskiy" labels="PLAT-2446">
        <sqlFile path="sql/android-boosted-sign-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="one-more-bipa-resource" author="andrei.khripushin" labels="PLAT-2611">
        <sqlFile path="sql/ios-bipa-cancel.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-coin-goal-text" author="andrei.khripushin" labels="PLAT-2633">
        <jp:createExperiment key="androidCoinGoalText">
            <jp:variation key="longAggressive"/>
            <jp:variation key="shorterAggressive"/>
            <jp:variation key="shorter"/>
            <jp:variation key="shortAggressive"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-tutorial-text" author="andrei.khripushin" labels="PLAT-2636">
        <jp:createExperiment key="androidTutorialText">
            <jp:variation key="aggressiveAndTrustworthy"/>
            <jp:variation key="notAggressiveAndTrustworthy"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="emoji-support-for-offerwall-big-item" author="vyacheslav.yasinskiy"
               labels="https://app.asana.com/0/1155692811605665/1209260025693035/f">
        <sql>
            ALTER TABLE `playtime`.`cfg_offerwall_rule`
            MODIFY name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
            MODIFY description VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

    <changeSet id="translations-2025-01-31-ios-sync" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1209290199587183/f,
                       PLAT-2656">
        <sqlFile path="sql/translations_2025_01_31_ios_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="add-ramp-id-experiment" author="cip.malos" labels="INT-231">
        <jp:createExperiment key="rampId">
            <jp:variation key="rampId"/>
        </jp:createExperiment>
    </changeSet>
    <changeSet id="dynamic-ad-unit-more-games" author="andrei.khripushin" labels="PLAT-2607_PLAT-2606_PLAT-2605_PLAT-2604_PLAT-2601_PLAT-2600">
        <sqlFile path="sql/dynamic-ad-unit-more-games.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="games-coins-booster-experiment" author="andrei.khripushin" labels="PLAT-2596">
        <jp:createExperiment key="androidGamesCoinsBooster">
            <jp:variation key="androidGamesCoinsBoosterOn"/>
        </jp:createExperiment>
        <jp:createExperiment key="iosGamesCoinsBooster">
            <jp:variation key="iosGamesCoinsBoosterOn"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="replace-one-ios-resource-value" author="vyacheslav.yasinskiy" labels="PLAT-2646">
        <sqlFile path="sql/replace-one-ios-resource-value.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id="expand-device-specs" author="vyacheslav.yasinskiy"
               labels="PLAT-2615">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="user_device" columnName="font_scale"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="user_device">
            <column name="font_scale" type="DECIMAL(6,3)"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="user_device">
            <column name="density" type="INT"/>
        </addColumn>
        <addColumn schemaName="playtime" tableName="user_device">
            <column name="density_scale_factor" type="DECIMAL(6,3)"/>
        </addColumn>
    </changeSet>
    <changeSet id="change-contact-us-to-help" author="vyacheslav.yasinskiy" labels="PLAT-2635">
        <sqlFile path="sql/help-button-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="challenges-pks" author="alex.potolitcyn" labels="PLAT-2629">
        <addPrimaryKey schemaName="playtime" tableName="user_challenge" columnNames="user_id,challenge_id"/>
        <addPrimaryKey schemaName="playtime" tableName="user_challenge_event" columnNames="user_id,event_id"/>
    </changeSet>

    <changeSet id="challenge-event-reward-control-table" author="alex.potolitcyn" labels="PLAT-2629">
        <createTable schemaName="playtime" tableName="user_challenge_event_rewarded">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_user_challenge_event_rewarded"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="event_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint
                constraintName="u_user_challenge_event_rewarded"
                schemaName="playtime" tableName="user_challenge_event_rewarded"
                columnNames="user_id,event_id"/>
        <addForeignKeyConstraint
                constraintName="fk_user_challenge_event_rewarded_user_id_event_id"
                baseTableSchemaName="playtime" baseTableName="user_challenge_event_rewarded" baseColumnNames="user_id,event_id"
                referencedTableSchemaName="playtime" referencedTableName="user_challenge_event" referencedColumnNames="user_id,event_id"/>
    </changeSet>

    <changeSet id="challenge-event-reward-fake-meta" author="alex.potolitcyn" labels="PLAT-2629">
        <sql>
            INSERT INTO playtime.meta_user_earnings (id) VALUES (-2);
        </sql>
    </changeSet>

    <changeSet id="ofw-promotions-2025-1st-quarter" author="aleksey.romantsov"
               labels="PLAT-2622,PLAT-2623">
        <sqlFile path="sql/promotions/2025-1st-quarter-ofw-promotions.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="change-challenge-api" author="pavel.novikov" labels="PLAT-2643">
        <sqlFile path="sql/challenges/treasure-master-20-levels-challenge-update.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="implement-challenge-progress-update" author="pavel.novikov" labels="PLAT-2571">
        <sqlFile path="sql/challenges/treasure-master-20-levels-challenge-update.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-earn-playing-games-text" author="andrei.khripushin" labels="PLAT-2631">
        <jp:createExperiment key="androidEarnPlayingGamesText" minimumAppVersion="68">
            <jp:variation key="loyaltyNormal"/>
            <jp:variation key="loyaltyFire"/>
            <jp:variation key="loyaltyDollars"/>
            <jp:variation key="noText"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="blockbuster1st-variation" author="pavel.novikov" labels="PLAT-2492">
        <jp:addVariations experimentKey="androidGamesOrder">
            <jp:variation key="blockbuster1st"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-beginner-friendly-games-ex" author="andrei.khripushin" labels="PLAT-2653">
        <jp:createExperiment key="androidBeginnerFriendlyGames" minimumAppVersion="69">
            <jp:variation key="androidBeginnerFriendlyGamesBadge"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="translations-2025-02-04-ios-sync" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1209316974381642/f,
                       PLAT-2666">
        <sqlFile path="sql/translations_2025_02_04_ios_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="implement-challenge-progress-update1" author="pavel.novikov" labels="PLAT-2571">
        <sqlFile path="sql/challenges/treasure-master-20-levels-challenge-update1.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="wooden-puzzle-dynamic-ad-unit" author="dvir.sarig" labels="PLAT-2602_PLAT-2603">
        <sqlFile path="sql/wooden-puzzle-dynamic-ad-unit.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-demo-games-solitaireverse" author="andrei.khripushin" labels="PLAT-2599">
        <sqlFile path="sql/android-solitaireverse-demo-games-url.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-task-in-pre-game-ex" author="vyacheslav.yasinskiy" labels="PLAT-2651">
        <jp:createExperiment key="androidTasksInPreGame">
            <jp:variation key="tasksInPreGame"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="translations-2025-02-04-android-sync" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1209316989499214/f,
                       PLAT-2667">
        <sqlFile path="sql/translations_2025_02_04_android_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>


    <changeSet id="lucky-hours-notifications" author="vitalii.sirotkin" labels="PLAT-2624">
        <jp:createExperiment key="androidLuckyHour">
            <jp:variation key="luckyHourDaily"/>
            <jp:variation key="luckyHourWeekends"/>
            <jp:variation key="luckyHourWeekendsEmail"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-experiment-android-boosted-sign" author="vyacheslav.yasinskiy" labels="PLAT-2446">
        <jp:closeExperiment experimentKey="androidBoostedSign"/>
    </changeSet>

    <changeSet id="tutorial-full-screen-add-variation" author="vyacheslav.yasinskiy" labels="PLAT-2652">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="noShowTutorial"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="ios-new-games-descriptions" author="andrei.khripushin" labels="PLAT-2681">
        <sqlFile path="sql/ios-new-games-descriptions.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-play-at-least-badges" author="aleksey.romantsov" labels="PLAT-2647">
        <jp:createExperiment key="androidPlayAtLeastBadges">
            <jp:variation key="allGames30minEarn"/>
            <jp:variation key="eachGameUnique"/>
            <jp:variation key="allGames30minDaily"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="wooden-puzzle-dynamic-ad-unit-fix" author="andrei.khripushin" labels="PLAT-2602_PLAT-2603_fix">
        <sqlFile path="sql/wooden-puzzle-dynamic-ad-unit-fix.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="best-coins-badge-4new" author="alex.potolitcyn" labels="PLAT-2693">
        <jp:addVariations experimentKey="bestCoinsBadge">
            <jp:variation key="dailyV1"/>
            <jp:variation key="weeklyV1"/>
            <jp:variation key="dailyV2"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="offboard-experiment-gameScoringMakeTen" author="alex.potolitcyn" labels="PLAT-2684">
        <jp:closeExperiment experimentKey="gameScoringMakeTen"/>
    </changeSet>

    <changeSet id="offboard-experiment-gameScoringColorLogic" author="alex.potolitcyn" labels="PLAT-2683">
        <jp:closeExperiment experimentKey="gameScoringColorLogic"/>
    </changeSet>
    <changeSet id="androidCashout2xOffer-exp" author="andrei.khripushin" labels="PLAT-2658">
        <jp:createExperiment key="androidCashout2xOffer" minimumAppVersion="69">
            <jp:variation key="androidCashout2xOfferOn"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="challenge-event-reward-direct-revenue" author="alex.potolitcyn" labels="PLAT-2629">
        <addColumn schemaName="playtime" tableName="user_challenge_event">
            <column name="applovin_non_banner_revenue" type="DECIMAL(16, 12)"/>
        </addColumn>
    </changeSet>

    <changeSet id="update-promotion-manager" author="pavel.novikov" labels="PLAT-2469">
        <addColumn schemaName="playtime" tableName="promotion_event_cfg">
            <column name="enabled" type="boolean"/>
            <column name="experiment_key" type="varchar(100)"/>
            <column name="variation_key" type="varchar(100)"/>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_promotion_event_cfg_experiment_key"
                baseTableSchemaName="playtime"
                baseTableName="promotion_event_cfg"
                baseColumnNames="experiment_key"
                referencedTableSchemaName="playtime"
                referencedTableName="ab_experiments"
                referencedColumnNames="key"
        />
        <addForeignKeyConstraint
                constraintName="fk_promotion_event_cfg_variation_key"
                baseTableSchemaName="playtime"
                baseTableName="promotion_event_cfg"
                baseColumnNames="variation_key"
                referencedTableSchemaName="playtime"
                referencedTableName="ab_variations"
                referencedColumnNames="key"
        />
        <sql>
            update playtime.promotion_event_cfg set enabled = true;
        </sql>
        <addNotNullConstraint columnDataType="boolean" schemaName="playtime" tableName="promotion_event_cfg" columnName="enabled"/>
    </changeSet>

    <changeSet id="cashout-offers-tables" author="vitalii.sirotkin" labels="PLAT-2654">
        <createTable tableName="cashout_offer_sets">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="varchar(36)">
                <constraints referencedTableName="users" referencedColumnNames="id" referencedTableSchemaName="playtime"
                             foreignKeyName="fk_cashout_offer_sets__user_id"/>
            </column>
            <column name="closed_at" type="timestamp"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="cashout_offers">
            <column name="id" type="varchar(36)">
                <constraints primaryKey="true"/>
            </column>
            <column name="offer_set_id" type="INT">
                <constraints
                        foreignKeyName="fk_cashout_offers__offer_set_id"
                        referencedTableName="cashout_offer_sets"
                        referencedColumnNames="id"
                />
            </column>
            <column name="game_id" type="int"/>
            <column name="active_until_date" type="timestamp"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <jp:createExperiment key="specialCashoutOffers">
            <jp:variation key="3specialCashoutOffers"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-fullscreen-cashout-version-increase-69" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1209106111277033/1209290199587168/f,
                       PLAT-2718">
        <jp:updateMinAppVersion experimentKey="androidFullscreenCashout" newMinimumAppVersion="69"/>
    </changeSet>

    <changeSet id="update-lucky-hour-minapp" author="vitalii.sirotkin" labels="PLAT-2625">
        <jp:updateMinAppVersion experimentKey="androidLuckyHour" newMinimumAppVersion="69"/>
    </changeSet>

    <changeSet id="add-games-to-challenges" author="pavel.novikov" labels="PLAT-2696">
        <sqlFile path="sql/challenges/add-more4-games.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="challenges-solitaire-fix" author="pavel.novikov" labels="PLAT-2756">
        <sql>
            update playtime.challenge set game_id = 200044 where id = 'SOLITAIRE_1';
        </sql>
    </changeSet>

    <changeSet id="drop-left-icon" author="alex.potolitcyn" labels="remove unused playtime.additional_offers.left_icon">
        <dropColumn schemaName="playtime"
                    tableName="additional_offers"
                    columnName="left_icon"/>
    </changeSet>

    <changeSet id="track-new-data-for-monetization-events" author="cip.malos"
               labels="INT-233">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="generic_revenue_totals" columnName="day_0_revenue"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="generic_revenue_totals">
            <column name="day_0_revenue" type = "DECIMAL(18,12)" defaultValue="0.0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="renaming-coins-experiment" author="aleksey.romantsov" labels="PLAT-2685">
        <jp:createExperiment key="androidCoinsRenaming">
            <jp:variation key="tickets"/>
            <jp:variation key="bucks"/>
            <jp:variation key="chips"/>
            <jp:variation key="tokens"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="add-cashout-ready-notification-exp" author="vyacheslav.yasinskiy" labels="PLAT-2553">
        <jp:createExperiment key="androidCashoutReadyNotification">
            <jp:variation key="cashoutReadyClaim"/>
            <jp:variation key="cashoutReadyWaiting"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="adjoe-order-and-fyber-pictures" author="alex.potolitcyn" labels="PLAT-2741">
        <sqlFile path="sql/offerwalls/adjoe-order-and-fyber-pictures.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="games-coins-booster-new-variations" author="andrei.khripushin" labels="PLAT-2763">
        <sql>
            UPDATE playtime.ab_variations SET `key` = 'androidGamesCoinsBoosterOn2Min' WHERE `key` = 'androidGamesCoinsBoosterOn';
            UPDATE playtime.ab_variations SET `key` = 'iosGamesCoinsBoosterOn2Min' WHERE `key` = 'iosGamesCoinsBoosterOn';
        </sql>
        <jp:addVariations experimentKey="androidGamesCoinsBooster">
            <jp:variation key="androidGamesCoinsBoosterOn3Min"/>
            <jp:variation key="androidGamesCoinsBoosterOn4Min"/>
        </jp:addVariations>
        <jp:addVariations experimentKey="iosGamesCoinsBooster">
            <jp:variation key="iosGamesCoinsBoosterOn3Min"/>
            <jp:variation key="iosGamesCoinsBoosterOn4Min"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="drop-foreign-key" author="pavel.novikov" labels="PLAT-2469">
        <dropForeignKeyConstraint baseTableName="promotion_event_cfg" baseTableSchemaName="playtime" constraintName="fk_promotion_event_cfg_variation_key"/>
    </changeSet>

    <changeSet id="fix-blockbuster-translations" author="pavel.novikov" labels="PLAT-2578">
        <sql>
            UPDATE playtime.games
            SET description = '$_blockbuster_description',
                info_text_install_bottom = '$_blockbuster_text_install_bottom',
                info_text_install_top = '$_blockbuster_text_install_top'
            WHERE application_id = 'com.gimica.blockbuster'
              AND platform = 'ANDROID'
        </sql>
    </changeSet>

    <changeSet id="app-full-rollout-android-68" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C01MTD2KFM5/p1739783245103399">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
            (68, '2025-02-12');
        </sql>
    </changeSet>

    <changeSet id="em2-piggy-bank-exp" author="alex.potolitcyn" labels="PLAT-2715">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2Stash5pct"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="em2-piggy-bank-table" author="alex.potolitcyn" labels="PLAT-2715">
        <createTable schemaName="playtime" tableName="user_coins_stash">
            <column name="user_id" type="varchar(36)">
                <constraints referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             foreignKeyName="fk_user_coins_stash__user_id"
                             primaryKey="true"
                />
            </column>
            <column name="coins" type="DECIMAL(18,6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="pregame-more-variations" author="alex.potolitcyn" labels="PLAT-2753">
        <jp:addVariations experimentKey="androidInstallGamesReminders">
            <jp:variation key="intense24hPregameScreenRetention7days"/>
            <jp:variation key="evenMoreIntense24hPregameScreen"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="offboard-visualTrustAndroid" author="alex.potolitcyn" labels="PLAT-2457">
        <jp:closeExperiment experimentKey="visualTrustAndroid"/>
    </changeSet>

    <changeSet id="onboarding-progress-bar-exp" author="vitalii.sirotkin" labels="PLAT-2711">
        <jp:createExperiment key="androidOnboardingProgressBar">
            <jp:variation key="firstPayment"/>
            <jp:variation key="firstPaypalPayment"/>
            <jp:variation key="personalPayment"/>
        </jp:createExperiment>

        <createTable tableName="onboarding_progress_bar_state" schemaName="playtime">
            <column name="id" type="int" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="varchar(36)">
                <constraints referencedTableSchemaName="playtime" referencedTableName="users" referencedColumnNames="id"
                             foreignKeyName="fk_onboarding_progress_bar_state__user_id"/>
            </column>
            <column name="text" type="varchar(300)"/>
            <column name="type" type="varchar(100)"/>
            <column name="status" type="varchar(20)"/>
            <column name="order" type="int"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="playtime-to-games-user-coins-stash" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C02187C9U2W/p1740379828987919"
               context="!unit-test">
        <sql>
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`user_coins_stash` TO `games` @`%`;
        </sql>
    </changeSet>

    <changeSet id="em2-increase-game-coins-on-new-games" author="aleksey.romantsov" labels="PLAT-2716">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2IncreaseCoinsOnNewGame"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="coin-goal-variations-experiment" author="aleksey.romantsov" labels="PLAT-2714">
        <jp:createExperiment key="androidCoinGoalVariations">
            <jp:variation key="doubleFirst"/>
            <jp:variation key="halfForRest"/>
            <jp:variation key="quarterForRest"/>
            <jp:variation key="reduceIfFail"/>
            <jp:variation key="increaseIfMeet"/>
            <jp:variation key="followUpOnResult"/>
            <jp:variation key="allTogether"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="last-period-goal-reached" author="aleksey.romantsov" labels="PLAT-2714">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="cashout_periods" columnName="last_period_goal_reached"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="cashout_periods">
            <column name="last_period_goal_reached" type="boolean"/>
        </addColumn>
    </changeSet>

    <changeSet id="offboard-experiment-ios-show-ad-after-cashout" author="vyacheslav.yasinskiy" labels="PLAT-2292">
        <jp:closeExperiment experimentKey="iosShowAdAfterCashout"/>
    </changeSet>

    <changeSet id="androidHighlightGamesOnLowEarnings-exp" author="andrei.khripushin" labels="PLAT-2713">
        <jp:createExperiment key="androidHighlightGamesOnLowEarnings" minimumAppVersion="70">
            <jp:variation key="androidHighlightGamesOnLowEarningsOn"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="event-multiple-config" author="pavel.novikov" labels="PLAT-2761">
        <addColumn tableName="promotion_event_cfg" schemaName="playtime">
            <column name="priority_key" type="int"/>
        </addColumn>
        <addNotNullConstraint catalogName="playtime"
                              columnDataType="int"
                              columnName="priority_key"
                              defaultNullValue="0"
                              schemaName="playtime"
                              tableName="promotion_event_cfg"
                              validate="true"/>
    </changeSet>

    <changeSet id="offboard-experiment-games-dynamic-ad-unit-allocation" author="vyacheslav.yasinskiy" labels="PLAT-2294">
        <jp:closeExperiment experimentKey="gamesDynamicAdUnitAllocation"/>
    </changeSet>

    <changeSet id="offboard-experiment-ios-cashout-for-coins" author="vyacheslav.yasinskiy" labels="PLAT-2770">
        <jp:closeExperiment experimentKey="iosCashoutForCoins"/>
    </changeSet>

    <changeSet id="android-cashout-bipa-face-pre-screen-html" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1203013731295607/1209421795406336/f,
                       PLAT-2738">
        <sqlFile path="sql/translations_2025_02_24_android_bipa_html.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="add-android-animation-to-celebrate-earnings-exp" author="vyacheslav.yasinskiy" labels="PLAT-2712">
        <jp:createExperiment key="androidAnimationToCelebrateEarnings">
            <jp:variation key="animationToCelebrateEarnings"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-experiment-android-locked-games-explanation" author="vyacheslav.yasinskiy" labels="PLAT-2309">
        <jp:closeExperiment experimentKey="androidLockedGamesExplanation"/>
    </changeSet>

    <changeSet id="order-challenge-table" author="pavel.novikov" labels="PLAT-2775">
        <addColumn tableName="challenge" schemaName="playtime">
            <column name="order" type="int"/>
        </addColumn>
        <addNotNullConstraint catalogName="playtime"
                              columnDataType="int"
                              columnName="order"
                              defaultNullValue="0"
                              schemaName="playtime"
                              tableName="challenge"
                              validate="true"/>
    </changeSet>

    <changeSet id="womensday-2025" author="pavel.novikov" labels="PLAT-2794" context="!ios-us AND !ios-us-test">
        <sqlFile path="sql/promotions/PLAT-2794-womens-day.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="add-blockbuster-support" author="pavel.novikov" labels="PLAT-2844">
        <addColumn tableName="challenge" schemaName="playtime">
            <column name="goal" type="int"/>
        </addColumn>
    </changeSet>

    <changeSet id="app-full-rollout-android-69" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1740472461415489">
        <sql>
            INSERT INTO playtime.app_versions_full_rollout_dates (app_version, full_rollout_date) VALUES
                (69, '2025-02-24');
        </sql>
    </changeSet>

    <changeSet id="have-market-timezone-in-db" author="aleksey.romantsov" labels="PLAT-2795">
        <addColumn schemaName="playtime" tableName="settings">
            <column name="market_timezone" type="VARCHAR(50)"/>
            <column name="market" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-au" author="aleksey.romantsov" labels="PLAT-2795"
               context="au OR au-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="Australia/Sydney"/>
            <column name="market" value="au"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-us" author="aleksey.romantsov" labels="PLAT-2795"
               context="us OR us-test OR unit-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="America/Chicago"/>
            <column name="market" value="us"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-us-staging" author="aleksey.romantsov" labels="PLAT-2795"
               context="us-staging OR us-staging-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="America/Chicago"/>
            <column name="market" value="us-staging"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-ios-us" author="aleksey.romantsov" labels="PLAT-2795"
               context="ios-us OR ios-us-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="America/Chicago"/>
            <column name="market" value="ios-us"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-gb" author="aleksey.romantsov" labels="PLAT-2795"
               context="gb OR gb-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="Europe/Berlin"/>
            <column name="market" value="gb"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-asia" author="aleksey.romantsov" labels="PLAT-2795"
               context="asia OR asia-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="Asia/Tokyo"/>
            <column name="market" value="asia"/>
        </update>
    </changeSet>

    <changeSet id="have-market-timezone-in-db-latam" author="aleksey.romantsov" labels="PLAT-2795"
               context="latam OR latam-test">
        <update schemaName="playtime" tableName="settings">
            <column name="market_timezone" value="America/Mexico_City"/>
            <column name="market" value="latam"/>
        </update>
    </changeSet>

    <changeSet id="ofw-promotions-2025-womens-day" author="aleksey.romantsov"
               labels="PLAT-2795">
        <sqlFile path="sql/promotions/2025-womens-day-promotion.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="offboard-Short1PeriodTopUpSEONAndroid" author="alex.potolitcyn" labels="PLAT-2399">
        <jp:closeExperiment experimentKey="Short1PeriodTopUpSEONAndroid"/>
    </changeSet>

    <changeSet id="offboard-Short1PeriodTopUpSEONIos" author="alex.potolitcyn" labels="PLAT-2399">
        <jp:closeExperiment experimentKey="Short1PeriodTopUpSEONIos"/>
    </changeSet>
    <changeSet id="completed-at-for-challenges" author="alex.potolitcyn" labels="PLAT-2820">
        <addColumn schemaName="playtime" tableName="user_challenge">
            <column name="completed_at" type="TIMESTAMP"/>
        </addColumn>
    </changeSet>

    <changeSet id="cut-share-option-for-challenge" author="alex.potolitcyn" labels="PLAT-2820">
        <addColumn schemaName="playtime" tableName="challenge">
            <column name="apply_earnings_cut" type="bool"/>
        </addColumn>
        <sql>
            UPDATE playtime.challenge
            SET apply_earnings_cut = true;
        </sql>
        <addNotNullConstraint
                schemaName="playtime"
                tableName="challenge"
                columnName="apply_earnings_cut"
                validate="true"
                columnDataType="bool"
        />
    </changeSet>

    <changeSet id="exp-androidCashout2xOffer-min-app-version-70" author="alex.potolitcyn"
               labels="https://app.asana.com/0/0/1209494769324802/1209557714171248/f">
        <jp:updateMinAppVersion experimentKey="androidCashout2xOffer" newMinimumAppVersion="70"/>
    </changeSet>

    <changeSet id="exp-specialCashoutOffers-min-app-version-70" author="alex.potolitcyn"
               labels="https://app.asana.com/0/0/1209364093858065/1209557976217159/f">
        <jp:updateMinAppVersion experimentKey="specialCashoutOffers" newMinimumAppVersion="70"/>
    </changeSet>

    <changeSet id="add-android-show-paypal-logo-exp" author="vyacheslav.yasinskiy" labels="PLAT-2829">
        <jp:createExperiment key="androidShowPaypalLogo">
            <jp:variation key="androidShowPaypalLogo"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="offboard-goalMessages" author="alex.potolitcyn" labels="PLAT-2386">
        <jp:closeExperiment experimentKey="goalMessages"/>
    </changeSet>

    <changeSet id="balance-update-translations" author="alex.potolitcyn" labels="PLAT-2386">
        <sqlFile path="sql/games/blockbuster-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="balance-update-translations-fixed" author="alex.potolitcyn" labels="PLAT-2386">
        <sqlFile path="sql/notifications/balance-update-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-3specialCashoutOffersRandom-variation" author="vitalii.sirotkin" labels="PLAT-2816">
        <jp:addVariations experimentKey="specialCashoutOffers">
            <jp:variation key="3specialCashoutOffersRandom"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="non-clickable-email-translations-update" author="andrei.khripushin" labels="PLAT-2841">
        <sqlFile path="sql/non-clickable-email-translations-update.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="badge-test-variation" author="alex.potolitcyn" labels="https://justplayapps.slack.com/archives/GQ38AASRH/p1741270730565669">
        <jp:addVariations experimentKey="bestCoinsBadge">
            <jp:variation key="test"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="exp-androidAnimationToCelebrateEarnings-min-app-version-70" author="vyacheslav.yasinskiy"
               labels="PLAT-2712">
        <jp:updateMinAppVersion experimentKey="animationToCelebrateEarnings" newMinimumAppVersion="70"/>
    </changeSet>

    <changeSet id="android-demo-games-new-variations" author="vyacheslav.yasinskiy"
               labels="PLAT-2830">
        <jp:addVariations experimentKey="androidDemoGames">
            <jp:variation key="noRestartMoreEarnings" allocationRatio="0.0"/>
            <jp:variation key="noRestartUnlimitedEarning" allocationRatio="0.0"/>
            <jp:variation key="noRestartUnlockEarning" allocationRatio="0.0"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="cash-streak-exp" author="andrei.khripushin" labels="PLAT-2832">
        <jp:createExperiment key="androidCashStreak">
            <jp:variation key="androidCashStreakOn"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="em2-rv-for-personal" author="alex.potolitcyn" labels="PLAT-2835">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2RvForPersonal"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="solitaire-classic-experiment" author="vitalii.sirotkin" labels="PLAT-2852,PLAT-2855">
        <sqlFile path="sql/games/classic-solitaire.sql" relativeToChangelogFile="true"/>
        <jp:createExperiment key="androidNewSolitaire">
            <jp:variation key="androidSolitaireClassic"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="operator-name-unicode-support" author="vyacheslav.yasinskiy"
               labels="PLAT-2723">
        <sql>
            ALTER TABLE `playtime`.`user_multi_sim_data`
                MODIFY network_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                MODIFY sim_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

    <changeSet id="add-bb-instead-of-wbp" author="vitalii.sirotkin" labels="PLAT-2843">
        <jp:addVariations experimentKey="androidGamesOrder">
            <jp:variation key="blockInsteadWPB"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="operator-name-unicode-support-fix-nullable" author="vyacheslav.yasinskiy"
               labels="PLAT-2723">
        <sql>
            ALTER TABLE `playtime`.`user_multi_sim_data`
                MODIFY network_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                MODIFY sim_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="ofbboard-android-new-default-games-order" author="andrei.khripushin" labels="PLAT-2540">
        <jp:closeExperiment experimentKey="newDefaultOrderAndroid"/>
    </changeSet>

    <changeSet id="target-earnings-pct-stash" author="alex.potolitcyn" labels="PLAT-2860">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2StashEarn45"/>
            <jp:variation key="em2StashEarn55"/>
            <jp:variation key="em2StashEarn65"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-translations-sync-2025-03-10" author="eduard.trushnikov"
               labels="https://app.asana.***************************************************/1209627244672739,
                       PLAT-2864">
        <sqlFile path="sql/translations_2025_03_10_android_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="operator-name-unicode-support-user-data" author="vyacheslav.yasinskiy"
               labels="PLAT-2723">
        <sql>
            ALTER TABLE `playtime`.`user_additional_data`
                MODIFY network_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                MODIFY sim_operator_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

    <changeSet id="" author="pavel.novikov"
               labels="PLAT-2889">
        <sqlFile path="sql/challenges/PLAT-2889-challenge-claim-translation.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="user-cash-streak-table" author="aleksey.romantsov"
               labels="PLAT-2826">
        <createTable schemaName="playtime" tableName="user_cash_streak">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_cash_streak_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="streak_start" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="streak_counter" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="deadline" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="current_step_end" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_goal_reached_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="playtime" tableName="user_cash_streak" indexName="idx_user_cash_streak__deadline">
            <column name="deadline"/>
        </createIndex>
    </changeSet>

    <changeSet id="cash-streak-user-rewards-table" author="aleksey.romantsov"
               labels="PLAT-2826">
        <createTable schemaName="playtime" tableName="cash_streak_rewards">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_cash_streak_rewards"/>
            </column>
            <column name="achievement_day" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="reward_type" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="reward_value" type="DECIMAL(18,6)">
                <constraints nullable="false"/>
            </column>
            <column name="big_reward" type="bool" defaultValue="false">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="cash-streak-user-rewards-table-fill" author="aleksey.romanstov"
               labels="PLAT-2826">
        <sql>
            INSERT INTO `playtime`.`cash_streak_rewards` (`achievement_day`, `reward_type`, `reward_value`, `big_reward`)
            VALUES
            (2, 'COINS', 25000, false),
            (5, 'EARNING_POWER', 2, false),
            (7, 'COINS', 50000, false),
            (10, 'COINS', 60000, false),
            (14, 'EARNING_POWER', 3, false),
            (17, 'COINS', 70000, false),
            (21, 'COINS', 70000, false),
            (24, 'EARNING_POWER', 4, false),
            (30, 'COINS', 70000, false),
            (40, 'EARNING_POWER', 5, false),
            (50, 'EARNING_POWER', 7, false),
            (60, 'EARNING_POWER', 10, true),
            (80, 'EARNING_POWER', 12, false),
            (90, 'EARNING_POWER', 15, false),
            (100, 'EARNING_POWER', 20, true),
            (365, 'EARNING_POWER', 25, false),
            (1000, 'EARNING_POWER', 30, true);
        </sql>
    </changeSet>

    <changeSet id="user-cash-streak-user-rewards-table" author="aleksey.romantsov"
               labels="PLAT-2826">
        <createTable schemaName="playtime" tableName="user_cash_streak_rewards">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_user_cash_streak_rewards_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                />
            </column>
            <column name="reward_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_user_cash_streak_rewards_reward_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="cash_streak_rewards"
                             referencedColumnNames="id"
                />
            </column>
            <column name="streak_unique_key" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="reward_status" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_cash_streak_rewards"
                       columnNames="user_id,reward_id,streak_unique_key"
                       constraintName="pk_user_cash_streak_rewards"/>
    </changeSet>

    <changeSet id="update-city-name-length" author="Vitalii Sirotkin" labels="PLAT-2877">
        <sql>
            alter table playtime.ip_registry_data modify city_name varchar(300);
        </sql>
    </changeSet>

    <changeSet id="android-pin-master-screwpuzzle-ad-unit-ids" author="vyacheslav.yasinskiy"
               labels="PLAT-2850"
               runOnChange="true">
        <sql>
            INSERT INTO playtime.game_ad_unit_ids
                (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
            VALUES
                ('com.pinmaster.screwpuzzle', 'BANNER', '4a4d38d8df4304d0', 1, 'ANDROID'),
                ('com.pinmaster.screwpuzzle', 'REWARDED', 'c5fa25fc1c5af5b6', 1, 'ANDROID'),
                ('com.pinmaster.screwpuzzle', 'INTERSTITIAL', '3b7fbbabe4131c10', 1, 'ANDROID'),
                ('com.pinmaster.screwpuzzle', 'BANNER', '4123a4775a5c031d', 0, 'ANDROID'),
                ('com.pinmaster.screwpuzzle', 'REWARDED', 'ec54a086d2bb4492', 0, 'ANDROID'),
                ('com.pinmaster.screwpuzzle', 'INTERSTITIAL', 'a1d86e96febc919f', 0, 'ANDROID')
            ON DUPLICATE KEY UPDATE ad_unit_id = VALUES (ad_unit_id);
        </sql>
    </changeSet>

    <changeSet id="bitlabs_table" author="Vitalii Sirotkin" labels="PLAT-2899">
        <createTable tableName="bitlabs_reports" schemaName="playtime">
            <column name="transaction_id" type="varchar(100)">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="varchar(36)">
                <constraints foreignKeyName="fk_bitlabs_reports__user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"/>
            </column>
            <column name="reward_value" type="int"/>
            <column name="reward_paid" type="decimal(16,6)"/>
            <column name="state_type" type="varchar(100)"/>
            <column name="offer_type" type="varchar(20)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="androidCashStreak-min-app-version" author="andrei.khripushin" labels="PLAT-2833-1">
        <jp:updateMinAppVersion experimentKey="androidCashStreak" newMinimumAppVersion="71"/>
    </changeSet>

    <changeSet id="playtime-to-games-tables-grants-2025-03-18" author="aleksey.romantsov"
               labels="PLAT-2833"
               context="!unit-test">
        <sql>
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`cashout_periods` TO `games` @`%`;
        </sql>
    </changeSet>

    <changeSet id="android-add-game-pin-master" author="vyacheslav.yasinskiy"  labels="PLAT-2881">
        <sqlFile path="sql/games/android-pin-master.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="android-games-order-pin-master-variation" author="vyacheslav.yasinskiy"
               labels="PLAT-2905">
        <jp:addVariations experimentKey="androidGamesOrder">
            <jp:variation key="pinMaster1st" allocationRatio="0.0"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="track-max-cash-streak" author="aleksey.romantsov" labels="PLAT-2826">
        <addColumn schemaName="playtime" tableName="user_cash_streak">
            <column name="max_streak_counter" type="int"/>
        </addColumn>
    </changeSet>

    <changeSet id="android-games-celebration-experiment" author="vyacheslav.yasinskiy" labels="PLAT-2846">
        <jp:createExperiment key="androidGamesCelebration">
            <jp:variation key="androidGamesCelebrationFastCoins"/>
            <jp:variation key="androidGamesCelebrationSlowCoins"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="ios-games-celebration-experiment" author="vyacheslav.yasinskiy" labels="PLAT-2846">
        <jp:createExperiment key="iosGamesCelebration">
            <jp:variation key="iosGamesCelebrationFastCoins"/>
            <jp:variation key="iosGamesCelebrationSlowCoins"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="exp-tutorial-full-screen-blue-social" author="eduard.trushnikov"
               labels="PLAT-2827">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="fullScreenBlue"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="cash-streak-user-rewards-table-fill-2" author="aleksey.romanstov"
               labels="PLAT-2826">
        <sqlFile path="sql/cash-streak-rewards.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="tutorial-full-screen-mav-increasing" author="andrei.khripushin" labels="PLAT-2969">
        <jp:updateMinAppVersion experimentKey="tutorialFullScreen" newMinimumAppVersion="69"/>
    </changeSet>


    <changeSet id="exp-android-task-in-pregame-variation-one-task" author="andrei.khripushin" labels="PLAT-2946">
        <jp:addVariations experimentKey="androidTasksInPreGame">
            <jp:variation key="tasksInPreGameOneTask"/>
        </jp:addVariations>
        <jp:updateMinAppVersion experimentKey="androidTasksInPreGame" newMinimumAppVersion="72"/>
    </changeSet>


    <changeSet id="make-ten-renaming" author="andrei.khripushin" labels="PLAT-2931">
        <sqlFile path="sql/make-ten-renaming.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id="android-pin-master-do-not-show" author="vyacheslav.yasinskiy"
               labels="PLAT-2995">
        <sql>
            UPDATE playtime.games
            SET do_not_show = 1
            WHERE application_id = 'com.pinmaster.screwpuzzle'
              and platform = 'ANDROID';
        </sql>
    </changeSet>

    <changeSet id="exp-em2-variation-em2StashOneTimeFix" author="alex.potolitcyn" labels="PLAT-2878">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2StashOneTimeFix"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="em2StashOneTimeFix-table" author="alex.potolitcyn" labels="PLAT-2878">
        <createTable schemaName="playtime" tableName="user_stash_fix_given">
            <column name="user_id" type="varchar(36)">
                <constraints referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             foreignKeyName="fk_user_stash_fix_given__user_id"
                             primaryKeyName="pk_user_stash_fix_given"
                             primaryKey="true"
                />
            </column>
            <column name="fix_amount" type="DECIMAL(18,6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="em3-variation" author="alex.potolitcyn" labels="PLAT-2920">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em3"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="em3-coins-task-launch" author="alex.potolitcyn" labels="PLAT-2920">
        <createTable schemaName="playtime" tableName="user_revenue_coins_period">
            <column name="user_id" type="VARCHAR(36)">
                <constraints
                        primaryKeyName="pk_user_revenue_coins_period"
                        primaryKey="true"
                        foreignKeyName="fk_user_revenue_coins_period_user_id"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                />
            </column>
            <column name="check_at" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="promotion-translations" author="pavel.novikov" labels="PLAT-2875">
        <createTable tableName="promotion_event_translation_value" schemaName="playtime">
            <column type="varchar(100)" name="resource_name">
            </column>
            <column type="varchar(30)" name="language">
                <constraints
                        nullable="false"
                />
            </column>
            <column type="text" name="translation">
                <constraints
                        nullable="false"
                />
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime"
                       tableName="promotion_event_translation_value"
                       columnNames="resource_name,language"
                       constraintName="pk_promotion_event_translation_value"/>
        <dropForeignKeyConstraint baseTableName="promotion_event_translation" constraintName="fk_promotion_event_resource_translation"/>
    </changeSet>

    <changeSet id="games-text-install-top-fix" author="andrei.khripushin" labels="PLAT-2996">
        <sqlFile path="sql/games-text-install-top-fix.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="modify-user_challenge_event_table" author="pavel.novikov" labels="PLAT-3000">
        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="18_6">
                    SELECT concat(numeric_precision, '_' , numeric_scale)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'user_challenge_event'
                    AND COLUMN_NAME = 'earnings_amount'
                </sqlCheck>
            </not>
        </preConditions>
        <modifyDataType schemaName="playtime" tableName="user_challenge_event" columnName="earnings_amount" newDataType="DECIMAL(18,6)"/>
    </changeSet>

    <changeSet id="exp-android-blockbuster-em2StashOneTimeFix" author="vyacheslav.yasinskiy" labels="PLAT-2994">
        <jp:addVariations experimentKey="androidGamesOrder">
            <jp:variation key="blockbuster3rd"/>
        </jp:addVariations>
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE id = 200074;
        </sql>
    </changeSet>

    <changeSet id="add-variation-android-challenges" author="pavel.novikov" labels="PLAT-3030">
        <jp:addVariations experimentKey="androidChallenges">
            <jp:variation key="showChallengesD1"/>
            <jp:variation key="showChallengesD2"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-game-stories-exp" author="andrei.khripushin" labels="PLAT-2885">
        <jp:createExperiment key="androidGameStories" minimumAppVersion="72">
            <jp:variation key="androidGameStoriesPreGame"/>
            <jp:variation key="androidGameStoriesGoogleStore"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="more-games-text-install-top-fix" author="andrei.khripushin" labels="PLAT-2996">
        <sqlFile path="sql/more-games-text-install-top-fix.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="ecpm-groups-thresholds" author="aleksey.romantsov" labels="PLAT-2950">
        <createTable schemaName="playtime" tableName="first_video_reward_ecpm_groups_thresholds">
            <column name="for_day" type="DATE">
                <constraints
                        primaryKey="true"
                        nullable="false"
                />
            </column>
            <column name="group0" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group1" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group2" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group3" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group4" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group5" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group6" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group7" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group8" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group9" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group10" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group11" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group12" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group13" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group14" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group15" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group16" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group17" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group18" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="group19" type="DECIMAL(12,8)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="user-ecpm-group" author="aleksey.romantsov" labels="PLAT-2950">
        <createTable schemaName="playtime" tableName="user_ecpm_group">
            <column name="user_id" type="VARCHAR(36)">
                <constraints
                        primaryKeyName="pk_user_ecpm_group"
                        primaryKey="true"
                        foreignKeyName="fk_user_ecpm_group_user_id"
                        referencedTableSchemaName="playtime"
                        referencedTableName="users"
                        referencedColumnNames="id"
                />
            </column>
            <column name="ecpm_group" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="hide-ofw-for-high-ecpm-users-exp" author="aleksey.romantsov" labels="PLAT-2950">
        <jp:createExperiment key="androidHideOfw">
            <jp:variation key="90forever"/>
            <jp:variation key="90first3days"/>
            <jp:variation key="90first7days"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id = "ftue-events" author="pavel.novikov" labels="PLAT-2965">
        <addColumn tableName="promotion_event_cfg" schemaName="playtime" >
            <column name="event_type" type="varchar(50)"/>
        </addColumn>
        <addNotNullConstraint
                schemaName="playtime"
                tableName="promotion_event_cfg"
                columnName="event_type"
                validate="true"
                defaultNullValue="GLOBAL"
                columnDataType="varchar(50)"
        />
        <jp:createExperiment key="ftueEvents" minimumAppVersion="69">
            <jp:variation key="ftuePromosD0D5"/>
            <jp:variation key="ftuePromosD0D5ChallengesD1D3"/>
        </jp:createExperiment>
        <sqlFile path="sql/promotions/PLAT-2965-ftue-promotions.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="noPromosDuringChallenges-exp" author="Vitalii Sirotkin">
        <jp:createExperiment key="noPromosDuringChallenges">
            <jp:variation key="noPromosDuringChallenges"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id = "be-translation-sync" author="pavel.novikov" labels="PLAT-3060">
        <sqlFile path="sql/PLAT-3060-syncr-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="modify-promotion_event_transaltion_value-table" author="pavel.novikov" labels="LEP-19" runOnChange="true">
        <sql>
            alter table playtime.promotion_event_translation_value modify column `translation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>
    <changeSet id="modify-promotion_event_transaltion_value-table2" author="pavel.novikov" labels="LEP-19" runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_promotion__event_original_resource_translation"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableSchemaName="playtime" baseTableName="promotion_event_translation" constraintName="fk_promotion__event_original_resource_translation"/>
    </changeSet>

    <changeSet id="android-ballbounce-atlantis-rewarding-logic" author="andrei.khripushin" labels="PLAT-3027">
        <sqlFile path="sql/add-ballbounce-atlantis-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="payment-providers-translations" author="vyacheslav.yasinskiy" labels="PLAT-3015">
        <sqlFile path="sql/payment-providers-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>


    <changeSet id = "ftue-events-fix" author="pavel.novikov" labels="PLAT-2965">
        <sqlFile path="sql/promotions/PLAT-2965-ftue-fix.sql" relativeToChangelogFile="true"/>
        <addColumn tableName="challenge_event" schemaName="playtime" >
            <column name="event_type" type="varchar(50)"/>
        </addColumn>
        <addNotNullConstraint
                schemaName="playtime"
                tableName="challenge_event"
                columnName="event_type"
                validate="true"
                defaultNullValue="GLOBAL"
                columnDataType="varchar(50)"
        />
    </changeSet>

    <changeSet id="cfg_offerwall_rule-add-highlighted" author="Vitalii Sirotkin">
        <addColumn tableName="cfg_offerwall_rule" schemaName="playtime">
            <column name="is_highlighted" type="bool" defaultValue="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ofw-campaign-table-and-exp" author="Vitalii Sirotkin">
        <jp:createExperiment key="offerwallCampaigns">
            <jp:variation key="nextRefresh"/>
        </jp:createExperiment>
        <createTable tableName="user_offerwall_campaign" schemaName="playtime">
            <column name="user_id" type="varchar(36)">
                <constraints primaryKey="true"/>
            </column>
            <column name="campaign_name" type="varchar(255)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="challenge_claim_widget_challenges_complete_small-translations" author="pavel.novikov" labels="LEP-31">
        <sqlFile path="sql/LEP-31-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="em2-2x-quotas" author="alex.potolitcyn" labels="PLAT-3064">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2Stash5pctDQ"/>
            <jp:variation key="em2StashEarn45DQ"/>
            <jp:variation key="em2StashEarn55DQ"/>
            <jp:variation key="em2StashOneTimeFixDQ"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="blockbuster-mergeblast--challnges-translations" author="pavel.novikov" labels="LEP-31">
        <sqlFile path="sql/challenges/LEP-36-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-special-cashout-offers-earning-variation" author="Vitalii Sirotkin">
        <jp:addVariations experimentKey="specialCashoutOffers">
            <jp:variation key="3specialCashoutOffersRandom1earnings"/>
            <jp:variation key="3specialCashoutOffersRandom0.25earnings"/>
            <jp:variation key="3specialCashoutOffersRandom0.5earnings"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="em2-target-correction-variations" author="alex.potolitcyn" labels="PLAT-3011">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2RevShare50"/>
            <jp:variation key="em2RevShare50DQHT"/>
            <jp:variation key="em2RevShare50DQHTnoOW"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="android-translations-sync-2025-04-07" author="eduard.trushnikov"
               labels="https://app.asana.com/0/1155692811605665/1209906442495788,
                       PLAT-3046">
        <sqlFile path="sql/translations_2025_04_07_android_sync.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="em2-game-equalizing-blockbuster-2025-04-24" author="alex.potolitcyn"
               labels="https://justplayapps.slack.com/archives/C026ZG18DNC/p1744027270909279">
        <sql>
            INSERT INTO playtime.game_equalizer_coeffs (application_id, multiplier)
            SELECT 'com.gimica.blockbuster',
                   CASE market
                       WHEN 'asia' THEN 8
                       WHEN 'au' THEN 13
                       WHEN 'gb' THEN 8
                       WHEN 'latam' THEN 7
                       WHEN 'us' THEN 5
                       WHEN 'us-staging' THEN 8
                       ELSE 5 -- as in "us"
                       END
            FROM playtime.settings ON DUPLICATE KEY
            UPDATE multiplier =
            VALUES (multiplier);
        </sql>
    </changeSet>

    <changeSet id="default-game-order-2025-04-18" author="alex.potolitcyn" labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1744964800984869">
        <sqlFile path="sql/games/order-2025-04-18.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="default-show-pinmaster" author="alex.potolitcyn" labels="https://justplayapps.slack.com/archives/C021EC06HV2/p1744964800984869">
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE platform = 'ANDROID'
              AND application_id = 'com.pinmaster.screwpuzzle';
        </sql>
    </changeSet>

    <changeSet id="iterable_event_table" author="pavel.novikov" labels="LEP11">
        <createTable schemaName="playtime" tableName="iterable_event">
            <column name = "id" type="varchar(50)">
                <constraints nullable="false"
                             primaryKey="true"
                />
            </column>
            <column name = "em1_coins" type="int">
                <constraints nullable="false"/>
            </column>
            <column name = "em2_coins" type="int">
                <constraints nullable="false"/>
            </column>
            <column name = "enabled" type="bool">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="adjust-show-video-preview-experiment" author="vyacheslav.yasinskiy">
        <jp:addVariations experimentKey="showVideoPreview">
            <jp:variation key="showVideoPreviewTop2"/>
        </jp:addVariations>
        <sqlFile path="sql/games/update-video-preview-file-names.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="em3-goal-bar-milestones" author="alex.potolitcyn" labels="RS-29">
        <addColumn tableName="cashout_periods" schemaName="playtime">
            <column name="coin_goal_milestones" type="json">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

   <changeSet id = "FUE-85_ftuePromosD0D5ChallengesD2D4" author="pavel.novikov" labels="FUE-85">
       <jp:addVariations experimentKey="ftueEvents">
           <jp:variation key="ftuePromosD0D5ChallengesD2D4"/>
       </jp:addVariations>
       <sqlFile path="sql/promotions/FUE-85-ftue-promotions.sql" relativeToChangelogFile="true" splitStatements="true"/>
   </changeSet>

    <changeSet id = "FUE-52_ftue_config" author="pavel.novikov" labels="FUE-85">
        <sqlFile path="sql/promotions/FUE-52-ftue-promotions.sql" relativeToChangelogFile="true" splitStatements="true"/>
        <sqlFile path="sql/challenges/FUE-52-ftue-challenges.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>
    <changeSet id="offboard-tinder-of-games" author="andrei.khripushin" labels="PLAT-2284">
        <jp:closeExperiment experimentKey="androidPickPreferredGames"/>
    </changeSet>

    <changeSet id="bubble-chief-rewarding-logic" author="andrei.khripushin" labels="ER-46">
        <sqlFile path="sql/add-bubble-chief-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="atlantis-bounce-rewarding-logic" author="andrei.khripushin" labels="ER-47">
        <sqlFile path="sql/add-atlantis-bounce-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="solitaire-classic-forevergreen-rewarding-logic" author="andrei.khripushin" labels="ER-48">
        <sqlFile path="sql/add-solitaire-classic-forevergreen-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="threadform-rewarding-logic" author="andrei.khripushin" labels="ER-50">
        <sqlFile path="sql/add-threadform-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="hexa-drop-rewarding-logic" author="andrei.khripushin" labels="ER-51">
        <sqlFile path="sql/add-hexa-drop-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="dont-pop-rewarding-logic" author="andrei.khripushin" labels="ER-52">
        <sqlFile path="sql/add-dont-pop-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="one-line-pattern-puzzle-rewarding-logic" author="andrei.khripushin" labels="ER-53">
        <sqlFile path="sql/add-one-line-pattern-puzzle-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="aero-escape-rewarding-logic" author="andrei.khripushin" labels="ER-54">
        <sqlFile path="sql/add-aero-escape-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="slam-dunk-rewarding-logic" author="andrei.khripushin" labels="ER-55">
        <sqlFile path="sql/add-slam-dunk-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="android-full-screen-blue-rework" author="vyacheslav.yasinskiy"
               labels="FUE-68">
        <sqlFile path="sql/translations_android_full_screen_blue_rework.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="close-lucky-hours-experiment" author="Vitalii Sirotkin">
        <jp:closeExperiment experimentKey="androidLuckyHour"/>
    </changeSet>

    <changeSet id="big-ofw-gb-exp" author="Vitalii Sirotkin" labels="ER-61">
        <jp:createExperiment key="bigOfwGb">
            <jp:variation key="bigOfwGbAllCountries"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="highlightOfwLowEcpm_exp" author="Vitalii Sirotkin" labels="ER-29">
        <jp:createExperiment key="highlightOfwLowEcpm">
            <jp:variation key="highlightForP10"/>
            <jp:variation key="highlightForP20"/>
            <jp:variation key="highlightForP30"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-show-coins-conversion-ratio-exp" author="Vyacheslav.Yasinskiy" labels="FUE-98">
        <jp:createExperiment key="androidShowCoinsConversionRatio">
            <jp:variation key="androidShowCoinsConversionRatio"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="slice-puzzle-rewarding-logic" author="andrei.khripushin" labels="ER-26">
        <sqlFile path="sql/add-slice-puzzle-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="side-and-roll-rewarding-logic" author="andrei.khripushin" labels="ER-27">
        <sqlFile path="sql/add-side-and-roll-android-game.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>


    <changeSet id="boosted-mode-tables" author="Vitalii Sirotkin" labels="ER-35">
        <createTable tableName="boosted_mode_preset" schemaName="playtime">
            <column name="id" type="varchar(100)">
                <constraints primaryKey="true"/>
            </column>
            <column name="ui_config" type="text"/>
            <column name="coins_coefficient" type="double"/>
            <column name="earnings_coefficient" type="double"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="boosted_mode_session" schemaName="playtime">
            <column name="id" type="varchar(36)">
                <constraints primaryKey="true"/>
            </column>
            <column name="preset_id" type="varchar(100)"/>
            <column name="user_id" type="varchar(36)"/>
            <column name="start_time" type="timestamp"/>
            <column name="visible_end_time" type="timestamp"/>
            <column name="effective_end_time" type="timestamp"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="boosted_mode_session" schemaName="playtime" indexName="idx_boosted_mode_session_start_time">
            <column name="start_time"/>
        </createIndex>
        <createIndex tableName="boosted_mode_session" schemaName="playtime" indexName="idx_boosted_mode_session_eff_end_time">
            <column name="effective_end_time"/>
        </createIndex>
    </changeSet>

    <changeSet id="close-highlight-tile-match-pro-experiment" author="andrei khripushin" labels="PLAT-2918_PLAT-2919">
        <jp:closeExperiment experimentKey="androidHighlightTileMatchPro"/>
        <jp:closeExperiment experimentKey="iosHighlightTileMatchPro"/>
    </changeSet>

    <changeSet id="drop-user_preferred_games" author="andrei khripushin">
        <sql>DROP TABLE IF EXISTS playtime.user_preferred_games;</sql>
    </changeSet>

    <changeSet id="em2-2x-quotas-fix" author="alex.potolitcyn" labels="RS-70">
        <jp:addVariations experimentKey="earningsModelV2">
            <jp:variation key="em2Stash5pctDQFixed"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="additional-ofws-exp" author="Vitalii Sirotkin" labels="ER-62">
        <jp:createExperiment key="tapjoyAdditionalOfw">
            <jp:variation key="tapjoyAddAdjoe"/>
            <jp:variation key="tapjoyAddFyberAdjoe"/>
            <jp:variation key="tapjoyAddAdjoeFyber"/>
            <jp:variation key="tapjoyAddFyber"/>
        </jp:createExperiment>
        <jp:createExperiment key="fyberAdditionalOfw">
            <jp:variation key="fyberAddAdjoe"/>
            <jp:variation key="fyberAddTapjoyAdjoe"/>
            <jp:variation key="fyberAddAdjoeTapjoy"/>
            <jp:variation key="fyberAddTapjoy"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="show-video-preview-experiment-new-videos" author="vyacheslav.yasinskiy">
        <sqlFile path="sql/games/update-video-preview-file-names-add-videos.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="disable-mx-ofw" author="Vitalii Sirotkin" labels="ER-44">
        <jp:createExperiment key="disableOfwMx">
            <jp:variation key="mxNoOfwHighlight"/>
            <jp:variation key="mxNoOfw"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-full-screen-blue-translations-fix" author="vyacheslav.yasinskiy"
               labels="FUE-68">
        <sqlFile path="sql/translations_android_full_screen_blue_rework_fix.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="androidShowCoinsConversionRatio-min-app-version" author="vyacheslav.yasinskiy" labels="FUE-125">
        <jp:updateMinAppVersion experimentKey="androidShowCoinsConversionRatio" newMinimumAppVersion="74"/>
    </changeSet>

    <changeSet id="optional-challenges" author="pavel.novikov" labels="LEP-17">
        <addColumn tableName="challenge" schemaName="playtime" >
            <column name="challenge_type" type="varchar(50)"/>
        </addColumn>
        <addNotNullConstraint
                schemaName="playtime"
                tableName="challenge"
                columnName="challenge_type"
                validate="true"
                defaultNullValue="REGULAR"
                columnDataType="varchar(50)"
        />
        <jp:addVariations experimentKey="androidChallenges">
            <jp:variation key="showSpecialChallenges"/>
        </jp:addVariations>
        <createTable tableName="special_challenge_pot">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_special_challenge_pot"/>
            </column>
            <column name="pot_key" type="varchar(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="ui_config" type="json">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="progress_max" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="user_special_challenge_pot">
            <column name="id" type="varchar(36)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="user_id" type="varchar(36)">
                <constraints
                        nullable="false"
                        foreignKeyName="fk_user_special_challenge_pot__users"
                        referencedColumnNames="id"
                        referencedTableName="users"
                        referencedTableSchemaName="playtime"
                />
            </column>
            <column name="pot_id" type="INT">
                <constraints
                        nullable="false"
                        foreignKeyName="fk_user_special_challenge_pot__special_challenge_pot"
                        referencedTableName="special_challenge_pot"
                        referencedTableSchemaName="playtime"
                        referencedColumnNames="id"
                />
            </column>
            <column name="counter" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="progress" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="state" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="earnings_amount" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="applovin_non_banner_revenue" type="DECIMAL(16, 12)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint tableName="user_special_challenge_pot"
                             schemaName="playtime"
                             columnNames="user_id, pot_id, counter"/>
        <createTable tableName="user_special_challenge_rewarded" schemaName="playtime">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_user_special_challenge_rewarder"/>
            </column>
            <column name="user_pot_id" type="varchar(36)">
                <constraints
                        nullable="false"
                        foreignKeyName="fk_user_special_challenge_pot__id"
                        referencedColumnNames="id"
                        referencedTableName="user_special_challenge_pot"
                        referencedTableSchemaName="playtime"
                        unique="true"
                />
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="challenges-default-translation" author="pavel.novikov"
               labels="OP-20_OP-21">
        <sqlFile path="sql/challenges/OP-20_OP-21-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>
    <changeSet id="add_normalized_email_hash_columns" author="cip.malos"
               labels="FRAUD-7">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions
                ADD COLUMN normalized_email_hash VARCHAR(64);
            ALTER TABLE playtime.user_cashout_transactions
                ADD INDEX idx_user_cashout_transactions__normalized_email_hash (normalized_email_hash);

            ALTER TABLE playtime.blacklist_email
                ADD COLUMN normalized_email_hash VARCHAR(64);
            ALTER TABLE playtime.blacklist_email
                ADD INDEX idx_blacklist_email__normalized_email_hash (normalized_email_hash);

            ALTER TABLE playtime.email_verification_result
                ADD COLUMN normalized_email_hash VARCHAR(64);
            ALTER TABLE playtime.email_verification_result
                ADD INDEX idx_email_verification_result__normalized_email_hash (normalized_email_hash);
        </sql>
    </changeSet>

    <changeSet id="add_normalized_encrypted_email_columns" author="cip.malos"
               labels="FRAUD-7">
        <sql>
            ALTER TABLE playtime.user_cashout_transactions
                ADD COLUMN normalized_encrypted_email VARCHAR(750);
            ALTER TABLE playtime.user_cashout_transactions
                ADD INDEX idx_user_cashout_transactions__normalized_encrypted_email (normalized_encrypted_email);

            ALTER TABLE playtime.blacklist_email
                ADD COLUMN normalized_encrypted_email VARCHAR(750);
            ALTER TABLE playtime.blacklist_email
                ADD INDEX idx_blacklist_email__normalized_encrypted_email (normalized_encrypted_email);

            ALTER TABLE playtime.email_verification_result
                ADD COLUMN normalized_encrypted_email VARCHAR(750);
            ALTER TABLE playtime.email_verification_result
                ADD INDEX idx_email_verification_result__normalized_encrypted_email (normalized_encrypted_email);
        </sql>
    </changeSet>

    <changeSet id="balance-update-notifications-with-amount" author="Maksim Kalashnikov" labels="RS-105">
        <jp:createExperiment key="balanceUpdatedNotificationWithAmount">
            <jp:variation key="incrementalCoinAmountInTitle"/>
            <jp:variation key="totalCoinAmountInTitle"/>
            <jp:variation key="newPhrasing"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="new-lucky-hour" author="Vitalii Sirotkin" labels="ER-67">
        <jp:createExperiment key="androidLuckyHour2">
            <jp:variation key="luckyHourWeekends"/>
            <jp:variation key="luckyHourWeekendsBM1"/>
            <jp:variation key="luckyHourWeekendsBM1.25"/>
        </jp:createExperiment>
        <insert tableName="boosted_mode_preset" schemaName="playtime">
            <column name="id" value="lucky_hour_1"/>
            <column name="ui_config">
                <![CDATA[
                    {"mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Lucky Coins", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Lucky Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"}, "readyToCashout": {"titleTranslation": "Your Lucky Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <column name="coins_coefficient" valueNumeric="2.0"/>
            <column name="earnings_coefficient" valueNumeric="1.0"/>
        </insert>
        <insert tableName="boosted_mode_preset" schemaName="playtime">
            <column name="id" value="lucky_hour_1_25"/>
            <column name="ui_config">
                <![CDATA[
                    {"mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Lucky Coins", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Lucky Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"}, "readyToCashout": {"titleTranslation": "Your Lucky Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <column name="coins_coefficient" valueNumeric="2.0"/>
            <column name="earnings_coefficient" valueNumeric="1.25"/>
        </insert>
    </changeSet>

    <changeSet id="tutorial-full-screen-min-app-version" author="vyacheslav.yasinskiy" labels="FUE-68">
        <jp:updateMinAppVersion experimentKey="tutorialFullScreen" newMinimumAppVersion="74"/>
    </changeSet>

    <changeSet id="add-bubble-chef-experiment" author="mikhail.sokolov" labels="ER-100">
        <jp:createExperiment key="bubbleChefVSbubblePop">
            <jp:variation key="bubbleChefVSbubblePop"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="add-bubble-chief-translations" author="mikhail.sokolov" labels="ER-46">
        <sqlFile path="sql/add-bubble-chief-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="new-coin-goal-variations" author="Maksim Kalashnikov" labels="RS-106">
        <jp:addVariations experimentKey="androidCoinGoalVariations">
            <jp:variation key="firstX4"/>
            <jp:variation key="firstX6"/>
            <jp:variation key="firstX8"/>
            <jp:variation key="firstX10"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="boosted-mode-earnings" author="alex.potolitcyn" labels="RS-63">
        <addColumn schemaName="playtime" tableName="user_earnings">
            <column name="non_boosted_user_earning_usd" type="DECIMAL(10,6)"/>
            <column name="non_boosted_amount" type="DECIMAL(16,6)"/>
        </addColumn>
    </changeSet>

    <changeSet id="games-ab-testing" author="Vitalii Sirotkin" labels="ER-23">
        <createTable tableName="ab_games_experiments" schemaName="playtime">
            <column name="experiment_id" type="int">
                <constraints referencedTableSchemaName="playtime" referencedTableName="ab_experiments"
                             referencedColumnNames="id"
                             foreignKeyName="fk_ab_games_experiments__ab_experiments"
                             primaryKey="true"/>
            </column>
            <column name="game_id" type="int">
                <constraints referencedTableSchemaName="playtime" referencedTableName="games"
                             referencedColumnNames="id"
                             foreignKeyName="fk_ab_games_experiments__games"
                />
            </column>
            <column name="game_version" type="varchar(20)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-2-tm-exps" author="Vitalii Sirotkin" labels="ER-19,ER-20">
        <jp:createExperiment key="tmLevelBalance">
            <jp:variation key="controldefault"/>
            <jp:variation key="hardBoss"/>
            <jp:variation key="controlHardBoss"/>
        </jp:createExperiment>
        <jp:createExperiment key="tmRotationSpeed">
            <jp:variation key="controldefault"/>
            <jp:variation key="faster15percent"/>
            <jp:variation key="faster25percent"/>
            <jp:variation key="faster35percent"/>
        </jp:createExperiment>

        <sql>
            insert into playtime.ab_games_experiments (experiment_id, game_id, game_version)
            values (
                (select id from playtime.ab_experiments where `key` = 'tmLevelBalance'),
                (select id from playtime.games where `application_id` = 'com.gimica.treasuremaster' AND platform = 'ANDROID'),
                '1.3.0'
           );
        </sql>
        <sql>
            insert into playtime.ab_games_experiments (experiment_id, game_id, game_version)
            values (
                (select id from playtime.ab_experiments where `key` = 'tmRotationSpeed'),
                (select id from playtime.games where `application_id` = 'com.gimica.treasuremaster' AND platform = 'ANDROID'),
                '1.3.0'
           );
        </sql>
    </changeSet>

    <changeSet id="add-silent-coins-notification-experiment" author="eduard.trushnikov" labels="ER-6">
        <jp:createExperiment key="androidSilentCoinsNotification" minimumAppVersion="42">
            <jp:variation key="fullFirstSilentFollowing"/>
            <jp:variation key="silentAll"/>
            <jp:variation key="fullFirstVibrateFollowing"/>
            <jp:variation key="vibrateOnlyAll"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="add-solitaire-classic-forevergreen-translations" author="mikhail.sokolov" labels="ER-48">
        <sqlFile path="sql/add-solitaire-classic-forevergreen-translations.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="event-button-text-default-translation" author="pavel.novikov"
               labels="LEP-56">
        <sqlFile path="sql/promotions/LEP-56_promotion-default-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="solitaire-bubble-chef-challenge-translations" author="pavel.novikov"
               labels="LEP-59_69">
        <sqlFile path="sql/challenges/LEP-59-LEP-69-solitaire-bubble-chef-challenge-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="android-onboarding-progress-bar-infinite-list-variation" author="mikhail.sokolov"
               labels="FUE-82">
        <jp:addVariations experimentKey="androidOnboardingProgressBar">
            <jp:variation key="enhanced" allocationRatio="0.0"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="update-lh-bm-config" author="Vitalii Sirotkin" labels="ER-84">
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config"><![CDATA[
                {"mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Lucky Coins", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Lucky Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"}, "readyToCashout": {"titleTranslation": "Your Lucky Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]></column>
            <where>id = 'lucky_hour_1'</where>
        </update>
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config">
                <![CDATA[
                {"mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Lucky Coins", "earnPlayingGamesReplacementTranslation": "Boosted Lucky Games:", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Lucky Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"}, "readyToCashout": {"titleTranslation": "Your Lucky Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <where>id = 'lucky_hour_1_25'</where>
        </update>
    </changeSet>

    <changeSet id="fix-lucky-hour-1" author="Vitalii Sirotkin">
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config">
                <![CDATA[
                  {"mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Lucky Coins", "earnPlayingGamesReplacementTranslation": "Boosted Lucky Games:", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Lucky Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"}, "readyToCashout": {"titleTranslation": "Your Lucky Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <where>id = 'lucky_hour_1'</where>
        </update>
    </changeSet>

    <changeSet id="bonus-bank-table" author="maksim kalashnikov" labels="RS-123">
        <createTable schemaName="playtime" tableName="bonus_bank">
            <column name="user_id" type="VARCHAR(36)">
                <constraints
                        primaryKeyName="pk_user_bonus_bank"
                        primaryKey="true"
                />
            </column>
            <column name="bank_balance" type="DECIMAL(18, 6)">
                <constraints nullable="false"/>
            </column>
            <column name="last_claimed_balance" type="DECIMAL(18, 6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="user-valuables-storage-table" author="maksim kalashnikov" labels="RS-123">
        <createTable schemaName="playtime" tableName="user_valuables_storage">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(18, 6)">
                <constraints nullable="false"/>
            </column>
            <column name="valuable_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_valuables_storage" columnNames="user_id,valuable_type"
                       constraintName="pk_user_valuables_storage"/>
    </changeSet>

    <changeSet id="cashout-offer-2x-bm" author="Vitalii Sirotkin" labels="ER-75">
        <jp:addVariations experimentKey="androidCashout2xOffer">
            <jp:variation key="androidCashout2xOfferBM1"/>
            <jp:variation key="androidCashout2xOfferBM1_25"/>
        </jp:addVariations>
        <insert tableName="boosted_mode_preset" schemaName="playtime">
            <column name="id" value="cashout_2x_offer_1"/>
            <column name="ui_config">
                <![CDATA[
                {"mainScreenHintTranslation": "<big><font color='#F2C94C'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Special Coins", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Special Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Special Coins!"}, "readyToCashout": {"titleTranslation": "Your Special Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <column name="coins_coefficient" valueNumeric="2.0"/>
            <column name="earnings_coefficient" valueNumeric="1.0"/>
        </insert>
        <insert tableName="boosted_mode_preset" schemaName="playtime">
            <column name="id" value="cashout_2x_offer_1_25"/>
            <column name="ui_config">
                <![CDATA[
                {"mainScreenHintTranslation": "<big><font color='#F2C94C'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>", "topLeftCoinsReplacementTranslation": "Special Coins", "balanceUpdate": {"titleTranslation": "$_balance_update_notification_title", "descriptionTranslation": "You now have %s Special Coins!", "descriptionNoCoinsTranslation": "Keep collecting your Special Coins!"}, "readyToCashout": {"titleTranslation": "Your Special Earnings Are Ready to Cash Out!", "descriptionTranslation": "Open JustPlay to claim your rewards"}}
                ]]>
            </column>
            <column name="coins_coefficient" valueNumeric="2.0"/>
            <column name="earnings_coefficient" valueNumeric="1.25"/>
        </insert>
    </changeSet>

    <changeSet id="android-pre-game-screen-exp" author="vyacheslav.yasinskiy" labels="FUE-89">
        <jp:createExperiment key="androidPreGameScreen" minimumAppVersion="75">
            <jp:variation key="preGameScreenIntroBeforeInstall"/>
            <jp:variation key="preGameScreenIntroPermanent"/>
            <jp:variation key="preGameScreenIntroPermanent3hTimer"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="pre-game-screen-opened-add-column" author="vyacheslav.yasinskiy" labels="FUE-89">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="user_pre_game_screen_opened" columnName="updated_at"/>
            </not>
        </preConditions>
        <addColumn schemaName="playtime" tableName="user_pre_game_screen_opened">
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>


    <changeSet id="ios-boosted-games-exp" author="aleksey.romantsov" labels="NP-130">
        <jp:createExperiment key="iosBoostedGames">
            <jp:variation key="boostedGames"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="ios-boosted-games-exp-table" author="aleksey.romantsov" labels="NP-130">
        <createTable tableName="ios_boosted_game" schemaName="playtime">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_ios_boosted_game"/>
            </column>
            <column name="for_day" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="game_id" type="int">
                <constraints referencedTableSchemaName="playtime" referencedTableName="games"
                             referencedColumnNames="id"
                             foreignKeyName="fk_ios_boosted_game__games"
                />
            </column>
            <column name="image_filename" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ios-boosted-games-exp-table-grants" author="aleksey.romantsov" labels="NP-130"
               context="!unit-test">
        <sql>
            GRANT SELECT, UPDATE, INSERT ON `playtime`.`ios_boosted_game` TO `games` @`%`;
        </sql>
    </changeSet>

    <changeSet id="cashout-2x-offer-bm-fix" author="Vitalii Sirotkin" labels="ER-122">
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config">
                <![CDATA[
                {
                    "mainScreenHintTranslation": "<big><font color='#F2C94C'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>",
                    "topLeftCoinsReplacementTranslation": "Special Coins",
                    "earnPlayingGamesReplacementTranslation": "Boosted Games:",
                    "balanceUpdate": {
                        "titleTranslation": "$_balance_update_notification_title",
                        "descriptionTranslation": "You now have %s Special Coins!",
                        "descriptionNoCoinsTranslation": "Keep collecting your Special Coins!"
                    },
                    "readyToCashout": {
                        "titleTranslation": "Your Special Earnings Are Ready to Cash Out!",
                        "descriptionTranslation": "Open JustPlay to claim your rewards"
                    }
                }
                ]]>
            </column>
            <where>id in ('cashout_2x_offer_1', 'cashout_2x_offer_1_25')</where>
        </update>
    </changeSet>

    <changeSet id="add-solitaire-classic-forevergreen-video-preview" author="mikhail.sokolov" labels="ER-48">
        <sqlFile path="sql/add-solitaire-classic-forevergreen-video-preview.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-slice-puzzle-translations-and-enablement" author="mikhail.sokolov" labels="ER-110">
        <sqlFile path="sql/add-slice-puzzle-translations.sql" relativeToChangelogFile="true"/>
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE id = 200086;
        </sql>
    </changeSet>

    <changeSet id="add-slide-and-roll-translations-and-enablement" author="mikhail.sokolov" labels="ER-107">
        <sqlFile path="sql/add-slide-and-roll-translations.sql" relativeToChangelogFile="true"/>
        <sql>
            UPDATE playtime.games
            SET do_not_show = 0
            WHERE id = 200087;
        </sql>
    </changeSet>
    <changeSet id="fix-lucky-hour-push" author="Vitalii Sirotkin">
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config">
                <![CDATA[
                {
                    "mainScreenHintTranslation": "<big><font color=\"#F2C94C\">Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>",
                    "topLeftCoinsReplacementTranslation": "Lucky Coins",
                    "earnPlayingGamesReplacementTranslation": "Boosted Lucky Games:",
                    "balanceUpdate": {
                        "titleTranslation": "$_balance_update_notification_title",
                        "descriptionTranslation": "You now have %s Lucky Coins!",
                        "descriptionNoCoinsTranslation": "Keep collecting your Lucky Coins!"
                    },
                    "readyToCashout": {
                        "titleTranslation": "Your Lucky Earnings Are Ready!",
                        "descriptionTranslation": "Open JustPlay to claim your rewards"
                    }
                }
                ]]>
            </column>
            <where>id in ('lucky_hour_1', 'lucky_hour_1_25')</where>
        </update>
    </changeSet>

    <changeSet id="ios-web-app-jailbreak-check-results" author="aleksey.romantsov" labels="NP-163">
        <createTable tableName="ios_jailbreak_check_result" schemaName="playtime">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             foreignKeyName="fk_ios_jailbreak_check_result_user_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="users"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="result" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="slide-and-roll-and-more-challenge-translations" author="pavel.novikov"
               labels="LEP-70,LEP-71">
        <sqlFile path="sql/challenges/LEP-70-LEP-71-slide-and-roll-and more-challenge-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="decimal-bonus-coins" author="maksim kalashnikov" labels="RS-100">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="playtime" tableName="user_bonuses_balance" columnName="decimals_coins"/>
            </not>
        </preConditions>
        <sql>
            ALTER TABLE playtime.user_bonuses_balance
            ADD COLUMN decimals_coins DECIMAL(18,6) NOT NULL DEFAULT 0, ALGORITHM=INSTANT;
        </sql>
    </changeSet>

    <changeSet id="add-applovin-forevergreen-api-key-to-relevant-games" author="cip.malos" labels="CAS-1">
        <sql>
            UPDATE playtime.games
            SET applovin_api_key = 'forevergreen-api-key'
            WHERE application_id IN ('com.forevergreen.solitaire', 'com.forevergreen.atlantis')
        </sql>
    </changeSet>

    <changeSet id="fix-cashout-2x-bm-visuals" author="vitalii sirotkin">
        <update tableName="boosted_mode_preset" schemaName="playtime">
            <column name="ui_config">
                <![CDATA[
                    {
                        "mainScreenHintTranslation": "<big><font color='#F2C94C'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>",
                        "topLeftCoinsReplacementTranslation": "Special Coins",
                        "earnPlayingGamesReplacementTranslation": "Boosted Games:",
                        "balanceUpdate": {
                            "titleTranslation": "$_balance_update_notification_title",
                            "descriptionTranslation": "You now have %s Special Coins!",
                            "descriptionNoCoinsTranslation": "Keep collecting your Special Coins!"
                        },
                        "readyToCashout": {
                            "titleTranslation": "Your Special Earnings Are Ready!",
                            "descriptionTranslation": "Open JustPlay to claim your rewards"
                        }
                    }
                ]]>
            </column>
            <where>id in ('cashout_2x_offer_1', 'cashout_2x_offer_1_25')</where>
        </update>
    </changeSet>

    <changeSet id="add-custom-game-pages-v2-experiment" author="vyacheslav.yasinskiy" labels="FUE-135">
        <jp:createExperiment key="androidCustomGamePagesV2">
            <jp:variation key="androidCustomGamePagesV2"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="ftue-bm" author="pavel.novikov" labels="LEP-62">
        <jp:addVariations experimentKey="ftueEvents">
            <jp:variation key="bmD0_1p5"/>
            <jp:variation key="bmD0_2p0"/>
            <jp:variation key="ftuePromosD2D4ChallengesD2D4"/>
        </jp:addVariations>
        <sqlFile path="sql/promotions/LEP-62-new-ftue-variations-promotions.sql" relativeToChangelogFile="true" splitStatements="true"/>
        <sqlFile path="sql/challenges/LEP-62-new-ftue-variations-challenges.sql" relativeToChangelogFile="true" splitStatements="true"/>
        <sqlFile path="sql/promotions/LEP-62-ftue-bm0.sql" relativeToChangelogFile="true" splitStatements="true"/>
        <jp:updateMinAppVersion experimentKey="ftueEvents" newMinimumAppVersion="74"/>
    </changeSet>

    <changeSet id="ftue-bm_fix" author="pavel.novikov" labels="LEP-62_fix">
        <sqlFile path="sql/promotions/LEP-62-ftue-bm0-fix.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="add-bubbleChef1st-variation" author="Vitalii Sirotkin">
        <jp:addVariations experimentKey="androidGamesOrder">
            <jp:variation key="bubbleChef1st"/>
        </jp:addVariations>
    </changeSet>

    <changeSet id="add-bubble-chef-video" author="Vitalii Sirotkin">
        <update tableName="games" schemaName="playtime">
            <column name="video_preview_filename" value="games/video_preview/BubbleChef.mp4"/>
            <where>application_id = 'com.bubblechef.bubbleshooter' AND platform = 'ANDROID'</where>
        </update>
    </changeSet>

    <changeSet id="close-androidGamesCoinsBooster" author="Vitalii Sirotkin">
        <jp:closeExperiment experimentKey="androidGamesCoinsBooster"/>
    </changeSet>

    <changeSet id="ios-web-app-adjust-deep-links" author="aleksey.romantsov" labels="NP-89">
        <createTable tableName="games_webapp" schemaName="playtime">
            <column name="game_id" type="INT">
                <constraints nullable="false"
                             foreignKeyName="fk_games_webapp_game_id"
                             referencedTableSchemaName="playtime"
                             referencedTableName="games"
                             referencedColumnNames="id"
                             primaryKey="true"
                />
            </column>
            <column name="adjust_link" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ios-boosted-game-add-descriptions" author="aleksey.romantsov"
               labels="NP-130">
        <addColumn tableName="ios_boosted_game" schemaName="playtime">
            <column name="description" type="varchar(200)"/>
        </addColumn>
    </changeSet>

    <changeSet id="add-tutorial-full-screen-variations" author="mikhail.sokolov" labels="FUE-140">
        <jp:addVariations experimentKey="tutorialFullScreen">
            <jp:variation key="longWithPlaytime"/>
            <jp:variation key="longWithout"/>
            <jp:variation key="shortWithPlaytime"/>
            <jp:variation key="shortWithout"/>
        </jp:addVariations>
        <jp:updateMinAppVersion experimentKey="tutorialFullScreen" newMinimumAppVersion="76"/>
    </changeSet>

    <changeSet id="add-special-offer-after-challenge-claim" author="mikhail.sokolov" labels="ER-40">
        <jp:createExperiment key="specialOfferAfterChallengeClaim" minimumAppVersion="75">
            <jp:variation key="bmWithoutEarningBoostEverySingleDay"/>
            <jp:variation key="bmWith20PercentEarningBoostEverySingleDay"/>
            <jp:variation key="bmWithoutEarningBoostEveryOtherDay"/>
            <jp:variation key="bmWith20PercentEarningBoostEveryOtherDay"/>
        </jp:createExperiment>
        <sqlFile path="sql/add-challenge-claim-bm-presets.sql" relativeToChangelogFile="true" splitStatements="true"/>
        <createTable tableName="user_challenge_special_offer_claims" schemaName="playtime">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_user_challenge_special_offer_claims"/>
            </column>
            <column name="user_id" type="varchar(36)">
                <constraints
                        nullable="false"
                        foreignKeyName="fk_user_challenge_special_offer_claims__users"
                        referencedColumnNames="id"
                        referencedTableName="users"
                        referencedTableSchemaName="playtime"
                />
            </column>
            <column name="claimed_at" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="offboard iosDeviceAttestationAtUserCreation" author="andrei.khripushin" labels="PLAT-1469">
        <jp:closeExperiment experimentKey="iosDeviceAttestationAtUserCreation"/>
    </changeSet>

    <changeSet id="offboard iosPreGameScreen" author="andrei.khripushin" labels="PLAT-2409">
        <jp:closeExperiment experimentKey="iosPreGameScreen"/>
    </changeSet>

    <changeSet id="offboard iosAllGamesButton" author="andrei.khripushin" labels="PLAT-2293">
        <jp:closeExperiment experimentKey="iosAllGamesButton"/>
    </changeSet>

    <changeSet id="offboard iosRewardsScreen" author="andrei.khripushin" labels="PLAT-1982">
        <jp:closeExperiment experimentKey="iosRewardsScreen"/>
    </changeSet>

    <changeSet id="offboard iosGamesOrder" author="andrei.khripushin" labels="PLAT-3082">
        <jp:closeExperiment experimentKey="iosGamesOrder"/>
        <sqlFile path="sql/reorder_ios_games.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="offboard iosClaimButton" author="andrei.khripushin" labels="PLAT-3084">
        <jp:closeExperiment experimentKey="iosClaimButton"/>
    </changeSet>

    <changeSet id="offboard iosMainScreenTimer" author="andrei.khripushin" labels="PLAT-2291">
        <jp:closeExperiment experimentKey="iosMainScreenTimer"/>
    </changeSet>

    <changeSet id="offboard iosBeEmailValidation" author="andrei.khripushin" labels="PLAT-2256">
        <jp:closeExperiment experimentKey="iosBeEmailValidation"/>
    </changeSet>

    <changeSet id="offboard iosCashoutFormNameMode" author="andrei.khripushin" labels="PLAT-2239">
        <jp:closeExperiment experimentKey="iosCashoutFormNameMode"/>
    </changeSet>

    <changeSet id="offboard iosCoinsAmountSeparator" author="andrei.khripushin" labels="PLAT-2290">
        <jp:closeExperiment experimentKey="iosCoinsAmountSeparator"/>
    </changeSet>

    <changeSet id="offboard iosHighlightGamesOnLowEarnings" author="andrei.khripushin" labels="PLAT-3094">
        <jp:closeExperiment experimentKey="iosHighlightGamesOnLowEarnings"/>
    </changeSet>

    <changeSet id="offboard iosNewGamesAmount" author="andrei.khripushin" labels="PLAT-2413">
        <jp:closeExperiment experimentKey="iosNewGamesAmount"/>
    </changeSet>

    <changeSet id="offboard iosCashoutProgressBar" author="andrei.khripushin" labels="PLAT-2104">
        <jp:closeExperiment experimentKey="iosCashoutProgressBar"/>
    </changeSet>

    <changeSet id="offboard iosPaymentProviderSurvey" author="andrei.khripushin" labels="PLAT-3095">
        <jp:closeExperiment experimentKey="iosPaymentProviderSurvey"/>
    </changeSet>

    <changeSet id="offboard iosPlayFirstGamePush" author="andrei.khripushin" labels="PLAT-3104">
        <jp:closeExperiment experimentKey="iosPlayFirstGamePush"/>
    </changeSet>

    <changeSet id="offboard iosCashoutTutorial" author="andrei.khripushin" labels="PLAT-3105">
        <jp:closeExperiment experimentKey="iosCashoutTutorial"/>
    </changeSet>

    <changeSet id="offboard coinsDoNotResetIos" author="andrei.khripushin" labels="PLAT-2961">
        <jp:closeExperiment experimentKey="coinsDoNotResetIos"/>
    </changeSet>

    <changeSet id="offboard iosNewsV2" author="andrei.khripushin" labels="PLAT-2979">
        <jp:closeExperiment experimentKey="iosNewsV2"/>
    </changeSet>

    <changeSet id="offboard iosPaypalLimits" author="andrei.khripushin" labels="PLAT-2267">
        <jp:closeExperiment experimentKey="iosPaypalLimits"/>
    </changeSet>

    <changeSet id="atlantis-challenge-translation" author="pavel.novikov" labels="LEP-79">
        <sqlFile path="sql/challenges/LEP-79-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="android-new-offer-button-exp" author="vyacheslav.yasinskiy" labels="FUE-70">
        <jp:createExperiment key="androidNewOfferButton" minimumAppVersion="75">
            <jp:variation key="addCTAOldPayout"/>
            <jp:variation key="addCTANewPayout"/>
            <jp:variation key="fullSet"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="android-onboarding-progress-bar-increase-min-app-version" author="mikhail.sokolov" labels="FUE-82">
        <jp:updateMinAppVersion experimentKey="androidOnboardingProgressBar" newMinimumAppVersion="76"/>
    </changeSet>

    <changeSet id="offboard iosIncompleteCashoutRestoring" author="andrei.khripushin" labels="PLAT-3100">
        <jp:closeExperiment experimentKey="iosIncompleteCashoutRestoring"/>
    </changeSet>

    <changeSet id="user-game-rank-table" author="Vitalii Sirotkin"
               labels="ER-168">
        <createTable schemaName="playtime" tableName="user_game_rank">
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="game_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="rank" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="progress" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="achievement" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey schemaName="playtime" tableName="user_game_rank" columnNames="user_id,game_id"
                       constraintName="pk_user_game_rank"/>
    </changeSet>

    <changeSet id="add-user-game-ranking-exp" author="Vitalii Sirotkin">
        <jp:createExperiment key="androidUserGameRanking">
            <jp:variation key="showGamesRanking"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="backfill-boosted-mode-session-user-id-index" author="mikhail.sokolov" labels="ER-166">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="playtime" tableName="boosted_mode_session" indexName="idx_boosted_mode_session_user_id"/>
            </not>
        </preConditions>
        <createIndex schemaName="playtime" tableName="boosted_mode_session" indexName="idx_boosted_mode_session_user_id">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="rank-translations" author="andrei.khripushin" labels="ER-169">
        <sqlFile path="sql/rank-translation.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>
    
    <changeSet id="bonus-bank-cash-bar" author="alex.potolitcyn" labels="RS-23">
        <jp:createExperiment key="bonusBank" minimumAppVersion="75">
            <jp:variation key="bonusCashBar"/>
        </jp:createExperiment>
    </changeSet>

    <changeSet id="add-spider-solitaire-translations-and-images" author="mikhail.sokolov" labels="ER-124">
        <sqlFile path="sql/add-spider-solitaire-translations-and-images.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="add-atlantis-bounce-translations-and-images" author="mikhail.sokolov" labels="ER-135">
        <sqlFile path="sql/add-atlantis-bounce-translations-and-images.sql" relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="special-challenges-translations" author="pavel.novikov" labels="LEP-86">
        <sqlFile path="sql/challenges/LEP-86-special-challenges-translations.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

    <changeSet id="gps-verification-exp-revenue-variation" author="cip.malos"
               labels="FRAUD-15">
        <jp:addVariations experimentKey="androidGpsVerification">
            <jp:variation key="revenueAbove30" allocationRatio="0.0"/>
        </jp:addVariations>
    </changeSet>


    <changeSet id="add-bonusId-column" author="pavel.novikov" labels="LEP-90">
        <sql>
            ALTER TABLE playtime.challenge_event ADD COLUMN bonus_id varchar(50), ALGORITHM=INSTANT;
        </sql>
    </changeSet>

    <changeSet id="rise-min-app-version-androidChallenges" author="pavel.novikov" labels="LEP-87">
        <jp:updateMinAppVersion experimentKey="androidChallenges" newMinimumAppVersion="75"/>
    </changeSet>

    <changeSet id="adjust-new-game-names" author="mikhail.sokolov" labels="ER-48">
        <sql>
            UPDATE playtime.games SET name = 'Spider Solitaire' WHERE application_id = 'com.gimica.spidersolitaire' AND platform = 'ANDROID';
            UPDATE playtime.games SET name = 'Solitaire Classic' WHERE application_id = 'com.forevergreen.solitaire' AND platform = 'ANDROID';
        </sql>
    </changeSet>

    <changeSet id="adjust-challenge-claim-bm-preset-ui-configs" author="mikhail.sokolov" labels="ER-185">
        <sqlFile path="sql/adjust-challenge-claim-bm-preset-ui-configs.sql" relativeToChangelogFile="true" splitStatements="true"/>
    </changeSet>

</databaseChangeLog>
