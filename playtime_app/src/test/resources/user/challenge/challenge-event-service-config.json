{"startTime": 1732233600, "endTime": 1732319940, "challengesUpdateTime": 1732319940, "timestamp": 1732276801, "challengeEventId": "15", "challengesUpdateText": "[fr translated] New challenges on New Year!", "claimWidget": {"bannerColor": "#FF0000", "bannerEndColor": "#00FF00", "textColor": "#FFFFFF", "headerText": "[fr translated] Golden Tickets Collected: ", "progressBarSubtext": "[fr translated] Collect All Golden Tickets to Claim your Cash Bonus", "eventRewardClaimed": false, "claimButtonText": "[fr translated] Claim up to 25$ CASH Reward!", "aheadButtonText": "[fr translated] Bonus Ahead!"}, "tutorialSteps": ["welcome", "cashBonus"], "challenges": [{"challengeId": "13", "title": "[fr translated] Get 100 correct questions in Trivia Madness", "icon": "https://example.com/images/merge_blast_icon.jpg", "progressCurrent": 5, "progressMax": 100, "rewardClaimed": false, "gameInfo": {"id": "20056", "applicationId": "applicationId", "activityName": "activityName", "title": "[fr translated] name", "iconUrl": "https://example.com/iconFilename", "imageUrl": "https://example.com/imageFilename", "isEnabled": true, "installImageUrl": "https://example.com/installImageFilename", "infoTextInstallTop": "[fr translated] infoTextInstallTop", "infoTextInstallBottom": "[fr translated] infoTextInstallBottom", "showInstallImage": true, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast&listing=justplay"}, "challengeType": "REGULAR"}, {"challengeId": "17", "title": "[fr translated] Get 100 correct questions in Trivia Madness", "icon": "https://example.com/images/merge_blast_icon.jpg", "progressCurrent": 5, "progressMax": 100, "rewardClaimed": false, "gameInfo": {"id": "20056", "applicationId": "applicationId", "activityName": "activityName", "title": "[fr translated] name", "iconUrl": "https://example.com/iconFilename", "imageUrl": "https://example.com/imageFilename", "isEnabled": true, "installImageUrl": "https://example.com/installImageFilename", "infoTextInstallTop": "[fr translated] infoTextInstallTop", "infoTextInstallBottom": "[fr translated] infoTextInstallBottom", "showInstallImage": true, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast&listing=justplay"}, "challengeType": "REGULAR"}], "specialChallengePotState": "IN_PROGRESS", "specialChallengePotProgressCurrent": 53, "specialChallengePotProgressMax": 100, "specialChallengeWidgets": {"mainScreen": [{"challengeType": "REGULAR", "imageUrl": "https://example.com/regular-image.png", "title": "[fr translated] Regular challenges"}, {"challengeType": "SPECIAL", "imageUrl": "https://example.com/special-image.png", "title": "[fr translated] Special challenges"}], "specialChallengeScreen": {"treasureImageUrl": "https://example.com/treasure-url.png"}, "claimWidget": {"title": "[fr translated] You hit the JackPot!", "description": "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!", "image": "https://example.com/claim-image.png"}}, "tutorialWidget": {"backgroundColor": "#FFFFFF", "highlightStartColor": "#FFFFFA", "highlightEndColor": "#FFFFFB", "tcUrl": "https://example.com", "tutorialId": "tutorialId"}, "bonusTracker": {"bonusId": "bonusId", "progressMax": 10, "progressCurrent": 5, "completeText": "[fr translated] You won super bonus!"}}