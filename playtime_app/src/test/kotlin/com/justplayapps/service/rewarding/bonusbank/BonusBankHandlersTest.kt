package com.justplayapps.service.rewarding.bonusbank

import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.bonusbank.bonusBankClaim
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.util.mock
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestone
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestoneStatus
import com.moregames.playtime.utils.Json.defaultJsonConverter
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

@ExtendWith(MockExtension::class)
class BonusBankHandlersTest(
  private val commandsClient: CommandsClient,
  private val abTestingFacade: AbTestingFacade,
  private val bonusBankStorage: BonusBankStorage,
  private val emExperimentBaseService: EmExperimentBaseService,
) {

  private val underTest = BonusBankHandlers(
    commandsClient = commandsClient,
    jsonSerializer = com.moregames.playtime.util.defaultJsonConverter,
    abTestingFacade = abTestingFacade,
    bonusBankStorage = bonusBankStorage,
    emExperimentBaseService = emExperimentBaseService,
  )

  companion object {
    private const val USER_ID = "user-id"
    private const val COMMAND_ID = "command-id"
  }

  @BeforeEach
  fun before() {
    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, Variations.BONUS_CASH_BAR)
    emExperimentBaseService.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
  }

  @Test
  fun `SHOULD return empty response ON handleBonusBankClaim WHEN user is not a participant`() {
    val claim = BonusCashBarClaimResult(
      claims = listOf()
    )
    val claimJson = defaultJsonConverter.encodeToString(claim)
    val expectedData = mapOf("data" to claimJson)

    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, DEFAULT)

    val bonusBankClaim = bonusBankClaim {
      this.userId = USER_ID
      this.commandId = COMMAND_ID
    }

    runBlocking { underTest.handleBonusBankClaim(bonusBankClaim) }

    verifyBlocking(commandsClient) { completeCommandSync(COMMAND_ID, expectedData) }
  }

  @Test
  fun `SHOULD process bonus bank claim ON handleBonusBankClaim`() {
    bonusBankStorage.mock(
      { getUserBank(USER_ID) },
      BonusBank(
        userId = USER_ID,
        bankBalance = BigDecimal("550.11"),
        lastClaimedBalance = BigDecimal("199.99")
      )
    )

    val claim = BonusCashBarClaimResult(
      claims = listOf(
        BonusCashBarClaim(
          barClaimed = true,
          barReward = BigDecimal("200000"),
          claimedMilestones = listOf(
            BonusCashBarMilestone(
              valueToReach = BigDecimal("500000"),
              reward = BigDecimal("15000"),
              status = BonusCashBarMilestoneStatus.CLAIMED
            ),
            BonusCashBarMilestone(
              valueToReach = BigDecimal("750000"),
              reward = BigDecimal("20000"),
              status = BonusCashBarMilestoneStatus.CLAIMED
            )
          )
        )
      )
    )
    val claimJson = defaultJsonConverter.encodeToString(claim)
    val expectedData = mapOf("data" to claimJson)

    val bonusBankClaim = bonusBankClaim {
      this.userId = USER_ID
      this.commandId = COMMAND_ID
      this.platform = Common.AppPlatformProto.ANDROID
    }

    runBlocking { underTest.handleBonusBankClaim(bonusBankClaim) }

    verifyBlocking(commandsClient) { completeCommandSync(COMMAND_ID, expectedData) }
  }

  @Test
  fun `SHOULD gracefully claim ON handleBonusBankClaim WHEN exception expected`() {
    val claim = BonusCashBarClaimResult(
      claims = listOf()
    )
    val claimJson = defaultJsonConverter.encodeToString(claim)
    val expectedData = mapOf("data" to claimJson)

    bonusBankStorage.mock(
      { getUserBank(USER_ID) },
      BonusBank(
        userId = USER_ID,
        bankBalance = BigDecimal.ZERO,
        lastClaimedBalance = BigDecimal.ZERO
      )
    )

    val bonusBankClaim = bonusBankClaim {
      this.userId = USER_ID
      this.commandId = COMMAND_ID
      this.platform = Common.AppPlatformProto.ANDROID
    }

    runBlocking { underTest.handleBonusBankClaim(bonusBankClaim) }

    verifyBlocking(commandsClient) { completeCommandSync(COMMAND_ID, expectedData) }
  }

}