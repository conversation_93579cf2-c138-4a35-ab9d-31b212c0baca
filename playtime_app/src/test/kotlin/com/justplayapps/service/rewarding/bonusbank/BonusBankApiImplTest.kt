package com.justplayapps.service.rewarding.bonusbank

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.google.protobuf.empty
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.bonusbank.BonusBankApiGrpcKt
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.BonusCashBarStateKt.bonusCashBarStateMilestone
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.bonusCashBarState
import com.justplayapps.playtime.rewarding.bonusbank.RewardingBonusBank.GetBonusCashBarStateResponse.BonusCashBarState.BonusCashBarStateMilestone
import com.justplayapps.playtime.rewarding.bonusbank.getBonusCashBarStateRequest
import com.justplayapps.playtime.rewarding.bonusbank.getBonusCashBarStateResponse
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.*
import com.moregames.base.grpc.testGrpcClient
import com.moregames.base.grpc.withGrpcService
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal

@ExtendWith(MockExtension::class)
class BonusBankApiImplTest(
  private val abTestingFacade: AbTestingFacade,
  private val bonusBankStorage: BonusBankStorage,
  private val emExperimentBaseService: EmExperimentBaseService,
) {

  private val client = testGrpcClient(BonusBankApiGrpcKt::BonusBankApiCoroutineStub)

  private val underTest = BonusBankApiImpl(
    abTestingFacade = abTestingFacade,
    bonusBankStorage = bonusBankStorage,
    emExperimentBaseService = emExperimentBaseService,
  )

  companion object {
    private const val USER_ID = "user-id"
  }

  @BeforeEach
  fun before() {
    emExperimentBaseService.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
  }

  @Test
  fun `SHOULD return empty response ON getBonusCashBarState WHEN NOT user is a participant`() = withGrpcService(underTest) {
    val expected = getBonusCashBarStateResponse {
      this.userId = USER_ID
      this.disabled = empty { }
    }

    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, DEFAULT)

    val request = getBonusCashBarStateRequest { this.userId = USER_ID }

    val actual = client.getBonusCashBarState(request)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return empty response ON getBonusCashBarState WHEN user is on another bonus_bank variation`() = withGrpcService(underTest) {
    val expected = getBonusCashBarStateResponse {
      this.userId = USER_ID
      this.disabled = empty { }
    }

    val testVariation = TestVariation("carouselV1")
    assertThat(testVariation.toBonusBankVariation()).isEqualTo(BonusBankVariation.CarouselV1) // make sure I refer to BonusBankVariation

    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, testVariation)

    val request = getBonusCashBarStateRequest { this.userId = USER_ID }

    val actual = client.getBonusCashBarState(request)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return response ON getBonusCashBarState`() = withGrpcService(underTest) {
    val expected = getBonusCashBarStateResponse {
      this.userId = USER_ID
      this.enabled = bonusCashBarState {
        this.valueToReach = BigDecimal("1000000").toProto()
        this.currentValue = BigDecimal("740220").toProto()
        this.reward = BigDecimal("200000").toProto()
        this.readyToClaim = false
        this.milestone.add(
          bonusCashBarStateMilestone {
            this.valueToReach = BigDecimal("250000").toProto()
            this.reward = BigDecimal("15000").toProto()
            this.status = BonusCashBarStateMilestone.BonusCashBarStateMilestoneStatus.CLAIMED
          }
        )
        this.milestone.add(
          bonusCashBarStateMilestone {
            this.valueToReach = BigDecimal("500000").toProto()
            this.reward = BigDecimal("15000").toProto()
            this.status = BonusCashBarStateMilestone.BonusCashBarStateMilestoneStatus.READY_TO_CLAIM
          }
        )
        this.milestone.add(
          bonusCashBarStateMilestone {
            this.valueToReach = BigDecimal("750000").toProto()
            this.reward = BigDecimal("20000").toProto()
            this.status = BonusCashBarStateMilestone.BonusCashBarStateMilestoneStatus.IN_PROGRESS
          }
        )
      }
    }

    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, Variations.BONUS_CASH_BAR)
    bonusBankStorage.mock(
      { getUserBank(USER_ID) },
      BonusBank(
        userId = USER_ID,
        bankBalance = BigDecimal("370.11"),
        lastClaimedBalance = BigDecimal("199.99")
      )
    )

    val request = getBonusCashBarStateRequest {
      this.userId = USER_ID
      this.platform = Common.AppPlatformProto.ANDROID
    }

    val actual = client.getBonusCashBarState(request)

    assertThat(actual).isEqualTo(expected)
  }

  class TestVariation(val variationKey: String) : TypedVariation {
    override fun getKey(): String = variationKey

    companion object : TypedVariationBase<TestVariation> by base()
  }
}