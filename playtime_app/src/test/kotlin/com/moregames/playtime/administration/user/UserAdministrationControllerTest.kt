package com.moregames.playtime.administration.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.abtesting.dto.Variation
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataDto
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.customnotification.*
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.dto.ExperimentVariationsDto
import com.moregames.playtime.administration.dto.FacetecFacesRequestDto
import com.moregames.playtime.administration.dto.FacetecFacesResponseDto
import com.moregames.playtime.administration.dto.RedisCachedResponseDto
import com.moregames.playtime.administration.user.UserAdministrationController.*
import com.moregames.playtime.notifications.MassPushNotificationService
import com.moregames.playtime.user.UserCheckManager
import com.moregames.playtime.user.UserPersistenceService.UserIdAndTrackingId
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.ChallengeEventConfigService
import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventsAdminApiDto
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.promotion.event.manager.PromotionEventService
import com.moregames.playtime.user.promotion.event.manager.PromotionTranslationService
import com.moregames.playtime.user.promotion.event.manager.api.admin.*
import com.moregames.playtime.user.promotion.event.manager.api.client.BadgeConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.CountDownInfoSectionApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.OfferModifierConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.TopConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentParametersApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarDynamicValueConfigurationApiDto
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventType
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.util.authenticate
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.utils.AuthProviderMock
import com.papsign.ktor.openapigen.OpenAPIGen
import com.papsign.ktor.openapigen.route.apiRouting
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.serialization.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import net.javacrumbs.jsonunit.JsonAssert
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.whenever
import redis.clients.jedis.Jedis
import redis.clients.jedis.JedisPool
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@ExperimentalSerializationApi
@ExtendWith(MockExtension::class)
class UserAdministrationControllerTest(
  private val fraudScoreService: FraudScoreService,
  private val userService: UserService,
  private val userCheckManager: UserCheckManager,
  private val jedisPool: JedisPool,
  private val facetecFacesService: FacetecFacesService,
  private val trackingService: TrackingService,
  private val timeService: TimeService,
  private val abTestingService: AbTestingService,
  private val massPushNotificationService: MassPushNotificationService,
  private val promotionEventService: PromotionEventService,
  private val challengeEventConfigService: ChallengeEventConfigService,
  private val challengeService: ChallengeService,
  private val promotionTranslationService: PromotionTranslationService,
) {

  private fun controller(): Application.() -> Unit = {
    install(OpenAPIGen)
    install(ContentNegotiation) {
      json(
        json = jsonConverter
      )
    }
    apiRouting {
      authenticate(AuthProviderMock()) {
        UserAdministrationController(
          fraudScoreService = fraudScoreService,
          userService = userService,
          userCheckManager = userCheckManager,
          jedisPool = jedisPool,
          facetecFacesService = facetecFacesService,
          trackingService = trackingService,
          timeService = timeService,
          abTestingService = abTestingService,
          massPushNotificationService = massPushNotificationService,
          promotionEventService = promotionEventService,
          challengeEventConfigService = challengeEventConfigService,
          promotionTranslationService = promotionTranslationService,
          challengeService = challengeService,
        ).startRouting(this)
      }
    }
  }

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
  }

  @Test
  fun `SHOULD ban user ON ban command call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/users/$USER_ID/ban")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fraudScoreService) { forceBanUser(USER_ID) }
  }

  @Test
  fun `SHOULD block user ON block command call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/users/$USER_ID/block")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fraudScoreService) { forceBlockUser(USER_ID) }
  }

  @Test
  fun `SHOULD whitelist GoogleAdId ON post GoogleAdIdsWhitelist call`() = withTestApplication(controller()) {
    val googleAdId = "google_ad_id1"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/whitelist/googleAdIds/$googleAdId")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) { whitelistGoogleAdId(googleAdId) }
  }

  @Test
  fun `SHOULD remove from the whitelist GoogleAdId ON delete GoogleAdIdsWhitelist call`() = withTestApplication(controller()) {
    val googleAdId = "google_ad_id1"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Delete, uri = "/whitelist/googleAdIds/$googleAdId")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(trackingService) { removeGoogleAdIdsFromWhitelist(setOf(googleAdId)) }
  }

  @Test
  fun `SHOULD whitelist trackingData ON post whitelist-trackingData-add call`() = withTestApplication(controller()) {
    val body = TrackingDataDto(UUID.randomUUID().toString(), IDFV, IOS)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/whitelist/add-tracking-data") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(body))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) { whitelistTrackingData(TrackingData.fromDto(body)) }
  }

  @Test
  fun `SHOULD remove from the whitelist trackingData ON whitelist-trackingData-remove call`() = withTestApplication(controller()) {
    val body = TrackingDataDto(UUID.randomUUID().toString(), IDFV, IOS)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/whitelist/remove-tracking-data") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(body))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(trackingService) { removeTrackingDataSetFromWhitelist(setOf(TrackingData.fromDto(body))) }
  }

  @Test
  fun `SHOULD trigger side effects processing ON generate-side-effects-for-earnings-meta`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/generate-side-effects-for-earnings-meta?metaId=5")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userCheckManager) { onUserEarningsCalculated(5) }
  }

  @Test
  fun `SHOULD queue notifications sending ON send-generic-notifications`() = withTestApplication(controller()) {
    val userIds = listOf("userId1", "userId2")
    val textMessage = "The issue with payments was fixed! Come back and cash out your earnings!"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/send-generic-notifications") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(MassNotificationRequestDto(userIds = userIds, notificationText = textMessage)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(massPushNotificationService) { sendGenericPushNotifications(userIds.toSet(), null, textMessage) }
  }

  @Test
  fun `SHOULD schedule notifications sending ON schedule-generic-notifications`() = withTestApplication(controller()) {
    val userIds = listOf("userId1", "userId2")
    val textMessage = "The issue with payments was fixed! Come back and cash out your earnings!"
    val title = "some title here"
    val sendAfter = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(10)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/schedule-generic-notifications") {
      addHeader("Content-Type", "application/json")
      setBody(
        jsonConverter.encodeToString(
          MassScheduledNotificationRequestDto(
            userIds = userIds,
            notificationText = textMessage,
            notificationTitle = title,
            sendAfter = sendAfter
          )
        )
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(massPushNotificationService) { scheduleGenericPushNotifications(userIds, title, textMessage, sendAfter) }
  }

  @Test
  fun `SHOULD queue notifications sending ON send-generic-notifications WHEN title defined`() = withTestApplication(controller()) {
    val userIds = listOf("userId1", "userId2")
    val textMessage = "The issue with payments was fixed! Come back and cash out your earnings!"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/send-generic-notifications") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(MassNotificationRequestDto(userIds = userIds, notificationText = textMessage, notificationTitle = "title")))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(massPushNotificationService) { sendGenericPushNotifications(userIds.toSet(), "title", textMessage) }
  }

  @Test
  fun `SHOULD throw wrong parameters exception ON send-custom-notifications WHEN both userId-s and idfv-s defined`() = withTestApplication(controller()) {
    assertThrows<IllegalArgumentException> {
      handleRequest(method = HttpMethod.Post, uri = "/send-custom-notifications") {
        addHeader("Content-Type", "application/json")
        setBody(
          jsonConverter.encodeToString(
            MassCustomNotificationRequestDto(
              userIds = listOf("userId1", "userId2"),
              trackingIds = listOf("idfv_1", "idfv_2"),
              notification = customNotification,
            )
          )
        )
      }
    }.let {
      assertThat(it.message).isEqualTo("Define userIdList or gaidOrIdfvList, not both")
    }
  }

  @Test
  fun `SHOULD send custom notifications ON send-custom-notifications WHEN userId-s defined`() = withTestApplication(controller()) {
    userService.mock({ fetchUserIds(emptyList()) }, emptyList())

    handleRequest(method = HttpMethod.Post, uri = "/send-custom-notifications") {
      addHeader("Content-Type", "application/json")
      setBody(
        jsonConverter.encodeToString(
          MassCustomNotificationRequestDto(
            userIds = listOf("userId1", "userId2"),
            trackingIds = emptyList(),
            notification = customNotification,
          )
        )
      )
    }
      .response
      .let { response ->
        assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
        val responseBody = defaultJsonConverter.decodeFromString<MassCustomNotificationResponseDto>(response.content!!)
        assertThat(responseBody).isEqualTo(MassCustomNotificationResponseDto(notificationsSentCount = 2, errors = emptyList()))
        verifyBlocking(massPushNotificationService) {
          sendCustomPushNotifications(mapOf("userId1" to customNotification, "userId2" to customNotification))
        }
      }
  }


  @Test
  fun `SHOULD send custom notifications ON send-custom-notifications WHEN idfv-s defined`() = withTestApplication(controller()) {

    userService.mock(
      { fetchUserIds(listOf("gaid1", "idfv2", "gaid_noUser")) },
      listOf(
        UserIdAndTrackingId("gaid1", "userId1"),
        UserIdAndTrackingId("idfv2", "userId2"),
      )
    )

    handleRequest(method = HttpMethod.Post, uri = "/send-custom-notifications") {
      addHeader("Content-Type", "application/json")
      setBody(
        jsonConverter.encodeToString(
          MassCustomNotificationRequestDto(
            userIds = emptyList(),
            trackingIds = listOf("gaid1", "idfv2", "gaid_noUser"),
            notification = customNotification,
          )
        )
      )
    }
      .response
      .let { response ->
        val responseBody = defaultJsonConverter.decodeFromString<MassCustomNotificationResponseDto>(response.content!!)

        assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
        assertThat(responseBody).isEqualTo(
          MassCustomNotificationResponseDto(
            notificationsSentCount = 2,
            errors = listOf(
              "Failed to find user by 'gaid_noUser'"
            )
          )
        )
        verifyBlocking(massPushNotificationService) {
          sendCustomPushNotifications(mapOf("userId1" to customNotification, "userId2" to customNotification))
        }
      }
  }

  @Test
  fun `SHOULD flush cache ON flush-redis`() = withTestApplication(controller()) {
    val jedis: Jedis = mock()
    whenever(jedisPool.resource).thenReturn(jedis)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/redis/flush-redis")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(jedis) { flushAll() }
  }

  @Test
  fun `SHOULD get users faces ON users-faces`() = withTestApplication(controller()) {
    val facesRequest = FacetecFacesRequestDto(
      userIds = listOf("user1", "user2"),
      googleAdIds = listOf("gaid1", "gaid2")
    )
    val facesResponse = FacetecFacesResponseDto(
      users = listOf(
        FacetecFacesResponseDto.UserRecord(
          metabaseUserDashboardLink = "https://metabase.link.user1",
          listOfFaces = listOf("https://face.one", "https://face.two")
        )
      )
    )
    facetecFacesService.mock({ getUsersInfo(facesRequest) }, facesResponse)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/users/faces") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(facesRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(facetecFacesService) { getUsersInfo(facesRequest) }
    assertThat(defaultJsonConverter.decodeFromString<FacetecFacesResponseDto>(testCall.response.content!!))
      .isEqualTo(facesResponse)
  }

  @Test
  fun `SHOULD return cached value ON redis-cached`() = withTestApplication(controller()) {
    val expected = RedisCachedResponseDto(
      cachedValue = "someCachedValue"
    )

    val jedis: Jedis = mock()
    jedis.mock({ get("someKeyPrefix:someKey") }, "someCachedValue")
    whenever(jedisPool.resource).thenReturn(jedis)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/redis/redis-cached/someKeyPrefix:someKey")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<RedisCachedResponseDto>(testCall.response.content!!))
      .isEqualTo(expected)
    verifyBlocking(jedis) { get("someKeyPrefix:someKey") }
  }

  @Test
  fun `SHOULD return null ON redis-cached WHEN there in cache no value`() = withTestApplication(controller()) {
    val expected = RedisCachedResponseDto(
      cachedValue = null
    )

    val jedis: Jedis = mock()
    jedis.mock({ get("someKeyPrefix:someKey") }, null)
    whenever(jedisPool.resource).thenReturn(jedis)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/redis/redis-cached/someKeyPrefix:someKey")

    verifyBlocking(jedis) { get("someKeyPrefix:someKey") }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<RedisCachedResponseDto>(testCall.response.content!!))
      .isEqualTo(expected)
  }

  @Test
  fun `SHOULD reset value ON redis-cached`() = withTestApplication(controller()) {

    val jedis: Jedis = mock()
    whenever(jedisPool.resource).thenReturn(jedis)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Delete, uri = "/redis/redis-cached/someKeyPrefix:someKey")
    verifyBlocking(jedis) { del("someKeyPrefix:someKey") }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD start experiment ON ab_testing experiment start`() = withTestApplication(controller()) {
    val startAt = now.plusSeconds(1000)
    val experimentStartRequest = ExperimentStartDto(startAt = startAt, minimumAppVersion = 42)

    handleRequest(method = HttpMethod.Post, uri = "/ab-testing/expKey/start") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(experimentStartRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { startExperiment(experimentKey = "expKey", startAt = startAt, minimumAppVersion = 42) }
  }

  @Test
  fun `SHOULD start experiment now ON ab_testing experiment start WHEN startAt not defined`() = withTestApplication(controller()) {
    val experimentStartRequest = ExperimentStartDto(startAt = null, minimumAppVersion = 42)

    handleRequest(method = HttpMethod.Post, uri = "/ab-testing/expKey/start") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(experimentStartRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { startExperiment(experimentKey = "expKey", startAt = now, minimumAppVersion = 42) }
  }

  @Test
  fun `SHOULD finish experiment ON ab_testing experiment finish`() = withTestApplication(controller()) {
    val finishAt = now.plusSeconds(1000)
    val experimentFinishRequest = ExperimentFinishDto(finishAt = finishAt)

    handleRequest(method = HttpMethod.Post, uri = "/ab-testing/expKey/finish") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(experimentFinishRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { finishExperiment(experimentKey = "expKey", finishAt = finishAt) }
  }

  @Test
  fun `SHOULD finish experiment now ON ab_testing experiment finish WHEN finishAt not passed`() = withTestApplication(controller()) {
    val experimentFinishRequest = ExperimentFinishDto(finishAt = null)

    handleRequest(method = HttpMethod.Post, uri = "/ab-testing/expKey/finish") {
      addHeader("Content-Type", "application/json")
      setBody(jsonConverter.encodeToString(experimentFinishRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { finishExperiment(experimentKey = "expKey", finishAt = now) }
  }

  @Test
  fun `SHOULD return experiment variations split ON variations GET call`() = withTestApplication(controller()) {
    val experimentKey = "experimentKey"
    val experiment = Experiment(
      1, experimentKey, isActive = false,
      minimumAppVersion = 42,
      startedAt = Instant.now().minusSeconds(1000),
      finishedAt = Instant.now().minusSeconds(900),
      gameVersion = null,
    )
    val variation1 = Variation(1, "var1", BigDecimal("0.40"), experiment)
    val variation2 = Variation(2, "var2", BigDecimal("0.60"), experiment)

    abTestingService.mock({ loadVariationsForExperiment(experimentKey) }, listOf(variation1, variation2))

    handleRequest(method = HttpMethod.Get, uri = "/ab-testing/$experimentKey/variations")
      .response
      .let { response ->
        assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
        assertThat(com.moregames.playtime.utils.Json.defaultJsonConverter.decodeFromString<ExperimentVariationsDto>(response.content!!))
          .isEqualTo(
            ExperimentVariationsDto(
              startedAt = experiment.startedAt,
              finishedAt = experiment.finishedAt,
              minimumAppVersion = 42,
              mapOf(
                variation1.key to variation1.allocation,
                variation2.key to variation2.allocation
              )
            )
          )
      }
  }


  @Test
  fun `SHOULD update experiment variations split ON variations POST call`() = withTestApplication(controller()) {
    val experimentKey = "experimentKey"
    val request = ExperimentVariationsDto(
      startedAt = Instant.now(),
      finishedAt = Instant.now(),
      minimumAppVersion = 9999,
      mapOf(
        "var1" to BigDecimal("0.30"),
        "var2" to BigDecimal("0.70")
      )
    )

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/ab-testing/$experimentKey/variations") {
      addHeader("Content-Type", "application/json")
      setBody(com.moregames.playtime.utils.Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(abTestingService) {
      updateVariationsForExperiment(experimentKey, request.variations)
    }
  }

  @Test
  fun `SHOULD get promotionEventConfig`() = withTestApplication(controller()) {
    promotionEventService.mock(
      { getPromotionConfigList(dateFrom = now, dateTo = Instant.parse("2099-12-03T00:00:00.00Z"), enabled = true) }, PromotionEventsAdminApiDto(
        listOf(
          PromotionEventAdminApiDto(
            id = "testId",
            dateFrom = Instant.parse("2088-12-01T10:15:30.00Z"),
            dateTo = Instant.parse("2088-12-03T10:15:30.00Z"),
            useUserTimeZone = true,
            uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
              top = TopConfigApiDto(
                gradientTop = "#FF1E072B"
              ),
              infoBar = InfoBarApiDto(
                content = listOf(
                  InfoBarContentApiDto(
                    text = "PLAYERS_ONLINE"
                  ),
                  InfoBarContentApiDto(
                    text = "Text with {DYNAMIC_VALUE}$ amount in it!",
                    parameters = InfoBarContentParametersApiDto(
                      showDuringSec = 5,
                      sliding = true,
                      dynamicValueConfiguration = InfoBarDynamicValueConfigurationApiDto(
                        baseValue = 1_000_000,
                        updateEachSec = 5,
                        updateValueBy = -200,
                        randomnessPercentage = 5
                      )
                    )
                  )
                )
              ),
              expectedAppVersion = 75,
              countDownBanners = CountDownBannersAdminApiDto(
                startPosition = 1,
                step = 3,
                max = 2,
                title = "Banner title",
                backgroundImage = "background_image.jpg",
                infoImages = listOf("image1.jpg", "image2.jpg"),
                infoTitle = "Info title",
                infoSections = listOf(
                  CountDownInfoSectionApiDto(subTitle = "SubTitle", subText = "SubText")
                ),
                infoButtonClickAction = ButtonAction(
                  name = ButtonActionName.OPEN_FIRST_FOUND_OFFERWALL,
                  parameters = listOf("parameter1", "parameter2")
                ),
                infoButtonText = "Info button text",
                durationInSeconds = 3600
              )
            ),
            experimentKey = "experimentKey",
            variationKey = "variationKey",
            priorityKey = 0,
            translationResources = listOf(
              PromotionEventTranslationAdminApiDto(resourceName = "\$_resource2", originalResourceName = "\$_original_resource2"),
              PromotionEventTranslationAdminApiDto(resourceName = "\$_resource1", originalResourceName = "\$_original_resource1"),
            ),
            eventType = PromotionEventType.GLOBAL,
          )
        )
      )
    )
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/promotion-event-config?dateTo=2099-12-03&enabled=true")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    JsonAssert.assertJsonEquals(
      """
      {
        "events": [
          {
            "id": "testId",
            "dateFrom": "2088-12-01T10:15:30Z",
            "dateTo": "2088-12-03T10:15:30Z",
            "useUserTimeZone": true,
            "uiConfiguration": {
              "top": {
                "gradientTop": "#FF1E072B"
              },
              "infoBar": {
                "content": [
                  {
                    "text": "PLAYERS_ONLINE"
                  },
                  {
                    "text": "Text with {DYNAMIC_VALUE}$ amount in it!",
                    "parameters": {
                      "showDuringSec": 5,
                      "sliding": true,
                      "dynamicValueConfiguration": {
                        "baseValue": 1000000,
                        "updateEachSec": 5,
                        "updateValueBy": -200,
                        "randomnessPercentage": 5
                      }
                    }
                  }
                ]
              },
              "expectedAppVersion": 75,
              "countDownBanners": {
                "startPosition": 1,
                "step": 3,
                "max": 2,
                "title": "Banner title",
                "backgroundImage": "background_image.jpg",
                "infoImages": [
                  "image1.jpg", "image2.jpg"            
                ],
                "infoTitle": "Info title",
                "infoSections": [
                  {
                    "subTitle": "SubTitle",
                    "subText": "SubText"
                  }
                ],
                "infoButtonClickAction": {
                  "name": "OPEN_FIRST_FOUND_OFFERWALL",
                  "parameters": ["parameter1", "parameter2"]
                },
                "infoButtonText": "Info button text",
                "durationInSeconds": 3600
              }
            },
            "experimentKey": "experimentKey",
            "variationKey": "variationKey",
            "priorityKey": 0,
            "translationResources": [
              {
                "resourceName": "${'$'}_resource2",
                "originalResourceName": "${'$'}_original_resource2"
              },
              {
                "resourceName": "${'$'}_resource1",
                "originalResourceName": "${'$'}_original_resource1"
              }
            ],
            "eventType": "GLOBAL"
          }
        ]
      }
    """, testCall.response.content
    )
  }

  @Test
  fun `SHOULD save promotion event config`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/promotion-event-config") {
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(
        """
        {
        "id": "TestPromotion",
        "dateFrom": "2007-12-03T10:15:30.00Z",
        "dateTo": "2007-12-05T10:15:30.00Z",
        "useUserTimeZone": false,
        "uiConfiguration": {
          "top": {
            "gradientTop": "#FF1E072B",
            "gradientBottom": "#FF171524",
            "backgroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
            "foregroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
            "cashoutButtonColor": "#FFF19D00"
          },
          "offerModifier": [
            {
              "offerId": "200039",
              "badge": {
                  "text": "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>",
                  "color": "#FFFF00",
                  "displaySpecialBadge": true
              },
              "offerImage": "treasure_master_override.jpg"
            }
          ],
          "countDownBanners": {
            "startPosition": 1,
            "step": 3,
            "max": 2,
            "title": "Banner title",
            "backgroundImage": "background_image.jpg",
            "infoImages": [
              "image1.jpg", "image2.jpg"            
            ],
            "infoTitle": "Info title",
            "infoSections": [
              {
                "subTitle": "SubTitle",
                "subText": "SubText"
              }
            ],
            "infoButtonClickAction": {
              "name": "OPEN_CHALLENGES",
              "parameters": ["parameter1", "parameter2"]
            },
            "infoButtonText": "Info button text",
            "durationInSeconds": 3600
          },
          "infoBar": {
            "content": [
              {
                "text": "PLAYERS_ONLINE"
              },
              {
                "text": "Text with {DYNAMIC_VALUE}$ amount in it!",
                "parameters": {
                  "showDuringSec": 5,
                  "sliding": true,
                  "dynamicValueConfiguration": {
                    "baseValue": 1000000,
                    "updateEachSec": 5,
                    "updateValueBy": -200,
                    "randomnessPercentage": 5
                  }
                }
              }
            ]
          },
          "expectedAppVersion": 75
        },
        "experimentKey": "experiment",
        "variationKey": "variation", 
        "priorityKey": 17,
        "translationResources": [
          {
          "resourceName": "resource_name", 
          "originalResourceName": "originalResourceName"
          }        
        ], 
        "eventType": "GLOBAL"
      }
        """
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionEventService) {
      savePromotionConfig(
        PromotionEventAdminApiDto(
          id = "TestPromotion",
          dateFrom = Instant.parse("2007-12-03T10:15:30.00Z"),
          dateTo = Instant.parse("2007-12-05T10:15:30.00Z"),
          useUserTimeZone = false,
          uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
            top = TopConfigApiDto(
              gradientTop = "#FF1E072B",
              gradientBottom = "#FF171524",
              backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
              foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
              cashoutButtonColor = "#FFF19D00"
            ),
            offerModifier = listOf(
              OfferModifierConfigApiDto(
                offerId = "200039",
                offerImage = "treasure_master_override.jpg",
                badge = BadgeConfigApiDto(
                  color = "#FFFF00",
                  displaySpecialBadge = true,
                  text = "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"
                )
              )
            ),
            countDownBanners = CountDownBannersAdminApiDto(
              startPosition = 1,
              step = 3,
              max = 2,
              title = "Banner title",
              backgroundImage = "background_image.jpg",
              infoImages = listOf("image1.jpg", "image2.jpg"),
              infoTitle = "Info title",
              infoSections = listOf(
                CountDownInfoSectionApiDto(subTitle = "SubTitle", subText = "SubText")
              ),
              infoButtonClickAction = ButtonAction(
                name = ButtonActionName.OPEN_CHALLENGES,
                parameters = listOf("parameter1", "parameter2")
              ),
              infoButtonText = "Info button text",
              durationInSeconds = 3600
            ),
            infoBar = InfoBarApiDto(
              content = listOf(
                InfoBarContentApiDto(
                  text = "PLAYERS_ONLINE"
                ),
                InfoBarContentApiDto(
                  text = "Text with {DYNAMIC_VALUE}$ amount in it!",
                  parameters = InfoBarContentParametersApiDto(
                    showDuringSec = 5,
                    sliding = true,
                    dynamicValueConfiguration = InfoBarDynamicValueConfigurationApiDto(
                      baseValue = 1_000_000,
                      updateEachSec = 5,
                      updateValueBy = -200,
                      randomnessPercentage = 5
                    )
                  )
                )
              )
            ),
            expectedAppVersion = 75,
          ),
          experimentKey = "experiment",
          variationKey = "variation",
          priorityKey = 17,
          translationResources = listOf(
            PromotionEventTranslationAdminApiDto(
              resourceName = "resource_name",
              originalResourceName = "originalResourceName"
            )
          ),
          eventType = PromotionEventType.GLOBAL,
        )
      )
    }
  }

  @Test
  fun `SHOULD complete user challenge event config`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/challenge-event/complete-challenge?trackingId=trackingId") {
      userService.mock({ fetchUserId(trackingId = "trackingId") }, "userId")
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(
        """{"challengeId": "testChallenge"}"""
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(challengeService) {
      forceCompleteChallenge(userId = "userId", challengeId = "testChallenge")
    }
  }

  @Test
  fun `SHOULD get challengeEventsList`() = withTestApplication(controller()) {
    challengeEventConfigService.mock(
      { getChallengeEventList(dateFrom = now, dateTo = Instant.parse("2099-12-03T00:00:00.00Z"), enabled = true) }, ChallengeEventsAdminApiDto(
        listOf(
          ChallengeEventAdminApiDto(
            id = "testId",
            dateFrom = Instant.parse("2088-12-01T10:15:30.00Z"),
            dateTo = Instant.parse("2088-12-03T10:15:30.00Z"),
            challengesUpdateText = "some challenge update text",
            claimWidget = ClaimWidgetDto(
              bannerColor = "red",
              bannerEndColor = "green",
              textColor = "white",
              claimButtonText = "Ok",
              headerText = "Some header",
              progressBarSubtext = "Some sub progress",
              aheadButtonText = "abt",
            ),
            tutorialSteps = listOf("welcome", "cash_bonus"),
            challenges = listOf(
              ChallengeAdminApiDto(
                id = "id1",
                title = "title1",
                icon = "icon1",
                progressMax = 100,
                gameApplicationId = "gameApplicationId1",
                calculator = ChallengeProgressCalculatorType.LEVEL_ID,
                order = 1,
                goal = 100,
                applyEarningsCut = true,
                challengeType = ChallengeType.REGULAR,
              ),
              ChallengeAdminApiDto(
                id = "id2",
                title = "title2",
                icon = "icon2",
                progressMax = 100,
                gameApplicationId = "gameApplicationId2",
                calculator = ChallengeProgressCalculatorType.LEVEL_ID,
                order = 1,
                goal = 100,
                applyEarningsCut = true,
                challengeType = ChallengeType.REGULAR,
              )
            ),
            enabled = true,
            eventType = ChallengeEventType.GLOBAL,
            tutorialWidget = TutorialWidgetDto(
              backgroundColor = "#FFFFFF",
              highlightStartColor = "#FFFFFA",
              highlightEndColor = "#FFFFFB",
              tcUrl = "https://example.com",
              tutorialId = "tutorialId",
            ),
            bonusTracker = BonusTrackerDto(
              bonusId = "bonusId",
              progressMax = 10,
              completeText = "You won super bonus!"
            ),
          )
        )
      )
    )
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/challenge-event?dateTo=2099-12-03&enabled=true")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    JsonAssert.assertJsonEquals(
      """
      {
        "events": [
          {
            "id": "testId",
            "dateFrom": "2088-12-01T10:15:30Z",
            "dateTo": "2088-12-03T10:15:30Z",
            "challengesUpdateText": "some challenge update text",
            "claimWidget": {
              "bannerColor": "red",
              "bannerEndColor": "green",
              "textColor": "white",
              "claimButtonText": "Ok",
              "headerText": "Some header",
              "progressBarSubtext": "Some sub progress",
              "aheadButtonText": "abt"
            },
            "tutorialSteps": [
               "welcome",
               "cash_bonus"
            ],
            "challenges": [
              {
                "id": "id1",
                "title": "title1",
                "icon": "icon1",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId1",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true, 
                "challengeType": "REGULAR"           
              },
              {
                "id": "id2",
                "title": "title2",
                "icon": "icon2",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId2",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true,
                "challengeType": "REGULAR"            
              }
            ],
            "enabled": true,
            "eventType": "GLOBAL", 
            "tutorialWidget": {
              "backgroundColor":"#FFFFFF",
              "highlightStartColor":"#FFFFFA",
              "highlightEndColor":"#FFFFFB",
              "tcUrl":"https://example.com",
              "tutorialId":"tutorialId"
            },
            "bonusTracker": {
              "bonusId": "bonusId",
              "progressMax": 10,
              "completeText": "You won super bonus!"
            }
          }
        ]
      }
    """, testCall.response.content
    )
  }

  @Test
  fun `SHOULD update challenge event`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Put, uri = "/challenge-event") {
      val body = javaClass.getResource("/administration/user/save-challenges.json")!!.readText()
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(body)
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(challengeEventConfigService) { updateChallengeEvent(challengeEventStub) }
    verifyBlocking(challengeEventConfigService) { updateChallengeEvent(challengeEventStub.copy(id = "testId2")) }
  }

  @Test
  fun `SHOULD create challenge event`() = withTestApplication(controller()) {
    val body = javaClass.getResource("/administration/user/save-challenges.json")!!.readText()
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/challenge-event") {
      addHeader("Content-Type", "application/json")
      setBody(body)
    }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(challengeEventConfigService) { createChallengeEvent(challengeEventStub) }
    verifyBlocking(challengeEventConfigService) { createChallengeEvent(challengeEventStub.copy(id = "testId2")) }
  }

  @Test
  fun `SHOULD get promotionEventTranslationValues ON GET promotion-event-translations`() = withTestApplication(controller()) {
    val resourceName = "hi_resource"

    promotionTranslationService.mock(
      { getPromotionTranslationValuesByResourceName(resourceName) }, PromotionEventTranslationValuesApiDto(
        listOf(
          PromotionEventTranslationValueApiDto(resourceName, "en", "Hello!"),
          PromotionEventTranslationValueApiDto(resourceName, "fr", "Bonjour!"),
        )
      )
    )

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/promotion-event-translations?resourceName=$resourceName")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    JsonAssert.assertJsonEquals(
      """
      {
        "translations": [
          { 
            "resourceName": "hi_resource",
            "language": "en",
            "translation": "Hello!"
          },
          { 
            "resourceName": "hi_resource",
            "language": "fr",
            "translation": "Bonjour!"
          }
        ] 
      }
    """, testCall.response.content
    )
  }

  @Test
  fun `SHOULD get promotionEventTranslationValues ON GET promotion-event-translations WHEN param is null`() = withTestApplication(controller()) {

    val resourceName = "hi_resource"

    promotionTranslationService.mock(
      { getPromotionTranslationValuesByResourceName(null) }, PromotionEventTranslationValuesApiDto(
        listOf(
          PromotionEventTranslationValueApiDto(resourceName, "en", "Hello!"),
          PromotionEventTranslationValueApiDto(resourceName, "fr", "Bonjour!"),
        )
      )
    )

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/promotion-event-translations")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    JsonAssert.assertJsonEquals(
      """
      {
        "translations": [
          { 
            "resourceName": "hi_resource",
            "language": "en",
            "translation": "Hello!"
          },
          { 
            "resourceName": "hi_resource",
            "language": "fr",
            "translation": "Bonjour!"
          }
        ] 
      }
    """, testCall.response.content
    )
  }

  @Test
  fun `SHOULD save promotionEventTranslationValues ON POST promotion-event-translations`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/promotion-event-translations") {
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(
        """
          {
        "translations": [
          { 
            "resourceName": "hi_resource",
            "language": "en",
            "translation": "Hello!"
          },
          { 
            "resourceName": "hi_resource",
            "language": "fr",
            "translation": "Bonjour!"
          }
        ] 
      }
        """
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(promotionTranslationService) {
      savePromotionEventTranslationValues(
        PromotionEventTranslationValuesApiDto(
          listOf(
            PromotionEventTranslationValueApiDto("hi_resource", "en", "Hello!"),
            PromotionEventTranslationValueApiDto("hi_resource", "fr", "Bonjour!"),
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD delete promotionEventTranslationValues ON DELETE promotion-event-translations`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Delete, uri = "/promotion-event-translations?resourceName=hi_resource")
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionTranslationService) {
      deletePromotionTranslations("hi_resource")
    }
  }

  companion object {
    private const val USER_ID = "userId"
    private val now = Instant.now()
    private val jsonConverter: Json = defaultJsonConverter
    val challengeEventStub = ChallengeEventAdminApiDto(
      id = "testId",
      dateFrom = Instant.parse("2088-12-01T10:15:30.00Z"),
      dateTo = Instant.parse("2088-12-03T10:15:30.00Z"),
      challengesUpdateText = "some challenge update text",
      claimWidget = ClaimWidgetDto(
        bannerColor = "red",
        bannerEndColor = "green",
        textColor = "white",
        claimButtonText = "Ok",
        headerText = "Some header",
        progressBarSubtext = "Some sub progress",
        aheadButtonText = "abt",
      ),
      tutorialSteps = listOf("welcome", "cash_bonus"),
      challenges = listOf(
        ChallengeAdminApiDto(
          id = "id1",
          title = "title1",
          icon = "icon1",
          progressMax = 100,
          gameApplicationId = "gameApplicationId1",
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        ),
        ChallengeAdminApiDto(
          id = "id2",
          title = "title2",
          icon = "icon2",
          progressMax = 100,
          gameApplicationId = "gameApplicationId2",
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        )
      ),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      tutorialWidget = TutorialWidgetDto(
        backgroundColor = "#FFFFFF",
        highlightStartColor = "#FFFFFA",
        highlightEndColor = "#FFFFFB",
        tcUrl = "https://example.com",
        tutorialId = "tutorialId",
      ),
      bonusTracker = BonusTrackerDto(
        bonusId = "bonusId",
        progressMax = 10,
        completeText = "You won super bonus!"
      ),
    )

    private val customNotification = CustomNotificationDto(
      notificationId = "22a44fc9-a9bf-4817-bbcf-4e51ee9ea590",
      title = "some title",
      shortDescription = "some short description",
      icon = "https://storage.googleapis.com/public-playtime/images/block_puzzle_offer_icon.jpg",
      image = "https://storage.googleapis.com/public-playtime/images/block_puzzle_offer_image.jpg",
      backgroundColor = "#A090B0",
      size = CustomNotificationSize.MEDIUM,
      countdownTimerTarget = "60000",
      vibrationEnabled = true,
      soundEnabled = false,
      onClickAction = OnClickActionApiDto.openLinkInPopUp("https://www.justplayapps.com/privacy-policy"),
      backgroundActions = listOf(BackgroundAction.REFRESH_USER, BackgroundAction.REFRESH_OFFERS),
      textColor = "#5262FB",
      actionButtons = listOf(
        ButtonApiDto("Play", "#5262FB", "#FFFFFF", ButtonAction.scrollToOfferAndOpenPreGameScreen(200032)),
        ButtonApiDto("Discard", "#5262FB", "#FFFFFF", ButtonAction.discardNotification())
      ),
      label = "mass_notification",
    )
  }
}
