package com.moregames.playtime.service.admin

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.config.APP_ENGINE_SUFFIX
import com.moregames.base.ktor.GAE_INSTANCE_HEADER
import com.moregames.base.util.Constants.GOOGLE_CLOUD_PROJECT_ID
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.challenge.ChallengeEventConfigService
import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import com.moregames.playtime.util.defaultJsonConverter
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.serialization.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.Instant

@ExperimentalSerializationApi
class AdminServiceControllerTest {
  private val adminService: AdminService = mock()
  private val challengeEventConfigService: ChallengeEventConfigService = mock()
  private val timeService: TimeService = mock()

  companion object {
    const val EXPERIMENT_KEY = "k1"
  }


  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    install(ContentNegotiation) {
      json(json = defaultJsonConverter)
    }
    routing {
      AdminServiceController(
        adminService = adminService,
        timeService = timeService,
        challengeEventConfigService = challengeEventConfigService,
      ).startRouting(this)
    }
  }

  @Test
  fun `SHOULD response 404 ON GET experiments_{experimentKey} WHEN experiment does not exists `() = withTestApplication(controller()) {
    adminService.mock({ getExperiment(EXPERIMENT_KEY) }, null)
    val httpResponse = handleRequest(
      method = HttpMethod.Get,
      uri = "/experiments/$EXPERIMENT_KEY"
    ) {
      addHeader(GAE_INSTANCE_HEADER, "$GOOGLE_CLOUD_PROJECT_ID.$APP_ENGINE_SUFFIX")
    }
    assertThat(httpResponse.response.status()).isEqualTo(HttpStatusCode.NotFound)
    assertThat(httpResponse.response.content).isEqualTo("Experiment is not found by key k1")
  }

  @Test
  fun `SHOULD return experiment ON GET experiments_{experimentKey} WHEN experiment exists`() = withTestApplication(controller()) {
    val experiment = ExperimentFullApiDto(
      key = "k1",
      status = ExperimentStatus.RUNNING,
      startedAt = Instant.parse("2021-03-15T00:00:00Z"),
      finishedAt = null,
      minimumAppVersion = 64,
      allocations = listOf(
        AllocationApiDto("v1", BigDecimal("0.8")),
        AllocationApiDto("v2", BigDecimal("0.2"))
      )
    )
    adminService.mock({ getExperiment(EXPERIMENT_KEY) }, experiment)

    val httpResponse = handleRequest(
      method = HttpMethod.Get,
      uri = "/experiments/$EXPERIMENT_KEY"
    ) {
      addHeader(GAE_INSTANCE_HEADER, "$GOOGLE_CLOUD_PROJECT_ID.$APP_ENGINE_SUFFIX")
    }

    assertJsonEquals(
      httpResponse.response.content!!,
      //language=json
      """
      {
        "key": "k1",
        "status": "RUNNING",
        "startedAt": "2021-03-15T00:00:00Z",
        "minimumAppVersion": 64,
        "allocations": [
          {
            "variationKey": "v1",
            "value": "0.8"
          },
          {
            "variationKey": "v2",
            "value": "0.2"
          }
        ]
      }
    """
    )
  }

  @Test
  fun `SHOULD return all experiments ON GET experiments`() = withTestApplication(controller()) {

    val experiments = listOf(
      ExperimentApiDto("k1", ExperimentStatus.RUNNING),
      ExperimentApiDto("k2", ExperimentStatus.OFFBOARDED),
      ExperimentApiDto("k3", ExperimentStatus.NOT_STARTED),
      ExperimentApiDto("k4", ExperimentStatus.FINISHED),
    )
    adminService.mock({ getExperimentList() }, ExperimentListApiDto(experiments))

    val httpResponse = handleRequest(
      method = HttpMethod.Get,
      uri = "/experiments"
    ) {
      addHeader(GAE_INSTANCE_HEADER, "$GOOGLE_CLOUD_PROJECT_ID.$APP_ENGINE_SUFFIX")
    }

    assertJsonEquals(
      httpResponse.response.content!!,
      //language=json
      """
          {
            "items": [
              {
                "key": "k1",
                "status": "RUNNING"
              },
              {
                "key": "k2",
                "status": "OFFBOARDED"
              },
              {
                "key": "k3",
                "status": "NOT_STARTED"
              }, 
              {
                "key": "k4",
                "status": "FINISHED"
              }
            ]
          }
      """
    )
  }


  @Test
  fun `SHOULD update challenge event`() = withTestApplication(controller()) {
    val testCall = handleRequest(
      method = HttpMethod.Put,
      uri = "/challenges/challenge-event"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(GAE_INSTANCE_HEADER, "$GOOGLE_CLOUD_PROJECT_ID.$APP_ENGINE_SUFFIX")
      //language=json
      setBody(
        """        
          {
            "id": "testId",
            "dateFrom": "2088-12-01T10:15:30Z",
            "dateTo": "2088-12-03T10:15:30Z",
            "challengesUpdateText": "some challenge update text",
            "claimWidget": {
              "bannerColor": "red",
              "bannerEndColor": "green",
              "textColor": "white",
              "claimButtonText": "Ok",
              "headerText": "Some header",
              "progressBarSubtext": "Some sub progress",
              "claimButtonTextMinified": "cbtm",
              "aheadButtonText": "abt",
              "aheadButtonTextMinified": "abtm"           
            },
            "tutorialSteps": [
               "welcome",
               "cash_bonus"
            ],
            "challenges": [
              {
                "id": "id1",
                "title": "title1",
                "icon": "icon1",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId1",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true,
                "challengeType": "REGULAR"           
              },
              {
                "id": "id2",
                "title": "title2",
                "icon": "icon2",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId2",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true,
                "challengeType": "REGULAR"            
              }
            ],
            "enabled": true,
            "eventType": "GLOBAL",
            "tutorialWidget": {
              "backgroundColor": "#FFFFFF",
              "highlightStartColor": "#FFFFFA",
              "highlightEndColor": "#FFFFFB",
              "tcUrl": "https://example.com",
              "tutorialId": "tutorialId"
             },
            "bonusTracker": {
              "bonusId": "bonusId",
              "progressMax": 10,
              "completeText": "You won super bonus!"
            }
          }
    """
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(challengeEventConfigService) {
      updateChallengeEvent(
        ChallengeEventAdminApiDto(
          id = "testId",
          dateFrom = Instant.parse("2088-12-01T10:15:30.00Z"),
          dateTo = Instant.parse("2088-12-03T10:15:30.00Z"),
          challengesUpdateText = "some challenge update text",
          claimWidget = ClaimWidgetDto(
            bannerColor = "red",
            bannerEndColor = "green",
            textColor = "white",
            claimButtonText = "Ok",
            headerText = "Some header",
            progressBarSubtext = "Some sub progress",
            aheadButtonText = "abt",
          ),
          tutorialSteps = listOf("welcome", "cash_bonus"),
          challenges = listOf(
            ChallengeAdminApiDto(
              id = "id1",
              title = "title1",
              icon = "icon1",
              progressMax = 100,
              gameApplicationId = "gameApplicationId1",
              calculator = ChallengeProgressCalculatorType.LEVEL_ID,
              order = 1,
              goal = 100,
              applyEarningsCut = true,
              challengeType = ChallengeType.REGULAR,
            ),
            ChallengeAdminApiDto(
              id = "id2",
              title = "title2",
              icon = "icon2",
              progressMax = 100,
              gameApplicationId = "gameApplicationId2",
              calculator = ChallengeProgressCalculatorType.LEVEL_ID,
              order = 1,
              goal = 100,
              applyEarningsCut = true,
              challengeType = ChallengeType.REGULAR,
            )
          ),
          enabled = true,
          eventType = ChallengeEventType.GLOBAL,
          bonusTracker = BonusTrackerDto(
            bonusId = "bonusId",
            progressMax = 10,
            completeText = "You won super bonus!"
          ),
          tutorialWidget = TutorialWidgetDto(
            backgroundColor = "#FFFFFF",
            highlightStartColor = "#FFFFFA",
            highlightEndColor = "#FFFFFB",
            tcUrl = "https://example.com",
            tutorialId = "tutorialId",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD create challenge event`() = withTestApplication(controller()) {
    val testCall = handleRequest(
      method = HttpMethod.Post,
      uri = "/challenges/challenge-event"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(GAE_INSTANCE_HEADER, "$GOOGLE_CLOUD_PROJECT_ID.$APP_ENGINE_SUFFIX")
      //language=json
      setBody(
        """        
          {
            "id": "testId",
            "dateFrom": "2088-12-01T10:15:30Z",
            "dateTo": "2088-12-03T10:15:30Z",
            "challengesUpdateText": "some challenge update text",
            "claimWidget": {
              "bannerColor": "red",
              "bannerEndColor": "green",
              "textColor": "white",
              "claimButtonText": "Ok",
              "headerText": "Some header",
              "progressBarSubtext": "Some sub progress",
              "claimButtonTextMinified": "cbtm",
              "aheadButtonText": "abt",
              "aheadButtonTextMinified": "abtm"          
            },
            "tutorialSteps": [
               "welcome",
               "cash_bonus"
            ],
            "challenges": [
              {
                "id": "id1",
                "title": "title1",
                "icon": "icon1",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId1",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true,
                "challengeType": "REGULAR"            
              },
              {
                "id": "id2",
                "title": "title2",
                "icon": "icon2",
                "progressMax": 100,
                "gameApplicationId": "gameApplicationId2",
                "calculator": "LEVEL_ID",
                "order": 1,
                "goal": 100,
                "applyEarningsCut": true,
                "challengeType": "REGULAR"          
              }
            ],
            "enabled": true,
            "eventType": "GLOBAL", 
            "bonusTracker": {
              "bonusId": "bonusId",
              "progressMax": 10,
              "completeText": "You won super bonus!"
            },
             "tutorialWidget": {
                "backgroundColor": "#FFFFFF",
                "highlightStartColor": "#FFFFFA",
                "highlightEndColor": "#FFFFFB",
                "tcUrl": "https://example.com",
                "tutorialId": "tutorialId"
              }
          }
    """
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(challengeEventConfigService) {
      createChallengeEvent(
        ChallengeEventAdminApiDto(
          id = "testId",
          dateFrom = Instant.parse("2088-12-01T10:15:30.00Z"),
          dateTo = Instant.parse("2088-12-03T10:15:30.00Z"),
          challengesUpdateText = "some challenge update text",
          claimWidget = ClaimWidgetDto(
            bannerColor = "red",
            bannerEndColor = "green",
            textColor = "white",
            claimButtonText = "Ok",
            headerText = "Some header",
            progressBarSubtext = "Some sub progress",
            aheadButtonText = "abt",
          ),
          tutorialSteps = listOf("welcome", "cash_bonus"),
          challenges = listOf(
            ChallengeAdminApiDto(
              id = "id1",
              title = "title1",
              icon = "icon1",
              progressMax = 100,
              gameApplicationId = "gameApplicationId1",
              calculator = ChallengeProgressCalculatorType.LEVEL_ID,
              order = 1,
              goal = 100,
              applyEarningsCut = true,
              challengeType = ChallengeType.REGULAR,
            ),
            ChallengeAdminApiDto(
              id = "id2",
              title = "title2",
              icon = "icon2",
              progressMax = 100,
              gameApplicationId = "gameApplicationId2",
              calculator = ChallengeProgressCalculatorType.LEVEL_ID,
              order = 1,
              goal = 100,
              applyEarningsCut = true,
              challengeType = ChallengeType.REGULAR,
            )
          ),
          enabled = true,
          eventType = ChallengeEventType.GLOBAL,
          tutorialWidget = TutorialWidgetDto(
            backgroundColor = "#FFFFFF",
            highlightStartColor = "#FFFFFA",
            highlightEndColor = "#FFFFFB",
            tcUrl = "https://example.com",
            tutorialId = "tutorialId",
          ),
          bonusTracker = BonusTrackerDto(
            bonusId = "bonusId",
            progressMax = 10,
            completeText = "You won super bonus!"
          ),
        )
      )
    }
  }
}
