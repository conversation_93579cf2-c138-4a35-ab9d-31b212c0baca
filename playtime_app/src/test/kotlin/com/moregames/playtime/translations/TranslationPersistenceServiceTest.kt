package com.moregames.playtime.translations

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.dto.AppPlatform
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.utils.appTranslationsStub
import com.moregames.playtime.utils.backendTranslationsStub
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.util.*

@ExtendWith(DatabaseExtension::class)
class TranslationPersistenceServiceTest(
  private val database: Database,
) {
  private lateinit var service: TranslationPersistenceService

  @BeforeEach
  fun before() {
    service = TranslationPersistenceService(database)
  }

  private val defaultLanguages = listOf(Locale.ENGLISH, Locale.FRENCH, Locale.CANADA_FRENCH)

  @Test
  fun `SHOULD get translations on getBackendTranslations`() {
    prepareBackendTranslations()

    val actual = runBlocking {
      service.getBackendTranslations(defaultLanguages)
    }

    assertThat(actual).isEqualTo(backendTranslationsStub)
  }

  @Test
  fun `SHOULD get translations with market specific replacements ON getBackendTranslations`() {
    val expected = mapOf(
      "en" to mapOf("\$_support" to "replace_en_support", "\$_having_issues" to "en_having_issues", "\$_claim_code" to "en_claim_code"),
      "fr" to mapOf("\$_support" to "fr_support", "\$_having_issues" to "fr_having_issues", "\$_claim_code" to "fr_claim_code"),
      "fr-ca" to mapOf("\$_support" to "fr-CA_support", "\$_having_issues" to "replace_fr_ca_having_issues", "\$_claim_code" to "fr-CA_claim_code")
    )
    prepareBackendTranslations()
    transaction(database) {
      MarketStringResourceTranslationTable.insert {
        it[language] = "en"
        it[resourceName] = "\$_support"
        it[translation] = "replace_en_support"
      }
      MarketStringResourceTranslationTable.insert {
        it[language] = "fr-ca"
        it[resourceName] = "\$_having_issues"
        it[translation] = "replace_fr_ca_having_issues"
      }
    }

    val actual = runBlocking {
      service.getBackendTranslations(defaultLanguages)
    }

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD get only en translations ON getBackendTranslations WHEN only en language provided`() {
    prepareBackendTranslations()

    val actual = runBlocking {
      service.getBackendTranslations(listOf(Locale.ENGLISH))
    }

    assertThat(actual).isEqualTo(backendTranslationsStub.filterKeys { it == "en" })
  }

  @Test
  fun `SHOULD get only en translations ON getBackendTranslations WHEN only fr language provided`() {
    prepareBackendTranslations()

    val actual = runBlocking {
      service.getBackendTranslations(listOf(Locale.FRENCH))
    }

    assertThat(actual).isEqualTo(backendTranslationsStub.filterKeys { it == "fr" })
  }

  @Test
  fun `SHOULD get only en + fr-ca translations ON getBackendTranslations WHEN only en + canadian french provided`() {
    prepareBackendTranslations()

    val actual = runBlocking {
      service.getBackendTranslations(listOf(Locale.ENGLISH, Locale.CANADA_FRENCH))
    }

    assertThat(actual).isEqualTo(backendTranslationsStub.filterKeys { it == "en" || it == "fr-ca" })
  }

  @Test
  fun `SHOULD get empty map ON getBackendTranslations WHEN translation table is empty`() {
    clearBackendTranslations()

    val actual = runBlocking {
      service.getBackendTranslations(defaultLanguages)
    }

    assertThat(actual).isEqualTo(emptyMap())
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD get translations on getAppTranslations`(appPlatform: AppPlatform) {
    prepareAppTranslations()

    val actual = runBlocking {
      service.getAppTranslations(defaultLanguages, appPlatform)
    }

    assertThat(actual).isEqualTo(appTranslationsStub.filterKeys { it in setOf("en", "fr", "fr-ca") })
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD get translations with market specific replacements ON getAppTranslations`(appPlatform: AppPlatform) {
    val expected = mapOf(
      "en" to mapOf("support" to "replace_en_support", "having_issues" to "en_having_issues", "claim_code" to "en_claim_code"),
      "fr" to mapOf("support" to "fr_support", "having_issues" to "fr_having_issues", "claim_code" to "fr_claim_code"),
      "fr-ca" to mapOf("support" to "fr-CA_support", "having_issues" to "replace_fr_ca_having_issues", "claim_code" to "fr-CA_claim_code")
    )
    prepareAppTranslations()
    transaction(database) {
      AppMarketTranslationTable.insert {
        it[language] = "en"
        it[resourceName] = "support"
        it[AppMarketTranslationTable.appPlatform] = appPlatform.name
        it[translation] = "replace_en_support"
      }
      AppMarketTranslationTable.insert {
        it[language] = "fr-ca"
        it[resourceName] = "having_issues"
        it[AppMarketTranslationTable.appPlatform] = appPlatform.name
        it[translation] = "replace_fr_ca_having_issues"
      }
    }

    val actual = runBlocking {
      service.getAppTranslations(defaultLanguages, appPlatform)
    }

    assertThat(actual).isEqualTo(expected)
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD get only en translations ON getAppTranslations WHEN only en language provided`(appPlatform: AppPlatform) {
    prepareAppTranslations()

    val actual = runBlocking {
      service.getAppTranslations(listOf(Locale.ENGLISH), appPlatform)
    }

    assertThat(actual).isEqualTo(appTranslationsStub.filterKeys { it == "en" })
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD get only en translations ON getAppTranslations WHEN only fr language provided`(appPlatform: AppPlatform) {
    prepareAppTranslations()

    val actual = runBlocking {
      service.getAppTranslations(listOf(Locale.FRENCH), appPlatform)
    }

    assertThat(actual).isEqualTo(appTranslationsStub.filterKeys { it == "fr" })
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD get only en + fr-ca translations ON getAppTranslations WHEN only en + canadian french provided`(appPlatform: AppPlatform) {
    prepareAppTranslations()

    val actual = runBlocking {
      service.getAppTranslations(listOf(Locale.ENGLISH, Locale.CANADA_FRENCH), appPlatform)
    }

    assertThat(actual).isEqualTo(appTranslationsStub.filterKeys { it == "en" || it == "fr-ca" })
  }

  private fun clearBackendTranslations() {
    transaction(database) {
      MarketStringResourceTranslationTable.deleteAll()
      StringResourceTranslationTable.deleteAll()
      StringResourceTable.deleteAll()
    }
  }

  private fun prepareBackendTranslations() {
    clearBackendTranslations()
    val resources = listOf("\$_support", "\$_having_issues", "\$_claim_code")

    transaction(database) {
      StringResourceTable.batchInsert(resources) { resource ->
        this[StringResourceTable.resourceName] = resource
      }
      StringResourceTranslationTable.batchInsert(resources) { resource ->
        this[StringResourceTranslationTable.language] = "en"
        this[StringResourceTranslationTable.resourceName] = resource
        this[StringResourceTranslationTable.translation] = "en_${resource.removePrefix("\$_")}"
      }
      StringResourceTranslationTable.batchInsert(resources) { resource ->
        this[StringResourceTranslationTable.language] = "fr"
        this[StringResourceTranslationTable.resourceName] = resource
        this[StringResourceTranslationTable.translation] = "fr_${resource.removePrefix("\$_")}"
      }
      StringResourceTranslationTable.batchInsert(resources) { resource ->
        this[StringResourceTranslationTable.language] = "fr-ca"
        this[StringResourceTranslationTable.resourceName] = resource
        this[StringResourceTranslationTable.translation] = "fr-CA_${resource.removePrefix("\$_")}"
      }
    }
  }

  private fun clearAppTranslations() {
    transaction(database) {
      AppMarketTranslationTable.deleteAll()
      AppTranslationTable.deleteAll()
    }
  }

  private fun prepareAppTranslations() {
    clearAppTranslations()
    val resources = listOf("support", "having_issues", "claim_code")

    AppPlatform.values().forEach {
      transaction(database) {
        AppTranslationTable.batchInsert(resources) { resource ->
          this[AppTranslationTable.language] = "en"
          this[AppTranslationTable.resourceName] = resource
          this[AppTranslationTable.appPlatform] = it.name
          this[AppTranslationTable.translation] = "en_$resource"
        }
        AppTranslationTable.batchInsert(resources) { resource ->
          this[AppTranslationTable.language] = "fr"
          this[AppTranslationTable.resourceName] = resource
          this[AppTranslationTable.appPlatform] = it.name
          this[AppTranslationTable.translation] = "fr_$resource"
        }
        AppTranslationTable.batchInsert(resources) { resource ->
          this[AppTranslationTable.language] = "fr-ca"
          this[AppTranslationTable.resourceName] = resource
          this[AppTranslationTable.appPlatform] = it.name
          this[AppTranslationTable.translation] = "fr-CA_$resource"
        }
      }
    }
  }
}