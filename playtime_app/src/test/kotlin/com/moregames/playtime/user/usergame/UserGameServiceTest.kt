package com.moregames.playtime.user.usergame

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.usergame.dto.RemindToPlayDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.time.Duration
import java.time.Instant

class UserGameServiceTest {
  private val userGamePersistenceService: UserGamePersistenceService = mock()
  private val timeService: TimeService = mock()
  private val abTestingService: AbTestingService = mock()
  private val userService: UserService = mock()
  private val gamesService: GamesService = mock()
  private val messageBus: MessageBus = mock()

  private val service = UserGameService(
    userGamePersistenceService = userGamePersistenceService,
    messageBus = messageBus,
    timeService = timeService,
    abTestingService = abTestingService,
    userService = userService,
    gamesService = gamesService,
  )

  private val now = Instant.now()
  private val userId = "userId"

  @BeforeEach
  fun before() {
    timeService.mock({ now() }, now)
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.ANDROID_NOTIFY_TO_PLAY) }, true)
  }

  @Test
  fun `SHOULD process pre game event ON onPreGameScreenOpened WHEN NOT user is a participant`() {
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.ANDROID_NOTIFY_TO_PLAY) }, false)
    userGamePersistenceService.mock({ trackUserOpenedPreGameScreenReturnNew(userId) }, true)

    runBlocking { service.onPreGameScreenOpened(userId) }

    verifyBlocking(userGamePersistenceService) { trackUserOpenedPreGameScreenReturnNew(userId) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT send event ON onPreGameScreenOpened WHEN user is a participant AND NOT first event`() {
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.ANDROID_NOTIFY_TO_PLAY) }, true)
    userGamePersistenceService.mock({ trackUserOpenedPreGameScreenReturnNew(userId) }, false)

    runBlocking { service.onPreGameScreenOpened(userId) }

    verifyBlocking(userGamePersistenceService) { trackUserOpenedPreGameScreenReturnNew(userId) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD process pre game event ON onPreGameScreenOpened WHEN user is a participant`() {
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.ANDROID_NOTIFY_TO_PLAY) }, true)
    userGamePersistenceService.mock({ trackUserOpenedPreGameScreenReturnNew(userId) }, true)

    runBlocking { service.onPreGameScreenOpened(userId) }

    verifyBlocking(userGamePersistenceService) { trackUserOpenedPreGameScreenReturnNew(userId) }
    verifyBlocking(messageBus) {
      publish(
        RemindToPlayDto(userId = userId, notificationsSent = 0),
        delayUntil = timeService.now() + Duration.ofMinutes(15)
      )
    }
  }

  @Test
  fun `SHOULD return null ON getGamesAdVariantValue_gameId WHEN NOT user is a participant AND NOT game played too long ago`() {
    val fewMinutesAgo = now.minus(Duration.ofMinutes(37))

    abTestingService.mock({ getGamesAdVariantValue(userId) }, null)
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        42 to
          UserPersistenceService.GamePlayStatusDto(
            gameId = 42,
            coins = 420,
            playedRecently = true,
            firstPlayedAt = fewMinutesAgo,
            lastPlayedAt = now,
          )
      )
    )

    runBlocking { service.getGamesAdVariantValue(userId, 42) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return ad variant value ON getGamesAdVariantValue_gameId WHEN user is a participant AND game never played`() {
    val adVariantConfig = AbTestingService.AdVariantConfig(
      adVariant = "adVariant",
      activeDuring = Duration.ofMinutes(40)
    )

    abTestingService.mock({ getGamesAdVariantValue(userId) }, adVariantConfig)
    userService.mock({ loadUserGameCoins(userId) }, emptyMap())

    runBlocking { service.getGamesAdVariantValue(userId, 42) }.let { actual ->
      assertThat(actual).isEqualTo("adVariant")
    }
  }

  @Test
  fun `SHOULD return null ON getGamesAdVariantValue_gameId WHEN user is a participant AND game played too long ago`() {
    val longTimeAgo = now.minus(Duration.ofHours(4))
    val adVariantConfig = AbTestingService.AdVariantConfig(
      adVariant = "someAdVariant",
      activeDuring = Duration.ofHours(3)
    )

    abTestingService.mock({ getGamesAdVariantValue(userId) }, adVariantConfig)
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        42 to
          UserPersistenceService.GamePlayStatusDto(
            gameId = 42,
            coins = 420,
            playedRecently = true,
            firstPlayedAt = longTimeAgo,
            lastPlayedAt = now,
          )
      )
    )

    runBlocking { service.getGamesAdVariantValue(userId, 42) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return ad variant value ON getGamesAdVariantValue_gameId WHEN user is a participant AND NOT game played too long ago`() {
    val fewMinutesAgo = now.minus(Duration.ofMinutes(37))
    val adVariantConfig = AbTestingService.AdVariantConfig(
      adVariant = "someAdVariant",
      activeDuring = Duration.ofMinutes(40)
    )

    abTestingService.mock({ getGamesAdVariantValue(userId) }, adVariantConfig)
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        42 to
          UserPersistenceService.GamePlayStatusDto(
            gameId = 42,
            coins = 420,
            playedRecently = true,
            firstPlayedAt = fewMinutesAgo,
            lastPlayedAt = now,
          )
      )
    )

    runBlocking { service.getGamesAdVariantValue(userId, 42) }.let { actual ->
      assertThat(actual).isEqualTo("someAdVariant")
    }
  }

  @Test
  fun `SHOULD return null ON getGamesAdVariantValue_application WHEN NOT game known`() {
    val fewMinutesAgo = now.minus(Duration.ofMinutes(37))
    val adVariantConfig = AbTestingService.AdVariantConfig(
      adVariant = "someAdVariant",
      activeDuring = Duration.ofMinutes(40)
    )

    abTestingService.mock({ getGamesAdVariantValue(userId) }, adVariantConfig)
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        42 to
          UserPersistenceService.GamePlayStatusDto(
            gameId = 42,
            coins = 420,
            playedRecently = true,
            firstPlayedAt = fewMinutesAgo,
            lastPlayedAt = now,
          )
      )
    )
    gamesService.mock({ getGameId("com.game", AppPlatform.IOS) }, null)

    runBlocking { service.getGamesAdVariantValue(userId, "com.game", AppPlatform.IOS) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return null ON getGamesAdVariantValue_application WHEN game known AND NOT ad variant defined`() {
    abTestingService.mock({ getGamesAdVariantValue(userId) }, null)
    gamesService.mock({ getGameId("com.game", AppPlatform.IOS) }, 42)

    runBlocking { service.getGamesAdVariantValue(userId, "com.game", AppPlatform.IOS) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return ad variant value ON getGamesAdVariantValue_application WHEN game known AND ad variant defined`() {
    val fewMinutesAgo = now.minus(Duration.ofMinutes(37))
    val adVariantConfig = AbTestingService.AdVariantConfig(
      adVariant = "someAdVariant",
      activeDuring = Duration.ofMinutes(40)
    )

    abTestingService.mock({ getGamesAdVariantValue(userId) }, adVariantConfig)
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        42 to
          UserPersistenceService.GamePlayStatusDto(
            gameId = 42,
            coins = 420,
            playedRecently = true,
            firstPlayedAt = fewMinutesAgo,
            lastPlayedAt = now,
          )
      )
    )
    gamesService.mock({ getGameId("com.game", AppPlatform.IOS) }, 42)

    runBlocking { service.getGamesAdVariantValue(userId, "com.game", AppPlatform.IOS) }.let { actual ->
      assertThat(actual).isEqualTo("someAdVariant")
    }
  }
}