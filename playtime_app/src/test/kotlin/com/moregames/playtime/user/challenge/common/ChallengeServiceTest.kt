package com.moregames.playtime.user.challenge.common

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.PackagePrivate
import com.moregames.base.util.mock
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.Challenge
import com.moregames.playtime.user.challenge.dto.ChallengeEvent
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.ChallengeEventState
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.dto.ChallengeProgressTrackingDto
import com.moregames.playtime.user.challenge.dto.ChallengeState
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.dto.UserChallengeEvent
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.math.BigDecimal
import java.time.Instant

@OptIn(PackagePrivate::class)
class ChallengeServiceTest {

  private val challengeServicePersistenceService: ChallengeEventPersistenceService = mock()
  private val challengeEventResolver: ChallengeEventResolver = mock()
  private val abTestingService: AbTestingService = mock()


  val underTest = ChallengeService(
    challengeServicePersistenceService,
    challengeEventResolver,
    abTestingService
  )

  companion object {
    const val USER_ID = "userId"
    const val EVENT_ID = "eventId"
    const val CHALLENGE_ID = "challengeId"
    val challenge = Challenge(
      id = ChallengeId(CHALLENGE_ID),
      eventId = ChallengeEventId(EVENT_ID),
      title = "title1",
      icon = "icon1",
      progressMax = 100,
      gameId = 200100,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )
    val challengeEvent = ChallengeEvent(
      id = ChallengeEventId(EVENT_ID),
      dateFrom = Instant.parse("2020-03-04T00:00:00.00Z"),
      dateTo = Instant.parse("2020-03-04T00:00:00.00Z"),
      cfg = "{}",
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      challenges = listOf(challenge),
      bonusId = "bonusId",
    )
    val userChallenge = UserChallenge(
      challenge = challenge,
      userId = USER_ID,
      progress = 100,
      coins = 0,
      state = ChallengeState.CLAIMED,
      completedAt = Instant.parse("2020-03-04T00:00:00.00Z"),
      updatedAt = Instant.parse("2020-03-04T00:00:00.00Z"),
    )
    val userChallengeEvent = UserChallengeEvent(
      userId = USER_ID,
      eventId = ChallengeEventId(EVENT_ID),
      state = ChallengeEventState.COMPLETED,
      applovinNonBannerRevenue = BigDecimal.TWO,
      earnings = BigDecimal.TWO,
      startedAt = Instant.parse("2020-03-04T00:00:00.00Z"),
    )
    val challengeEventAdmin = ChallengeEvent(
      id = ChallengeEventId(EVENT_ID),
      dateFrom = Instant.parse("2020-03-04T00:00:00.00Z"),
      dateTo = Instant.parse("2020-03-04T00:00:00.00Z"),
      cfg = "{}",
      enabled = true,
      challenges = listOf(challenge.copy(id = ChallengeId("id1")), challenge.copy(id = ChallengeId("id2"))),
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
  }

  @BeforeEach
  fun setup() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES, true) }, Variations.SHOW_CHALLENGES)
  }

  @Test
  fun `SHOULD return ChallengeProgressTracking on getChallengeProgressTracking WHEN there is an uncompleted challenge`() = runTest {
    val gameId = 42
    challengeEventResolver.mock({ getChallengeEvent(USER_ID) }, challengeEvent)
    challengeServicePersistenceService.mock(
      { getUserChallenges(challengeEvent.id, USER_ID) }, listOf(
        userChallenge.copy(state = ChallengeState.COMPLETED, challenge = challenge.copy(gameId = gameId)),
        userChallenge.copy(state = ChallengeState.IN_PROGRESS, challenge = challenge.copy(gameId = gameId, progressMax = 56)),
        userChallenge.copy(state = ChallengeState.IN_PROGRESS, challenge = challenge.copy(gameId = 43)),

        )
    )
    val result = underTest.getChallengeProgressTracking(USER_ID, 42)
    assertThat(result).isEqualTo(ChallengeProgressTrackingDto(56))
  }

  @Test
  fun `SHOULD return null on getChallengeProgressTracking WHEN all challenges are completed`() = runTest {
    val gameId = 42
    challengeEventResolver.mock({ getChallengeEvent(USER_ID) }, challengeEvent)
    challengeServicePersistenceService.mock(
      { getUserChallenges(challengeEvent.id, USER_ID) }, listOf(
        userChallenge.copy(state = ChallengeState.COMPLETED, challenge = challenge.copy(gameId = gameId)),
        userChallenge.copy(state = ChallengeState.CLAIMED, challenge = challenge.copy(gameId = gameId)),
        userChallenge.copy(state = ChallengeState.IN_PROGRESS, challenge = challenge.copy(gameId = 43)),

        )
    )
    val result = underTest.getChallengeProgressTracking(USER_ID, 42)
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return null on getChallengeProgressTracking WHEN there is no event`() = runTest {
    challengeEventResolver.mock({ getChallengeEvent(USER_ID) }, null)
    val result = underTest.getChallengeProgressTracking(USER_ID, 42)
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return ChallengeEventDto by id`() {
    challengeServicePersistenceService.mock({ loadChallengeEventById(ChallengeEventId(EVENT_ID)) }, challengeEvent)
    val result = runBlocking { underTest.getChallengeEventById(ChallengeEventId(EVENT_ID)) }
    assertThat(result).isEqualTo(challengeEvent)
    verifyBlocking(challengeServicePersistenceService) { loadChallengeEventById(ChallengeEventId(EVENT_ID)) }
  }

  @Test
  fun `SHOULD return result of userHadChallengesInLastday`() {
    challengeServicePersistenceService.mock({ userHadChallengesInLastDay(USER_ID) }, true)
    val result = runBlocking { underTest.userHadChallengesInLastDay(USER_ID) }
    assertThat(result).isEqualTo(true)
    verifyBlocking(challengeServicePersistenceService) { userHadChallengesInLastDay(USER_ID) }
  }

  @Test
  fun `SHOULD claim challenge event`() {
    challengeServicePersistenceService.mock({ claimEvent(ChallengeEventId(EVENT_ID), USER_ID, BigDecimal.TWO) }, true)
    val result = runBlocking { underTest.claimEvent(ChallengeEventId(EVENT_ID), USER_ID, BigDecimal.TWO) }
    assertThat(result).isTrue()
    verifyBlocking(challengeServicePersistenceService) { claimEvent(ChallengeEventId(EVENT_ID), USER_ID, BigDecimal.TWO) }
  }

  @Test
  fun `SHOULD claim challenge`() {
    challengeServicePersistenceService.mock({ claimChallenge(ChallengeId(CHALLENGE_ID), USER_ID, 12) }, true)
    val result = runBlocking { underTest.claimChallenge(ChallengeId(CHALLENGE_ID), USER_ID, 12) }
    assertThat(result).isTrue()
    verifyBlocking(challengeServicePersistenceService) { claimChallenge(ChallengeId(CHALLENGE_ID), USER_ID, 12) }
  }

  @Test
  fun `SHOULD return user challenge`() {
    challengeServicePersistenceService.mock({ getUserChallenge(userChallenge.challenge.id, USER_ID) }, userChallenge)
    val result = runBlocking { underTest.getUserChallenge(userChallenge.challenge.id, USER_ID) }
    assertThat(result).isEqualTo(userChallenge)
  }

  @Test
  fun `SHOULD update incomplete challnge event revenue`() {
    challengeServicePersistenceService.mock({ updateIncompleteChallengeEventRevenue(USER_ID, ChallengeEventId(EVENT_ID), BigDecimal.TEN) }, 1)
    val result = runBlocking { underTest.updateIncompleteChallengeEventRevenue(USER_ID, ChallengeEventId(EVENT_ID), BigDecimal.TEN) }
    assertThat(result).isEqualTo(1)
    verifyBlocking(challengeServicePersistenceService) { updateIncompleteChallengeEventRevenue(USER_ID, ChallengeEventId(EVENT_ID), BigDecimal.TEN) }
  }

  @Test
  fun `SHOUL return user challenge event ON getUserChallengeEvent`() {
    challengeServicePersistenceService.mock({ getUserChallengeEvent(USER_ID, challengeEvent.id) }, userChallengeEvent)
    val result = runBlocking { underTest.getUserChallengeEvent(USER_ID, challengeEvent.id) }
    assertThat(result).isEqualTo(userChallengeEvent)
  }

  @Test
  fun `SHOULD create challenge event`() {
    runBlocking { underTest.createChallengeEvent(challengeEventAdmin) }
    verifyBlocking(challengeServicePersistenceService) { createChallengeEvent(challengeEventAdmin) }
  }

  @Test
  fun `SHOULD update challenge event`() {
    runBlocking { underTest.updateChallengeEvent(challengeEventAdmin) }
    verifyBlocking(challengeServicePersistenceService) { updateChallengeEvent(challengeEventAdmin) }
  }

  @Test
  fun `SHOULD return challenge event List`() {
    val dateFrom = Instant.parse("2020-03-04T00:00:00.00Z")
    val dateTo = Instant.parse("2020-03-04T00:00:00.00Z")
    val enabled = true
    challengeServicePersistenceService.mock({ getChallengeEventList(dateFrom, dateTo, enabled) }, listOf(challengeEventAdmin))
    val result = runBlocking { underTest.getChallengeEventList(dateFrom, dateTo, enabled) }
    assertThat(result).isEqualTo(listOf(challengeEventAdmin))
  }

  @Test
  fun `SHOULD update challengeProgress`() {
    val challengeProgress: ChallengeProgress = mock()
    val state = ChallengeState.COMPLETED
    runBlocking { underTest.updateChallengeProgress(userChallenge, challengeProgress, state) }
    verifyBlocking(challengeServicePersistenceService) { updateChallengeProgress(userChallenge, challengeProgress, state) }
  }

  @Test
  fun `SHOULD start user's challenge`() {
    runBlocking { underTest.startUserChallenge(ChallengeId(CHALLENGE_ID), USER_ID) }
    verifyBlocking(challengeServicePersistenceService) { startUserChallenge(ChallengeId(CHALLENGE_ID), USER_ID) }
  }

  @Test
  fun `SHOULD complete user's event`() {
    runBlocking { underTest.completeEvent(ChallengeEventId(EVENT_ID), USER_ID) }
    verifyBlocking(challengeServicePersistenceService) { completeEvent(ChallengeEventId(EVENT_ID), USER_ID) }
  }

  @Test
  fun `SHOULD start user's event`() {
    runBlocking { underTest.startUserEvent(ChallengeEventId(EVENT_ID), USER_ID) }
    verifyBlocking(challengeServicePersistenceService) { startUserEvent(ChallengeEventId(EVENT_ID), USER_ID) }
  }

  @Test
  fun `SOULD complete challenge`() {
    val challengeId = "CH_ID"
    challengeServicePersistenceService.mock({ forceCompleteUserChallenge(challengeId, USER_ID) }, true)
    runBlocking { underTest.forceCompleteChallenge(USER_ID, challengeId) }
    verifyBlocking(challengeServicePersistenceService) { forceCompleteUserChallenge(challengeId, USER_ID) }
  }


  @Test
  fun `SHOULD return emptyList ON getUserChallenges WHEN challengeResolver returns null`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, null)
    val result = runBlocking { underTest.getUserChallenges(USER_ID) }
    assertThat(result).isEmpty()
    verifyNoInteractions(challengeServicePersistenceService)
  }

  @Test
  fun `SHOULD return list of userChallenges ON getUserChallenges WHEN challengeResolver returns lists`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, challengeEvent)
    val expected = listOf(
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("1"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("2"))),
    )
    challengeServicePersistenceService.mock({ getUserChallenges(challengeEvent.id, USER_ID) }, expected)
    //when
    val result = runBlocking { underTest.getUserChallenges(USER_ID) }
    //then
    assertThat(result).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return true ON challengeEventExists WHEN challengeResolver returns challengeEvent`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, challengeEvent)
    val result = runBlocking { underTest.challengeEventExists(USER_ID) }
    assertThat(result).isTrue()
  }

  @Test
  fun `SHOULD return false ON challengeEventExists WHEN challengeResolver returns null`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, null)
    val result = runBlocking { underTest.challengeEventExists(USER_ID) }
    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD return null ON getCurrentChallengeEvent WHEN resolver returns null`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, null)
    val result = runBlocking { underTest.getCurrentChallengeEvent(USER_ID) }
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return current challenge event ON getCurrentChallengeEvent`() {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, challengeEvent)
    val result = runBlocking { underTest.getCurrentChallengeEvent(USER_ID) }
    assertThat(result).isEqualTo(challengeEvent)
  }

  @Test
  fun `SHOULD return current challenge event without special challenges ON getCurrentChallengeEvent WHEN user is not on special variation`() {
    challengeEventResolver.mock(
      { getChallengeEvent(userId = USER_ID) },
      challengeEvent.copy(challenges = listOf(challenge, challenge.copy(challengeType = ChallengeType.SPECIAL)))
    )
    val result = runBlocking { underTest.getCurrentChallengeEvent(USER_ID) }
    assertThat(result).isEqualTo(challengeEvent)
  }

  @Test
  fun `SHOULD return current challenge event with special challenges ON getCurrentChallengeEvent WHEN user is on special variation`() {
    challengeEventResolver.mock(
      { getChallengeEvent(userId = USER_ID) },
      challengeEvent.copy(challenges = listOf(challenge, challenge.copy(challengeType = ChallengeType.SPECIAL)))
    )
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES, true) }, Variations.SHOW_SPECIAL_CHALLENGES)
    val result = runBlocking { underTest.getCurrentChallengeEvent(USER_ID) }
    assertThat(result).isEqualTo(challengeEvent.copy(challenges = listOf(challenge, challenge.copy(challengeType = ChallengeType.SPECIAL))))
  }

  @Test
  fun `SHOULD return user challenges ON getUserChallenges`() = runTest {
    challengeEventResolver.mock({ getChallengeEvent(userId = USER_ID) }, challengeEvent)
    val expected = listOf(
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("1"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("2"), challengeType = ChallengeType.SPECIAL)),
    )
    challengeServicePersistenceService.mock({ getUserChallenges(challengeEvent.id, USER_ID) }, expected)
    val result = underTest.getUserChallenges(USER_ID, ChallengeEventId(EVENT_ID))
    assertThat(result).isEqualTo(listOf(userChallenge.copy(challenge = challenge.copy(id = ChallengeId("1")))))
  }

  @Test
  fun `SHOULD return `() = runTest {
    challengeServicePersistenceService.mock({ countClaimedBonusEvent(USER_ID, "bonusId") }, 5)
    val result = underTest.countClaimedBonusEvent(USER_ID, "bonusId")
    assertThat(result).isEqualTo(5)
  }

}