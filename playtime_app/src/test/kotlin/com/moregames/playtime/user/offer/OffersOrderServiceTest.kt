package com.moregames.playtime.user.offer

import assertk.assertThat
import assertk.assertions.containsExactly
import assertk.assertions.isEqualTo
import assertk.assertions.isTrue
import com.moregames.base.junit.MockExtension
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.TimeService
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.games.AndroidGameService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.LimitedTrackingInfo
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService
import com.moregames.playtime.user.pregamescreen.AndroidPreGameScreenService
import com.moregames.playtime.utils.EN_LOCALE
import com.moregames.playtime.utils.androidGameOfferListStub
import com.moregames.playtime.utils.androidGameOfferStub
import com.moregames.playtime.utils.userWithPlatform
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.*
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals

@ExtendWith(MockExtension::class)
class OffersOrderServiceTest(
  private val userPersistenceService: UserPersistenceService,
  private val offerPersistenceService: OfferPersistenceService,
  private val timeService: TimeService,
  private val translationService: UserTranslationService,
  private val androidGameService: AndroidGameService,
  private val androidLockedGamesService: AndroidLockedGamesService,
  private val userService: UserService,
  private val marketService: MarketService,
  private val androidGameOrderExperimentService: AndroidGameOrderExperimentService,
  private val showVideoPreviewExperimentService: ShowVideoPreviewExperimentService,
  private val androidPreGameScreenService: AndroidPreGameScreenService,
) {

  private val service = spy(
    OffersOrderService(
      userPersistenceService = userPersistenceService,
      offerPersistenceService = offerPersistenceService,
      timeService = timeService,
      translationService = translationService,
      androidGameService = androidGameService,
      androidLockedGamesService = androidLockedGamesService,
      userService = userService,
      marketService = marketService,
      androidGameOrderExperimentService = androidGameOrderExperimentService,
      showVideoPreviewExperimentService = showVideoPreviewExperimentService,
      androidPreGameScreenService = androidPreGameScreenService
    )
  )

  private companion object {
    const val USER_ID = "userId"
    val now: Instant = Instant.now()
    val game = AndroidGameOffer(
      id = 1,
      applicationId = "applicationId",
      name = "name",
      activityName = "activityName",
      description = "description",
      iconFilename = "iconFilename",
      imageFilename = "imageFilename",
      orderKey = 1,
      applovinApiKey = "applovinApiKey",
      installImageFilename = "installImageFilename",
      videoPreviewFilename = "videoPreviewFilename",
      infoTextInstallTop = "infoTextInstallTop",
      infoTextInstallBottom = "infoTextInstallBottom",
      expImageFilename = "expImageFilename",
      backGroundColor = "",
      publisherId = 1,
      showForLat = false,
      doNotShow = false,
    )

    val game1 = game.copy(id = 1, orderKey = 1)
    val game2 = game.copy(id = 2, orderKey = 2)

    private val sampleOffer = AdditionalOffer(
      id = 1,
      action = OfferAction.TAPJOY,
      imageFilename = "Zzz_Zzz.jpg",
      rightIcon = "right_icon.svg",
      coinsToEarn = 100500,
      isOneTimeUseOnly = false,
      offerCompletionNotificationDelaySeconds = 0,
      orderKey = 1,
      isDisabled = false,
      title = "title",
      subtext = "substring",
      bodyText = "body text",
      rewardText = "reward text",
    )
  }

  @BeforeEach
  fun before() {
    userPersistenceService.mock({ loadUserCreationDate(USER_ID) }, now)
    timeService.mock({ now() }, now)
    whenever(runBlocking { translationService.tryTranslate(any(), any(), any()) }).then { it.getArgument(0) }
    userService.mock({ getUser(USER_ID) }, userWithPlatform)
    androidLockedGamesService.answer({ getAvailableAndLockedGames(any(), any()) }, {
      val games = (it.arguments[0] as List<*>).filterIsInstance<AndroidGameOffer>()
      AndroidLockedGamesService.GamesAvailableAndLocked(games, emptyList(), null)
    })
    androidGameOrderExperimentService.answer({ applyOrderExperiment(any(), any(), any()) }, { it.arguments[1] as List<*> })
    androidGameOrderExperimentService.answer({ addGameToTheTop(any(), any()) }, { it.arguments[1] as List<*> })
    showVideoPreviewExperimentService.answer({ applyExperiment(any(), any()) }, { (it.arguments[1] as List<*>).filterIsInstance<AndroidGameOffer>() })
    androidPreGameScreenService.answer({ applyExperiment(any(), any()) }, { (it.arguments[1] as List<*>).filterIsInstance<AndroidGameOffer>() })
  }

  @Test
  fun `SHOULD load sort offers by orderKey and dynamic position ON loadGamesOrdered WHEN using pointsForGameWithCoins AND user is not recent`() {
    userPersistenceService.mock({ loadUserCreationDate(USER_ID) }, now.minus(2, ChronoUnit.HOURS))
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))
    val gameCoins = listOf(GamePlayStatusDto(1, 2000, false, now, now)).associateBy { it.gameId }

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual).containsExactly(game2, game1.copy(lastPlayedAt = now))
  }

  @Test
  fun `SHOULD load sort offers by orderKey ignoring dynamic position ON loadGamesOrdered WHEN user is recent`() {
    userPersistenceService.mock({ loadUserCreationDate(USER_ID) }, now.minus(10, ChronoUnit.MINUTES))
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))
    val gameCoins = listOf(GamePlayStatusDto(1, 2000, false, now, now)).associateBy { it.gameId }

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual).containsExactly(game1.copy(lastPlayedAt = now), game2)
  }

  @Test
  fun `SHOULD load sort offers by orderKey and dynamic position ON loadGamesOrdered WHEN using pointsForGameWithRecentCoins AND user is not recent`() {
    userPersistenceService.mock({ loadUserCreationDate(USER_ID) }, now.minus(2, ChronoUnit.HOURS))
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))
    val gameCoins = listOf(
      GamePlayStatusDto(1, 2000, true, now, now),
      GamePlayStatusDto(2, 2000, false, now, now)
    ).associateBy { it.gameId }

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual).containsExactly(game2.copy(lastPlayedAt = now), game1.copy(lastPlayedAt = now))
  }

  @Test
  fun `SHOULD load sort offers by orderKey and dynamic position ON loadGamesOrdered WHEN using pointsForGameWithLowCoins AND user is not recent`() {
    userPersistenceService.mock({ loadUserCreationDate(USER_ID) }, now.minus(2, ChronoUnit.HOURS))
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))
    val gameCoins = listOf(
      GamePlayStatusDto(1, 149, false, now, now),
      GamePlayStatusDto(2, 151, false, now, now)
    ).associateBy { it.gameId }

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual).containsExactly(game2.copy(lastPlayedAt = now), game1.copy(lastPlayedAt = now))
  }

  @Test
  fun `SHOULD load games with showVideoPreview true ON loadGamesOrdered WHEN user is on show video preview experiment`() {
    showVideoPreviewExperimentService.mock(
      { applyExperiment(userId = USER_ID, listOf(game1, game2)) },
      listOf(game1.copy(showVideoPreview = true), game2.copy(showVideoPreview = true))
    )
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, emptyMap(), EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual.all { game -> game.showVideoPreview }).isTrue()
  }

  @Test
  fun `SHOULD apply androidPreGamesScreen experiment ON loadGamesOrdered`() {
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))

    runBlocking {
      service.loadGamesOrdered(USER_ID, emptyMap(), EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    verifyBlocking(androidPreGameScreenService) { applyExperiment(USER_ID, listOf(game1, game2)) }
  }

  @ParameterizedTest
  @CsvSource(",", "untranslatable value,untranslatable value", "translatable value,translated")
  fun `SHOULD translate subtext in additional offers ON loadAdditionalOffersOrdered`(before: String?, after: String?) {
    offerPersistenceService.mock(
      { loadAdditionalOffersForUser("userId") }, listOf(sampleOffer.copy(subtext = before))
    )

    translationService.mock({ tryTranslate("translatable value", EN_LOCALE, "userId") }, "translated")

    runBlocking { service.loadAdditionalOffersOrdered("userId", EN_LOCALE) }
      .also {
        assertThat(it).containsExactly(sampleOffer.copy(subtext = after))
      }
  }

  @ParameterizedTest
  @CsvSource(",", "untranslatable value,untranslatable value", "translatable value,translated")
  fun `SHOULD translate title in additional offers ON loadAdditionalOffersOrdered`(before: String?, after: String?) {
    offerPersistenceService.mock(
      { loadAdditionalOffersForUser("userId") }, listOf(sampleOffer.copy(title = before))
    )

    translationService.mock({ tryTranslate("translatable value", EN_LOCALE, "userId") }, "translated")

    runBlocking { service.loadAdditionalOffersOrdered("userId", EN_LOCALE) }
      .also {
        assertThat(it).containsExactly(sampleOffer.copy(title = after))
      }
  }

  @ParameterizedTest
  @CsvSource(",", "untranslatable value,untranslatable value", "translatable value,translated")
  fun `SHOULD translate bodyText in additional offers ON loadAdditionalOffersOrdered`(before: String?, after: String?) {
    offerPersistenceService.mock(
      { loadAdditionalOffersForUser("userId") }, listOf(sampleOffer.copy(bodyText = before))
    )

    translationService.mock({ tryTranslate("translatable value", EN_LOCALE, "userId") }, "translated")

    runBlocking { service.loadAdditionalOffersOrdered("userId", EN_LOCALE) }
      .also {
        assertThat(it).containsExactly(sampleOffer.copy(bodyText = after))
      }
  }

  @ParameterizedTest
  @CsvSource(",", "untranslatable value,untranslatable value", "translatable value,translated")
  fun `SHOULD translate rewardText in additional offers ON loadAdditionalOffersOrdered`(before: String?, after: String?) {
    offerPersistenceService.mock(
      { loadAdditionalOffersForUser("userId") }, listOf(sampleOffer.copy(rewardText = before))
    )

    translationService.mock({ tryTranslate("translatable value", EN_LOCALE, "userId") }, "translated")

    runBlocking { service.loadAdditionalOffersOrdered("userId", EN_LOCALE) }
      .also {
        assertThat(it).containsExactly(sampleOffer.copy(rewardText = after))
      }
  }

  @Test
  fun `SHOULD NOT translate additional offers ON loadAdditionalOffersOrdered WHEN nothing to translate`() {
    val offer = sampleOffer.copy(title = null, subtext = null, bodyText = null, rewardText = null)

    offerPersistenceService.mock(
      { loadAdditionalOffersForUser("userId") }, listOf(offer)
    )

    runBlocking { service.loadAdditionalOffersOrdered("userId", EN_LOCALE) }
      .also {
        assertThat(it).containsExactly(offer)
      }

    verifyNoInteractions(translationService)
  }

  @Test
  fun `SHOULD mark game as played ON loadGamesOrdered WHEN user has coins for related game`() {
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, listOf(game1, game2))
    val gameCoins = listOf(GamePlayStatusDto(1, 2000, false, now, now)).associateBy { it.gameId }

    val actual = runBlocking {
      service.loadGamesOrdered(USER_ID, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    assertThat(actual).containsExactly(game1.copy(lastPlayedAt = now), game2)
  }

  @Test
  fun `SHOULD load games with unchanged order ON loadGamesOrdered WHEN user is not gamesOrder exp participant`() {
    var startId = 20000
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, defaultGamesList)

    val actual = runBlocking {
      service.loadGamesOrdered(userWithPlatform.id, emptyMap(), EN_LOCALE, loadLatGamesOnly = false).availableGames
    }

    for (i in defaultGamesList.indices) {
      assertThat(actual[i]).isEqualTo(defaultGamesList[i])
    }
  }

  @Test
  fun `SHOULD return data of same size as from AndroidLockedGamesService but data may be sorted`() {
    val games = (1..13).map { androidGameOfferStub.copy(id = it, orderKey = it) }
    val recentUnlockedGame = games[games.size - 1]
    val yetLockedGames = (13..20).map { androidGameOfferStub.copy(id = it, orderKey = it) }
    val gamesAvailableAndLocked = AndroidLockedGamesService.GamesAvailableAndLocked(
      availableGames = games,
      yetLockedGames = yetLockedGames,
      recentUnlockedGame = recentUnlockedGame,
    )

    androidLockedGamesService.mock({ getAvailableAndLockedGames(any(), any()) }, gamesAvailableAndLocked)
    androidGameService.mock({ loadGameOffers(any(), any(), any()) }, games)

    val actual = runBlocking {
      service.loadGamesOrdered(userWithPlatform.id, emptyMap(), EN_LOCALE, loadLatGamesOnly = false)
    }

    assertEquals(gamesAvailableAndLocked.availableGames.size, actual.availableGames.size)
    assertEquals(gamesAvailableAndLocked.yetLockedGames, actual.yetLockedGames)
    assertEquals(gamesAvailableAndLocked.recentUnlockedGame, actual.recentUnlockedGame)
  }

  @Test
  fun `SHOULD return recently unlocked game if something is unlocked`() {
    userService.mock({ getUser(USER_ID) }, userWithPlatform.copy(locale = EN_LOCALE))
    userService.mock({ loadUserGameCoins(USER_ID) }, emptyMap())
    androidGameService.mock({ loadGameOffers(any(), any(), any()) }, emptyList())
    userPersistenceService.mock({ getLimitedTrackingInfo(USER_ID) }, LimitedTrackingInfo(false, "US", true))
    androidLockedGamesService.mock(
      { getAvailableAndLockedGames(any(), any()) },
      AndroidLockedGamesService.GamesAvailableAndLocked(emptyList(), emptyList(), androidGameOfferStub.copy(id = 100))
    )

    val actual = runBlocking {
      service.loadRecentUnlockedGame(userId = USER_ID)
    }

    assertEquals(androidGameOfferStub.copy(id = 100), actual)
  }

  @Test
  fun `SHOUD apply ANRDOID_GAMES_ORDER experiment`() {
    var startId = 20000
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    androidGameService.mock({ loadGameOffers(userWithPlatform, EN_LOCALE, false) }, defaultGamesList)
    val gameCoins = listOf(GamePlayStatusDto(1, 2000, false, now, now)).associateBy { it.gameId }

    runBlocking {
      service.loadGamesOrdered(userWithPlatform.id, gameCoins, EN_LOCALE, loadLatGamesOnly = false).availableGames
    }
    verifyBlocking(androidGameOrderExperimentService) { applyOrderExperiment(userWithPlatform.id, defaultGamesList, gameCoins) }
  }

}
