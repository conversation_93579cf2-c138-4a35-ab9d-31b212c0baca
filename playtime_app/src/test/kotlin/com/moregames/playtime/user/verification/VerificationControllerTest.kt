package com.moregames.playtime.user.verification

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.exceptions.GpsVerificationFailedException
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.util.Constants.ANDROID_APP_VERSION_HEADER
import com.moregames.base.util.Constants.IOS_APP_VERSION_HEADER
import com.moregames.base.util.Constants.VERIFICATION_SESSION_HEADER
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.playtime.app.PlaytimeFeatureFlags
import com.moregames.playtime.ios.dto.IosExaminationEnv
import com.moregames.playtime.ios.examination.dto.IosExaminationChallengeApiDto
import com.moregames.playtime.ios.examination.dto.IosExaminationRequestApiDto
import com.moregames.playtime.user.EmailValidationService
import com.moregames.playtime.user.UserHandleValidationService
import com.moregames.playtime.user.dto.InitiateVerificationRequestDto
import com.moregames.playtime.user.exception.AttestationStatementExaminationFailedException
import com.moregames.playtime.user.verification.dto.*
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus.REQUIRED
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStep
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationType.FACE
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationType.PHONE
import com.moregames.playtime.user.verification.dto.email.EmailValidationRequestApiDto
import com.moregames.playtime.user.verification.dto.userhandle.UserHandleValidationRequestApiDto
import com.moregames.playtime.user.verification.exception.JailBreakUsageException
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.Json
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class VerificationControllerTest {
  private val verificationService: VerificationService = mock()
  private val retryableVerifyFaceService: RetryableVerifyFaceService = mock()
  private val emailValidationService: EmailValidationService = mock()
  private val userHandleValidationService: UserHandleValidationService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock {
    on { boolValue(any(), any()) } doReturn false
  }
  private val applicationConfig: ApplicationConfig = mock()

  private companion object {
    const val userId = "userIdX"
    const val sessionId = "sessionIdX"
    val iosAppVersion = AppVersionDto(AppPlatform.IOS, 142)
  }

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      VerificationController(
        verificationService = verificationService,
        retryableVerifyFaceService = retryableVerifyFaceService,
        emailValidationService = emailValidationService,
        userHandleValidationService = userHandleValidationService,
        featureFlagsFacade = featureFlagsFacade,
        applicationConfig = applicationConfig,
      ).startRouting(this)
    }
  }

  @Test
  fun `SHOULD initiate verification ON verification_initiate call`() = withTestApplication(controller()) {
    val expected = VerificationSessionDto(
      sessionId = sessionId,
      userId = userId,
      expiredAt = Instant.now(),
      verification = listOf(
        VerificationStep(FACE, REQUIRED, 1),
        VerificationStep(PHONE, REQUIRED, 2)
      )
    )
    verificationService.mock({ initiateVerification(userId, userIp = "********", appVersion = iosAppVersion) }, expected)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/initiate?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("X-Forwarded-For", "********,***********")
      addHeader(IOS_APP_VERSION_HEADER, "142")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<VerificationSessionDto>(response.response.content!!)).isEqualTo(expected)
    verifyBlocking(verificationService) { initiateVerification(userId, userIp = "********", appVersion = iosAppVersion) }
  }

  @Test
  fun `SHOULD initiate verification ON verification_initiate call WHEN provider and email are passed`() = withTestApplication(controller()) {
    val testEmail = "<EMAIL>"
    val expected = VerificationSessionDto(
      sessionId = sessionId,
      userId = userId,
      expiredAt = Instant.now(),
      verification = listOf(
        VerificationStep(FACE, REQUIRED, 1),
        VerificationStep(PHONE, REQUIRED, 2)
      )
    )
    val provider = PaymentProviderType.AMAZON
    verificationService.mock({ initiateVerification(userId, provider, testEmail, userIp = null, appVersion = iosAppVersion) }, expected)
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/initiate?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "142")
      setBody(Json.defaultJsonConverter.encodeToString(InitiateVerificationRequestDto(provider = provider, email = testEmail)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<VerificationSessionDto>(response.response.content!!)).isEqualTo(expected)
    verifyBlocking(verificationService) { initiateVerification(userId, provider, testEmail, userIp = null, appVersion = iosAppVersion) }
  }

  @Test
  fun `SHOULD initiate verification ON verification_initiate call WHEN body is not passed`() = withTestApplication(controller()) {
    val expected = VerificationSessionDto(
      sessionId = sessionId,
      userId = userId,
      expiredAt = Instant.now(),
      verification = listOf(
        VerificationStep(FACE, REQUIRED, 1),
        VerificationStep(PHONE, REQUIRED, 2)
      )
    )

    verificationService.mock({ initiateVerification(userId, provider = null, email = null, userIp = null, appVersion = iosAppVersion) }, expected)
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/initiate?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "142")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<VerificationSessionDto>(response.response.content!!)).isEqualTo(expected)
    verifyBlocking(verificationService) { initiateVerification(userId, provider = null, email = null, userIp = null, appVersion = iosAppVersion) }
  }

  @Test
  fun `SHOULD initiate verification ON verification_initiate call WHEN cashout is donation`() = withTestApplication(controller()) {
    val testEmail = "<EMAIL>"
    val expected = VerificationSessionDto(
      sessionId = sessionId,
      userId = userId,
      expiredAt = Instant.now(),
      verification = emptyList()
    )
    val provider = PaymentProviderType.DOCTORS_WITHOUT_BORDERS
    verificationService.mock({ initiateVerification(userId, provider, testEmail, userIp = null, appVersion = iosAppVersion) }, expected)
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/initiate?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "142")
      setBody(Json.defaultJsonConverter.encodeToString(InitiateVerificationRequestDto(provider = provider, email = testEmail)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<VerificationSessionDto>(response.response.content!!)).isEqualTo(expected)
    verifyBlocking(verificationService) { initiateVerification(userId, provider, testEmail, userIp = null, appVersion = iosAppVersion) }
  }

  @Test
  fun `SHOULD verify face ON verification_verify-face call WHEN FF is enabled`() = withTestApplication(controller()) {
    whenever(featureFlagsFacade.boolValue(PlaytimeFeatureFlags.FACE_VERIFICATION_RETRIES, false)) doReturn true
    val expected = VerifyFaceApiResponseDto(
      scanResultBlob = "scanResultBlob"
    )
    val userAgent = "userAgent"
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    retryableVerifyFaceService.mock({ verifyFace(sessionId, userAgent = userAgent, request = request) }, VerifyFaceResult.Success(expected))

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/verify-face?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("User-Agent", userAgent)
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo("{\"scanResultBlob\":\"scanResultBlob\"}")
    verifyBlocking(retryableVerifyFaceService) { verifyFace(sessionId, userAgent, request) }
  }

  @Test
  fun `SHOULD return empty scanResultBlob ON verify-face call WHEN there are problems with Facetec AND FF enabled`() = withTestApplication(controller()) {
    whenever(featureFlagsFacade.boolValue(PlaytimeFeatureFlags.FACE_VERIFICATION_RETRIES, false)) doReturn true
    val userAgent = "userAgent"
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    retryableVerifyFaceService.mock({ verifyFace(sessionId, userAgent = userAgent, request = request) }, VerifyFaceResult.Failure)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/verify-face?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("User-Agent", userAgent)
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo("{}")
    verifyBlocking(retryableVerifyFaceService) { verifyFace(sessionId, userAgent, request) }
  }

  @Test
  fun `SHOULD verify face ON verification_verify-face call WHEN FF is disabled`() = withTestApplication(controller()) {
    val expected = VerifyFaceApiResponseDto(
      scanResultBlob = "scanResultBlob"
    )
    val userAgent = "userAgent"
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationService.mock({ verifyFace(sessionId, userAgent = userAgent, request = request) }, expected)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/verify-face?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("User-Agent", userAgent)
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo("{\"scanResultBlob\":\"scanResultBlob\"}")
    verifyBlocking(verificationService) { verifyFace(sessionId, userAgent, request) }
  }

  @Test
  fun `SHOULD return empty scanResultBlob ON verify-face call WHEN there are problems with Facetec AND FF disabled`() = withTestApplication(controller()) {
    val userAgent = "userAgent"
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationService.mock({ verifyFace(sessionId, userAgent = userAgent, request = request) }, VerifyFaceApiResponseDto(null))

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/verify-face?userId=${userId}"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("User-Agent", userAgent)
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo("{}")
    verifyBlocking(verificationService) { verifyFace(sessionId, userAgent, request) }
  }

  @Test
  fun `SHOULD start examination ON GET verification_examination call`() = withTestApplication(controller()) {
    verificationService.mock({ generateIosChallenge(sessionId) }, "challenge1")

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/verification/examination"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<IosExaminationChallengeApiDto>(response.response.content!!).challenge).isEqualTo("challenge1")
    verifyBlocking(verificationService, times(1)) { generateIosChallenge(sessionId) }
  }

  @Test
  fun `SHOULD call examination ON POST verification_examination call`() = withTestApplication(controller()) {
    val request = IosExaminationRequestApiDto(
      keyIdBase64 = "key id",
      attestationObjectBase64 = "attestation object",
      challenge = "challenge"
    )

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/examination"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("X-iOS-Env", "SANDBOX")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService, times(1)) { examineIosDevice(sessionId, request, IosExaminationEnv.SANDBOX_JP) }
  }

  @Test
  fun `SHOULD call prod examination ON POST verification_examination call WHEN prod call`() = withTestApplication(controller()) {
    val request = IosExaminationRequestApiDto(
      keyIdBase64 = "key id",
      attestationObjectBase64 = "attestation object",
      challenge = "challenge"
    )

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/examination"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader("X-iOS-Env", "PROD")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService, times(1)) { examineIosDevice(sessionId, request, IosExaminationEnv.PROD_JP) }
  }

  @Test
  fun `SHOULD throw examination fail exception ON POST verification_examination call WHEN verification failed`() = withTestApplication(controller()) {
    val request = IosExaminationRequestApiDto(
      keyIdBase64 = "key id",
      attestationObjectBase64 = "attestation object",
      challenge = "challenge"
    )
    verificationService.throwException(
      { examineIosDevice(sessionId, request, IosExaminationEnv.SANDBOX_JP) },
      AttestationStatementExaminationFailedException(userId)
    )

    assertFailsWith(AttestationStatementExaminationFailedException::class) {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/examination"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader("X-iOS-Env", "SANDBOX")
        addHeader(VERIFICATION_SESSION_HEADER, sessionId)
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }

    verifyBlocking(verificationService, times(1)) { examineIosDevice(sessionId, request, IosExaminationEnv.SANDBOX_JP) }
  }

  @Test
  fun `SHOULD throw exception ON POST verification_examination call WHEN app version header is missing`() = withTestApplication(controller()) {
    val request = IosExaminationRequestApiDto(
      keyIdBase64 = "key id",
      attestationObjectBase64 = "attestation object",
      challenge = "challenge"
    )

    assertFailsWith(IllegalStateException::class) {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/examination"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader(VERIFICATION_SESSION_HEADER, sessionId)
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }.let { exception ->
      assertThat(exception.message).isEqualTo("Unknown app Env: 'null'")
    }
  }

  @Test
  fun `SHOULD throw exception ON POST verification_examination call WHEN app version header is wrong`() = withTestApplication(controller()) {
    val request = IosExaminationRequestApiDto(
      keyIdBase64 = "key id",
      attestationObjectBase64 = "attestation object",
      challenge = "challenge"
    )

    assertFailsWith(IllegalStateException::class) {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/examination"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader("X-iOS-Env", "WRONG")
        addHeader(VERIFICATION_SESSION_HEADER, sessionId)
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }.let { exception ->
      assertThat(exception.message).isEqualTo("Unknown app Env: 'WRONG'")
    }
  }

  @Test
  fun `SHOULD call jailBreak check ON POST verification_jailBreak call`() = withTestApplication(controller()) {
    val request = JailBreakRequestApiDto(false)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/jailBreak"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService, times(1)) { verifyIosJailBreak(sessionId, request) }
  }

  @Test
  fun `SHOULD throw jailBreak fail exception ON POST verification_jailBreak call WHEN jailBreak usage detected`() = withTestApplication(controller()) {
    val request = JailBreakRequestApiDto(true)
    verificationService.throwException({ verifyIosJailBreak(sessionId, request) }, JailBreakUsageException(userId))

    assertFailsWith<JailBreakUsageException> {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/jailBreak"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader(VERIFICATION_SESSION_HEADER, sessionId)
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }

    verifyBlocking(verificationService, times(1)) { verifyIosJailBreak(sessionId, request) }
  }

  @Test
  fun `SHOULD call gps location check ON POST verification_location call`() = withTestApplication(controller()) {
    val request = GpsLocationRequestApiDto(location = "DE", isMocked = false)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/location"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      addHeader(ANDROID_APP_VERSION_HEADER, "42")
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService, times(1)) { verifyGpsLocation(sessionId, request, AppPlatform.ANDROID) }
  }

  @Test
  fun `SHOULD call gps location check ON POST verification_location call WHEN ios application`() = withTestApplication(controller()) {
    val request = GpsLocationRequestApiDto(location = "DE", isMocked = null)

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/location"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(VERIFICATION_SESSION_HEADER, sessionId)
      addHeader(IOS_APP_VERSION_HEADER, "51")
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService, times(1)) { verifyGpsLocation(sessionId, request, AppPlatform.IOS) }
  }

  @Test
  fun `SHOULD throw gps location exception ON POST verification_location call WHEN verification failed`() = withTestApplication(controller()) {
    val request = GpsLocationRequestApiDto(location = "KZ", isMocked = true)
    verificationService.throwException({ verifyGpsLocation(sessionId, request, AppPlatform.ANDROID) }, GpsVerificationFailedException())

    assertFailsWith<GpsVerificationFailedException> {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/location"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader(VERIFICATION_SESSION_HEADER, sessionId)
        addHeader(ANDROID_APP_VERSION_HEADER, "42")
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }

    verifyBlocking(verificationService, times(1)) { verifyGpsLocation(sessionId, request, AppPlatform.ANDROID) }
  }

  @Test
  fun `SHOULD throw ParameterRequiredException exception ON POST validateEmail call WHEN email is missing`() = withTestApplication(controller()) {
    val request = EmailValidationRequestApiDto("")

    assertFailsWith<ParameterRequiredException> {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/validateEmail"
      ) {
        addHeader("Content-Type", "application/json")
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }.let {
      assertEquals("Required parameter 'email' not found", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
    verifyNoInteractions(emailValidationService)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD validate email ON POST validateEmail call`(validationResult: Boolean) = withTestApplication(controller()) {
    val request = EmailValidationRequestApiDto("<EMAIL>")
    emailValidationService.mock({ isRawEmailValid("<EMAIL>") }, validationResult)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/validateEmail"
    ) {
      addHeader("Content-Type", "application/json")
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }.let {
      assertEquals("""{"isEmailValid":$validationResult}""", it.response.content)
    }
  }

  @Test
  fun `SHOULD throw ParameterRequiredException exception ON POST validateUserHandle call WHEN userHandle is missing`() = withTestApplication(controller()) {
    val request = UserHandleValidationRequestApiDto("")

    assertFailsWith<ParameterRequiredException> {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/verification/validateUserHandle"
      ) {
        addHeader("Content-Type", "application/json")
        setBody(Json.defaultJsonConverter.encodeToString(request))
      }
    }.let {
      assertEquals("Required parameter 'userHandle' not found", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
    verifyNoInteractions(emailValidationService)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD validate user handle ON POST validateUserHandle call`(validationResult: Boolean) = withTestApplication(controller()) {
    val request = UserHandleValidationRequestApiDto("moneyMaker")
    userHandleValidationService.mock({ isUserHandleValid("moneyMaker") }, validationResult)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/verification/validateUserHandle"
    ) {
      addHeader("Content-Type", "application/json")
      setBody(Json.defaultJsonConverter.encodeToString(request))
    }.let {
      assertEquals("""{"isUserHandleValid":$validationResult}""", it.response.content)
    }
  }

}
