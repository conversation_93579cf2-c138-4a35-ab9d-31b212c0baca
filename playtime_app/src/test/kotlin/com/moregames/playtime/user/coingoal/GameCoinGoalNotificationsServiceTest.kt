package com.moregames.playtime.user.coingoal

import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.util.minus
import com.moregames.playtime.util.plus
import com.moregames.playtime.utils.user
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.time.Instant
import kotlin.time.Duration.Companion.hours

class GameCoinGoalNotificationsServiceTest {
  private val now: Instant = Instant.now()
  private val periodEnd = now + 1.hours
  private val gameCoinGoalService = mock<GameCoinGoalsService>()
  private val userService = mock<UserService> {
    onBlocking { userExists(user.userId) } doReturn true
  }
  private val delayedMessagePublisher = mock<DelayedMessagePublisher>()
  private val timeService = mock<TimeService> {
    on { now() } doReturn now
  }
  private val messageBus: MessageBus = mock()
  private val rewardingFacade: RewardingFacade = mock()

  private val underTest = GameCoinGoalNotificationsService(gameCoinGoalService, userService, delayedMessagePublisher, timeService, rewardingFacade, messageBus)

  @Nested
  inner class GameGoal {
    @Test
    fun `SHOULD not notify IF coin goal is not reached`() {
      gameCoinGoalService.mock(
        { findActiveCoinGoal(user.userId) },
        CoinGoal.Game(setId = "setId", goalId = "1", coinGoal = 100, coinGoalBalance = 50.toBigDecimal(), gameId = 1, notified = false)
      )

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyNoInteractions(messageBus)
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyNoMoreInteractions(gameCoinGoalService)
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }

    @Test
    fun `SHOULD notify IF coin goal is reached`() {
      gameCoinGoalService.mock(
        { findActiveCoinGoal(user.userId) },
        CoinGoal.Game(setId = "setId", goalId = "1", coinGoal = 100, coinGoalBalance = 101.toBigDecimal(), gameId = 1, notified = false)
      )
      val userDto = userDtoStub.copy(id = user.userId)
      userService.mock({ getUser(user.userId) }, userDto)

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyBlocking(messageBus) { publishAsync(any<PushNotificationEffect>()) }
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyBlocking(gameCoinGoalService) { markAsNotified("1") }
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }

    @Test
    fun `SHOULD not notify IF coin goal is reached and already notified`() {
      gameCoinGoalService.mock(
        { findActiveCoinGoal(user.userId) },
        CoinGoal.Game(setId = "setId", goalId = "1", coinGoal = 100, coinGoalBalance = 101.toBigDecimal(), gameId = 1, notified = true)
      )

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyNoInteractions(messageBus)
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyNoMoreInteractions(gameCoinGoalService)
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }
  }

  @Nested
  inner class GeneralGoal {
    @Test
    fun `SHOULD not notify IF generic goal is not reached`() {
      gameCoinGoalService.mock({ findActiveCoinGoal(user.userId) }, CoinGoal.General(setId = "setId", goalId = "1", coinGoal = 100, notified = false))
      rewardingFacade.mock({ loadUninflatedGoalCoins(user.userId) }, 50)

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyNoInteractions(messageBus)
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyNoMoreInteractions(gameCoinGoalService)
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }

    @Test
    fun `SHOULD not notify IF generic goal is reached and already notified`() {
      gameCoinGoalService.mock({ findActiveCoinGoal(user.userId) }, CoinGoal.General(setId = "setId", goalId = "1", coinGoal = 100, notified = true))
      rewardingFacade.mock({ loadUninflatedGoalCoins(user.userId) }, 101)

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyNoInteractions(messageBus)
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyNoMoreInteractions(gameCoinGoalService)
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }

    @Test
    fun `SHOULD notify IF generic goal is reached`() {
      gameCoinGoalService.mock({ findActiveCoinGoal(user.userId) }, CoinGoal.General(setId = "setId", goalId = "1", coinGoal = 100, notified = false))
      rewardingFacade.mock({ loadUninflatedGoalCoins(user.userId) }, 101)
      val userDto = userDtoStub.copy(id = user.userId)
      userService.mock({ getUser(user.userId) }, userDto)

      runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

      verifyBlocking(messageBus) { publishAsync(any<PushNotificationEffect>()) }
      verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
      verifyBlocking(gameCoinGoalService) { markAsNotified("1") }
      verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
    }
  }

  @Test
  fun `SHOULD just continue chain WHEN cooldown`() {
    gameCoinGoalService.mock({ findActiveCoinGoal(user.userId) }, CoinGoal.CoolDown(nextGoalTimestamp = Instant.now().plusSeconds(10)))

    runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

    verifyNoInteractions(messageBus)
    verifyBlocking(gameCoinGoalService) { findActiveCoinGoal(user.userId) }
    verifyNoMoreInteractions(gameCoinGoalService)
    verifyBlocking(delayedMessagePublisher) { publish(GameCoinGoalDelayedNotification(user.userId, periodEnd), GameCoinGoalNotificationsService.DELAY) }
  }

  @Test
  fun `SHOULD break chain WHEN user is deleted`() {
    userService.mock({ userExists(user.userId) }, false)

    runBlocking { underTest.notifyUserIfCompleted(user.userId, periodEnd) }

    verifyNoInteractions(messageBus)
    verifyNoInteractions(gameCoinGoalService)
    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD break chain WHEN CP ended`() {
    gameCoinGoalService.mock({ findActiveCoinGoal(user.userId) }, CoinGoal.CoolDown(nextGoalTimestamp = Instant.now().plusSeconds(10)))

    runBlocking { underTest.notifyUserIfCompleted(user.userId, now - 1.hours) }

    verifyNoInteractions(messageBus)
    verifyNoInteractions(gameCoinGoalService)
    verifyNoInteractions(delayedMessagePublisher)
  }
}