package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.challenge.dto.ChallengeId
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeResponseApiDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimBoxType
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventResponseApiDto
import com.moregames.playtime.user.challenge.dto.claim.offer.ClaimChallengeSpecialOfferResponseApiDto
import com.moregames.playtime.user.challenge.dto.claim.pot.ClaimPotResponseApiDto
import com.moregames.playtime.user.challenge.dto.config.*
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.EN_LOCALE
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.routing.*
import io.ktor.server.testing.*
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.util.*

@ExtendWith(MockExtension::class)
class ChallengeEventControllerTest(
  private val challengeEventConfigService: ChallengeEventConfigService,
  private val claimChallengeEventService: ClaimChallengeEventService,
  private val claimSpecialChallengePotService: ClaimSpecialChallengePotService,
  private val claimChallengeService: ClaimChallengeService,
  private val claimChallengeSpecialOfferService: ClaimChallengeSpecialOfferService,
) {

  companion object {
    const val USER_ID = "userId"
  }

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      ChallengeEventController(
        challengeEventConfigService = challengeEventConfigService,
        claimChallengeEventService = claimChallengeEventService,
        claimChallengeService = claimChallengeService,
        claimSpecialChallengePotService = claimSpecialChallengePotService,
        claimChallengeSpecialOfferService = claimChallengeSpecialOfferService,
      ).startRouting(this)
    }
  }

  @Test
  fun `SHOULD claim special pot`() = withTestApplication(controller()) {
    claimSpecialChallengePotService.mock(
      { claimPot(USER_ID) },
      ClaimPotResponseApiDto(
        amountString = "$2.50",
      )
    )

    handleRequest(
      method = HttpMethod.Post,
      uri = "/challenges/claim-special-pot?userId=$USER_ID",
    ) {
      addHeader("Content-Type", "application/json")
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      //language=json
      assertJsonEquals(
        """
        {
          "amountString": "$2.50"
        }
        """, call.response.content
      )
    }
  }

  @Test
  fun `SHOULD claim event`() = withTestApplication(controller()) {
    claimChallengeEventService.mock(
      { claimEvent(USER_ID, ClaimEventRequestApiDto(ChallengeEventId("13"))) },
      ClaimEventResponseApiDto(
        amountString = "$2.50",
        boxType = ClaimBoxType.GOLDEN,
      )
    )

    handleRequest(
      method = HttpMethod.Post,
      uri = "/challenges/claim-event?userId=$USER_ID",
    ) {
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(
        """
        {
          "challengeEventId": "13"
        }
      """
      )
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      //language=json
      assertJsonEquals(
        """
        {
          "amountString": "$2.50",
          "boxType": "GOLDEN"
        }
        """, call.response.content
      )
    }
  }

  @Test
  fun `SHOULD claim challenge`() = withTestApplication(controller()) {
    claimChallengeService.mock(
      { claimChallenge(USER_ID, ClaimChallengeRequestApiDto(ChallengeId("13")), Locale.ENGLISH) },
      ClaimChallengeResponseApiDto(
        coins = 2050,
        text = "You've won a golden ticket",
        errorMessage = "No errors"
      )
    )

    handleRequest(
      method = HttpMethod.Post,
      uri = "/challenges/claim-challenge?userId=$USER_ID",
    ) {
      addHeader("Content-Type", "application/json")
      //language=json
      setBody(
        """
        {
          "challengeId": "13"
        }
      """
      )
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      //language=json
      assertJsonEquals(
        """
        {
          "coins": 2050,
          "text": "You've won a golden ticket",
          "errorMessage": "No errors"
        }
        """, call.response.content
      )
    }
  }

  @Test
  fun `SHOULD return empty config WHEN service returns empty one`() = withTestApplication(controller()) {
    challengeEventConfigService.mock({ getChallengeEvent(USER_ID, EN_LOCALE) }, EmptyChallengeEventConfigApiDto)

    handleRequest(
      method = HttpMethod.Get,
      uri = "/challenges/event-configuration?userId=$USER_ID",

      ) {
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      assertJsonEquals("{}", call.response.content)
    }
  }

  @Test
  fun `SHOULD respect user locale ON event-configuration call`() = withTestApplication(controller()) {
    val userLocale = Locale.forLanguageTag("fr-CA")

    challengeEventConfigService.mock({ getChallengeEvent(USER_ID, userLocale) }, EmptyChallengeEventConfigApiDto)

    handleRequest(
      method = HttpMethod.Get,
      uri = "/challenges/event-configuration?userId=$USER_ID",
    ) {
      addHeader(HttpHeaders.AcceptLanguage, "fr-CA")
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      assertJsonEquals("{}", call.response.content)
    }
  }

  @Test
  fun `SHOULD return challenge config WHEN there is a challenge`() = withTestApplication(controller()) {
    val expected = javaClass.getResource("/user/challenge/challenge-event-controller-config.json")!!.readText()

    val expectedDto = ChallengeEventConfigApiDto(
      startTime = 1734408812,
      endTime = 1734408898,
      challengesUpdateTime = 1734408814,
      timestamp = 1734408813,
      challengesUpdateText = "New challenges on New Year!",
      challengeEventId = ChallengeEventId("13"),
      claimWidget = ClaimWidgetApiDto(
        bannerColor = "#FF0000",
        bannerEndColor = "#FF0000",
        textColor = "#FFFFFF",
        claimButtonText = "Claim up to 25$ CASH Reward!",
        headerText = "Golden Tickets Collected: ",
        progressBarSubtext = "Collect All Golden Tickets to Claim your Cash Bonus",
        eventRewardClaimed = false,
        aheadButtonText = "abt",
      ),
      tutorialSteps = listOf("welcome", "cashBonus"),
      challenges = listOf(
        ChallengeApiDto(
          challengeId = ChallengeId("1"),
          title = "Get 100 correct questions in Trivia Madness",
          icon = "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg",
          progressMax = 100,
          progressCurrent = 5,
          rewardClaimed = false,
          gameInfo = ChallengeOfferApiDto(
            id = "200056",
            applicationId = "com.gimica.triviamadness",
            activityName = "com.unity3d.player.UnityPlayerActivity",
            title = "Trivia Madness",
            iconUrl = "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg",
            imageUrl = "https://storage.googleapis.com/public-playtime/images/merge_blast_preview.jpg",
            isEnabled = true,
            installImageUrl = "https://storage.googleapis.com/public-playtime/images/merge_blast_install.jpg",
            infoTextInstallTop = "install top",
            infoTextInstallBottom = "install bottom",
            showInstallImage = true,
            installationLink = "https://play.google.com/store/apps/details?id=com.gimica.mergeblast&listing=justplay",
          ),
          challengeType = ChallengeType.REGULAR,
        )
      ),
      specialChallengePotState = SpecialChallengePotState.IN_PROGRESS,
      specialChallengePotProgressCurrent = 53,
      specialChallengePotProgressMax = 100,
      specialChallengeWidgets = SpecialChallengeWidgetsDto(
        mainScreen = listOf(
          SpecialChallengeWidgetsDto.MenuItemDto(
            challengeType = ChallengeType.REGULAR,
            title = "Regular challenges",
            imageUrl = "image.png"
          ),
          SpecialChallengeWidgetsDto.MenuItemDto(
            challengeType = ChallengeType.SPECIAL,
            title = "Regular challenges",
            imageUrl = "regular-image.png"
          ),
        ),
        specialChallengeScreen = SpecialChallengeWidgetsDto.SpecialChallengeScreenDto("treasure-url.png"),
        claimWidget = SpecialChallengeWidgetsDto.ClaimWidgetDto(
          title = "You hit the JackPot!",
          description = "Keep going! Complete more Treasure Quests for you new epicReward!",
          image = "claim-image.png",
        ),

      ),
      tutorialWidget = TutorialWidgetDto(
        backgroundColor = "#FFFFFF",
        highlightStartColor = "#FFFFFA",
        highlightEndColor = "#FFFFFB",
        tcUrl = "https://example.com",
        tutorialId = "tutorialId",
      ),
      bonusTracker = BonusTrackerApiDto(
        bonusId = "bonusId",
        progressMax = 10,
        completeText = "You won super bonus!",
        progressCurrent = 5,
      ),
    )

    challengeEventConfigService.mock({ getChallengeEvent(USER_ID, EN_LOCALE) }, expectedDto)

    handleRequest(
      method = HttpMethod.Get,
      uri = "/challenges/event-configuration?userId=$USER_ID"
    ).let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      assertJsonEquals(expected, call.response.content)
    }
  }

  @Test
  fun `SHOULD claim special offer`() = withTestApplication(controller()) {
    claimChallengeSpecialOfferService.mock({ claimSpecialOffer(USER_ID) },
      ClaimChallengeSpecialOfferResponseApiDto(
        errorMessage = "No errors"
      )
    )

    handleRequest(
      method = HttpMethod.Post,
      uri = "/challenges/claim-special-offer?userId=$USER_ID",
    ).let { call ->
      assertThat(call.response.status()).isEqualTo(OK)
      //language=json
      assertJsonEquals(
        """
        {
          "errorMessage": "No errors"
        }
        """, call.response.content
      )
    }
  }
}