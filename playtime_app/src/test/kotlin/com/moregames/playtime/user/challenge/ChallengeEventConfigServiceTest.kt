package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.PackagePrivate
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.general.exception.InvalidParameterValueException
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventsAdminApiDto
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ChallengeEventConfigApiDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.EmptyChallengeEventConfigApiDto
import com.moregames.playtime.user.challenge.dto.config.IChallengeEventConfigApiDto
import com.moregames.playtime.user.challenge.dto.config.SpecialChallengeWidgetsDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import com.moregames.playtime.user.offer.AndroidInstallationLinkProvider
import com.moregames.playtime.user.promotion.event.manager.PromotionEventConfigResolverTest
import com.moregames.playtime.utils.FR_LOCALE
import com.moregames.playtime.utils.Json.defaultJsonConverter
import com.moregames.playtime.utils.androidGameOfferStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Locale

class ChallengeEventConfigServiceTest {
  companion object {
    const val USER_ID = "userId"
    const val IMAGE_URL_FORMAT = "https://example.com/%s"
    private val userLocale = FR_LOCALE
    val CHALLENGE_EVENT = ChallengeEvent(
      id = ChallengeEventId("16"),
      dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
      dateTo = Instant.parse("2024-11-22T23:59:00.000Z"),
      cfg = "{}",
      challenges = emptyList(),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    val now: Instant = Instant.parse("2024-11-22T12:00:01.000Z")
    val userDto = userDtoStub.copy(id = PromotionEventConfigResolverTest.Companion.USER_ID, createdAt = now.minus(3, ChronoUnit.DAYS))
    val dateFrom: Instant = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo: Instant = Instant.parse("2025-12-05T10:15:30.00Z")
    val challengeEventAdminApiDto = ChallengeEventAdminApiDto(
      id = "eventId1",
      dateFrom = dateFrom,
      dateTo = dateTo,
      challengesUpdateText = "some challenge update text",
      claimWidget = ClaimWidgetDto(
        bannerColor = "red",
        bannerEndColor = "green",
        textColor = "white",
        claimButtonText = "Ok",
        headerText = "Some header",
        progressBarSubtext = "Some sub progress",
        aheadButtonText = "abt",
      ),
      tutorialSteps = listOf("welcome", "cash_bonus"),
      challenges = listOf(
        ChallengeAdminApiDto(
          id = "id1",
          title = "title1",
          icon = "icon1",
          progressMax = 100,
          gameApplicationId = "gameApplicationId1",
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        ),
        ChallengeAdminApiDto(
          id = "id2",
          title = "title2",
          icon = "icon2",
          progressMax = 100,
          gameApplicationId = "gameApplicationId2",
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        )
      ),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      tutorialWidget = TutorialWidgetDto(
        backgroundColor = "#FFFFFF",
        highlightStartColor = "#FFFFFA",
        highlightEndColor = "#FFFFFB",
        tcUrl = "https://example.com",
        tutorialId = "tutorialId",
      ),
      bonusTracker = BonusTrackerDto(
        bonusId = "bonusId",
        progressMax = 10,
        completeText = "You won super bonus!"
      ),
    )
    val eventId = ChallengeEventId("eventId1")
    val challengeEvent = ChallengeEvent(
      id = eventId,
      dateFrom = dateFrom,
      dateTo = dateTo,
      //language=json
      cfg = """{
        "challengesUpdateText":"some challenge update text",
        "claimWidget":{"bannerColor":"red","bannerEndColor":"green","textColor":"white","headerText":"Some header","progressBarSubtext":"Some sub progress","claimButtonText":"Ok","aheadButtonText":"abt"},
        "tutorialSteps":["welcome","cash_bonus"],
        "tutorialWidget":{"backgroundColor":"#FFFFFF","highlightStartColor":"#FFFFFA","highlightEndColor":"#FFFFFB","tcUrl":"https://example.com","tutorialId":"tutorialId"},
        "bonusTracker":{"bonusId":"bonusId","progressMax":10,"completeText":"You won super bonus!"}
       }
        """
        .lines()
        .joinToString("")
        { it.trim() },
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
      challenges = listOf(
        Challenge(
          id = ChallengeId("id1"),
          eventId = eventId,
          title = "title1",
          icon = "icon1",
          progressMax = 100,
          gameId = 200100,
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        ), Challenge(
          id = ChallengeId("id2"),
          eventId = eventId,
          title = "title2",
          icon = "icon2",
          progressMax = 100,
          gameId = 200200,
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          order = 1,
          goal = 100,
          applyEarningsCut = true,
          challengeType = ChallengeType.REGULAR,
        )
      )
    )
  }

  private val json: Json = defaultJsonConverter
  private val timeService: TimeService = mock()
  private val abTestingService: AbTestingService = mock()
  private val gamePersistenceService: GamePersistenceService = mock()
  private val imageService: ImageService = mock {
    whenever(it.toUrl(any())).then { invocation -> IMAGE_URL_FORMAT.format(invocation.getArgument(0) as String) }
  }
  private val androidInstallationLinkProvider: AndroidInstallationLinkProvider = mock()
  private val userTranslationService: UserTranslationService = mock()
  private val userService: UserService = mock()
  private val challengeService: ChallengeService = mock()
  private val challengeConfigValidator: ChallengeConfigValidator = mock()
  private val specialChallengePotService: SpecialChallengePotService = mock()

  private val underTest = ChallengeEventConfigService(
    timeService = timeService,
    json = defaultJsonConverter,
    gamePersistenceService = gamePersistenceService,
    imageService = imageService,
    androidInstallationLinkProvider = androidInstallationLinkProvider,
    userTranslationService = userTranslationService,
    challengeService = challengeService,
    challengeConfigValidator = challengeConfigValidator,
    specialChallengePotService = specialChallengePotService,
  )

  @BeforeEach
  fun init() {
    timeService.mock({ now() }, now)
    userService.mock({ getUser(USER_ID, true) }, userDto.copy(createdAt = now.minus(10, ChronoUnit.DAYS)))
    whenever(runBlocking { userTranslationService.tryTranslate(any(), eq(FR_LOCALE), any()) }).then { "[fr translated] " + it.getArgument(0) }
    whenever(runBlocking { userTranslationService.translateOrDefault(any(), eq(FR_LOCALE), any()) }).then { "[fr translated] " +
       (it.getArgument(0) as TranslationResource).defaultValue }
    challengeService.mock( {countClaimedBonusEvent(USER_ID, "bonusId")}, 5)
  }

  @Test
  fun `SHOULD return specialPotWidgets ON createSpecialChallengeWidgets`() = runTest {
    val userSpecialPot = createUserSpecialChallengePot()
    val result = underTest.createSpecialChallengeWidgets(userSpecialPot, FR_LOCALE)
    assertThat(result).isEqualTo(
      SpecialChallengeWidgetsDto(
        mainScreen = listOf(
          SpecialChallengeWidgetsDto.MenuItemDto(
            challengeType = ChallengeType.REGULAR,
            imageUrl = "https://example.com/regular-image.png",
            title = "[fr translated] Regular challenges",
          ),
          SpecialChallengeWidgetsDto.MenuItemDto(
            challengeType = ChallengeType.SPECIAL,
            imageUrl = "https://example.com/special-image.png",
            title = "[fr translated] Special challenges",
          )
        ),
        specialChallengeScreen = SpecialChallengeWidgetsDto.SpecialChallengeScreenDto(
          treasureImageUrl = "https://example.com/treasure-url.png"
        ),
        claimWidget = SpecialChallengeWidgetsDto.ClaimWidgetDto(
          title = "[fr translated] You hit the JackPot!",
          description =  "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
          image =  "https://example.com/claim-image.png"
        )
      )
    )
  }

  @Test
  fun `SHOULD return null ON createSpecialChallengeWidgets WHEN userSpecialPot has not parseable config`() = runTest {
    val userSpecialPot = createUserSpecialChallengePot()
    val unparseablePot = userSpecialPot.copy(potConfig = userSpecialPot.potConfig.copy(uiConfig = "{fjkk}"))
    val result = underTest.createSpecialChallengeWidgets(unparseablePot, Locale.GERMAN)
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return null ON createSpecialChallengeWidgets WHEN userSpecialPot is null`() = runTest {
    val result = underTest.createSpecialChallengeWidgets(null, Locale.GERMAN)
    assertThat(result).isNull()
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return empty config WHEN user is not ANDROID_CHALLENGES experiment participant`() {
    val challengeEvent = ChallengeEvent(
      id = ChallengeEventId("17"),
      dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
      dateTo = Instant.parse("2024-11-22T23:59:00.000Z"),
      cfg = "{}",
      eventType = ChallengeEventType.GLOBAL,
      enabled = true,
      challenges = emptyList(),
      bonusId = "bonusId",
    )
    challengeService.mock({ getCurrentChallengeEvent(any()) }, challengeEvent)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, DEFAULT)
    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }
    assertThat(result).isEqualTo(EmptyChallengeEventConfigApiDto)
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return empty config WHEN there are no events in DB`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    challengeService.mock({ getCurrentChallengeEvent(any()) }, null)
    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }
    assertThat(result).isEqualTo(EmptyChallengeEventConfigApiDto)
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return empty config WHEN there are events in DB but cfg is wrong json`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    challengeService.mock(
      { getCurrentChallengeEvent(any()) },
      ChallengeEvent(
        id = ChallengeEventId("15"),
        dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
        dateTo = Instant.parse("2024-11-22T12:00:00.000Z"),
        cfg = "{}",
        challenges = emptyList(),
        enabled = true,
        eventType = ChallengeEventType.GLOBAL,
        bonusId = "bonusId",
      )
    )
    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }
    assertThat(result).isEqualTo(EmptyChallengeEventConfigApiDto)
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return empty config WHEN there are events in DB but time is wrong`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    challengeService.mock(
      { getCurrentChallengeEvent(any()) },
      ChallengeEvent(
        id = ChallengeEventId("15"),
        dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
        dateTo = Instant.parse("2024-11-22T12:00:00.000Z"),
        cfg = "{}",
        challenges = emptyList(),
        enabled = true,
        eventType = ChallengeEventType.GLOBAL,
        bonusId = "bonusId",
      )
    )
    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }
    assertThat(result).isEqualTo(EmptyChallengeEventConfigApiDto)
  }

  @Test
  fun `SHOULD return config`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    val cfg = javaClass.getResource("/user/challenge/event-additional-config.json")!!.readText()
    val eventId = ChallengeEventId("15")
    val event = ChallengeEvent(
      id = eventId,
      dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
      dateTo = Instant.parse("2024-11-22T23:59:00.000Z"),
      cfg = cfg,
      challenges = emptyList(),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    challengeService.mock({ getCurrentChallengeEvent(any()) }, event)
    val offerId = 20056
    val challenge = Challenge(
      id = ChallengeId("13"),
      eventId = eventId,
      progressMax = 100,
      gameId = offerId,
      title = "Get %s correct questions in Trivia Madness",
      icon = "images/merge_blast_icon.jpg",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val userChallenge = UserChallenge(
      userId = USER_ID,
      challenge = challenge,
      progress = 5,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      completedAt = null,
      updatedAt = now,
    )
    challengeService.mock(
      { getUserChallenges(userId = USER_ID) },
      listOf(
        userChallenge,
        userChallenge.copy(challenge = challenge.copy(id = ChallengeId("17")))
      )
    )
    val offer = androidGameOfferStub.copy(id = offerId)
    gamePersistenceService.mock(
      { loadAndroidGamesByIds(setOf(offerId)) }, listOf(
        offer
      )
    )
    androidInstallationLinkProvider.mock(
      { provide(offer.applicationId, USER_ID) },
      "https://play.google.com/store/apps/details?id=com.gimica.mergeblast&listing=justplay"
    )
    specialChallengePotService.mock( { findUserSpecialPot(USER_ID) }, createUserSpecialChallengePot())
    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }
    val expected = javaClass.getResource("/user/challenge/challenge-event-service-config.json")!!.readText()
    val stringResult = json.encodeToString(result as ChallengeEventConfigApiDto)
    assertJsonEquals(expected, stringResult)
  }

  private fun createUserSpecialChallengePot(): UserSpecialChallengePot =
    UserSpecialChallengePot(
      id = "b980c54e-3224-11f0-a491-43ebe80115ee",
      userId = USER_ID,
      counter = 7,
      potConfig = SpecialChallengePot(
        id = 13,
        key = "POT_KEY",
        //language=json
        uiConfig = """
        {
          "mainScreen": [
            {
              "challengeType": "REGULAR",
              "imageUrl": "regular-image.png",
              "title": "Regular challenges"
            },
            {
              "challengeType": "SPECIAL",
              "imageUrl": "special-image.png",
              "title": "Special challenges"
            }
          ],
          "specialChallengeScreen": {
            "treasureImageUrl": "treasure-url.png"
          },
          "claimWidget": {
            "title": "You hit the JackPot!",
            "description": "Keep going! Complete more Treasure Quests for you new epicReward!",
            "image": "claim-image.png"
          }
        }
        """,
        progressMax = 100,
      ),
      earnings = BigDecimal("0.15"),
      state = SpecialChallengePotState.IN_PROGRESS,
      progress = 53,
       applovinNonBannerRevenue = BigDecimal("0.01"),
    )

  @Test
  fun `SHOULD return config with sorted challenges based on order and id ON getChallengeEvent`() {
    val challenges = listOf(
      ChallengeSetup("A", 2, ChallengeState.NOT_STARTED),
      ChallengeSetup("B", 1, ChallengeState.NOT_STARTED),
      ChallengeSetup("C", 1, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 3, ChallengeState.COMPLETED),
      ChallengeSetup("E", 4, ChallengeState.COMPLETED),
      ChallengeSetup("F", 4, ChallengeState.COMPLETED),
      ChallengeSetup("G", 5, ChallengeState.CLAIMED),
      ChallengeSetup("H", 6, ChallengeState.CLAIMED),
      ChallengeSetup("I", 6, ChallengeState.CLAIMED),
    ).shuffled()
    val expected = listOf("D", "E", "F", "B", "C", "A", "G", "H", "I")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN all challenges are not started`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.NOT_STARTED),
      ChallengeSetup("C", 3, ChallengeState.NOT_STARTED),
      ChallengeSetup("B", 2, ChallengeState.NOT_STARTED),
      ChallengeSetup("A", 1, ChallengeState.NOT_STARTED),
    )
    val expected = listOf("A", "B", "C", "D", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN 1 active AND 4 not started 1_4_0`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.NOT_STARTED),
      ChallengeSetup("C", 3, ChallengeState.NOT_STARTED),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("A", 1, ChallengeState.NOT_STARTED),
    )
    val expected = listOf("B", "A", "C", "D", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN 2 active AND 3 not started 2_3_0`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now.minusSeconds(60)),
      ChallengeSetup("C", 3, ChallengeState.NOT_STARTED),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("A", 1, ChallengeState.NOT_STARTED),
    )
    val expected = listOf("D", "B", "A", "C", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN 2 active AND 2 not started AND 1 claimed 2_2_1`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now.minusSeconds(60)),
      ChallengeSetup("C", 3, ChallengeState.CLAIMED, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("A", 1, ChallengeState.NOT_STARTED),
    )
    val expected = listOf("D", "B", "A", "E", "C")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN 2 active AND 1 not started AND 2 claimed 2_1_2`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now.minusSeconds(60)),
      ChallengeSetup("C", 3, ChallengeState.CLAIMED, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("A", 1, ChallengeState.CLAIMED, now.minusSeconds(180)),
    )
    val expected = listOf("D", "B", "E", "A", "C")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN latest active is completed`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now),
      ChallengeSetup("C", 3, ChallengeState.CLAIMED, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now.minusSeconds(60)),
      ChallengeSetup("A", 1, ChallengeState.CLAIMED, now.minusSeconds(180)),
    )
    val expected = listOf("D", "B", "E", "A", "C")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN no active`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.CLAIMED, now.minusSeconds(240)),
      ChallengeSetup("D", 4, ChallengeState.CLAIMED, now),
      ChallengeSetup("C", 3, ChallengeState.CLAIMED, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.CLAIMED, now.minusSeconds(60)),
      ChallengeSetup("A", 1, ChallengeState.CLAIMED, now.minusSeconds(180)),
    )
    val expected = listOf("A", "B", "C", "D", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN only active `() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.IN_PROGRESS, now.minusSeconds(240)),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now),
      ChallengeSetup("C", 3, ChallengeState.IN_PROGRESS, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.COMPLETED, now.minusSeconds(60)),
      ChallengeSetup("A", 1, ChallengeState.IN_PROGRESS, now.minusSeconds(180)),
    )
    val expected = listOf("B", "D", "C", "A", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN one active AND others completed`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.CLAIMED, now.minusSeconds(240)),
      ChallengeSetup("D", 4, ChallengeState.CLAIMED, now),
      ChallengeSetup("C", 3, ChallengeState.IN_PROGRESS, now.minusSeconds(120)),
      ChallengeSetup("B", 2, ChallengeState.CLAIMED, now.minusSeconds(60)),
      ChallengeSetup("A", 1, ChallengeState.CLAIMED, now.minusSeconds(180)),
    )
    val expected = listOf("C", "A", "B", "D", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN challenge active`() {
    val challenges = listOf(
      ChallengeSetup("E", 5, ChallengeState.NOT_STARTED),
      ChallengeSetup("D", 4, ChallengeState.NOT_STARTED),
      ChallengeSetup("C", 3, ChallengeState.NOT_STARTED),
      ChallengeSetup("B", 2, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("A", 1, ChallengeState.NOT_STARTED),
    )
    val expected = listOf("B", "A", "C", "D", "E")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN all status here`() {
    val challenges = listOf(
      ChallengeSetup("H", 8, ChallengeState.NOT_STARTED),
      ChallengeSetup("G", 7, ChallengeState.NOT_STARTED),
      ChallengeSetup("F", 6, ChallengeState.IN_PROGRESS, now),
      ChallengeSetup("E", 5, ChallengeState.IN_PROGRESS, now.minusSeconds(60)),
      ChallengeSetup("D", 4, ChallengeState.COMPLETED, now.minusSeconds(120)),
      ChallengeSetup("C", 3, ChallengeState.COMPLETED, now.minusSeconds(180)),
      ChallengeSetup("B", 2, ChallengeState.CLAIMED, now.minusSeconds(240)),
      ChallengeSetup("A", 1, ChallengeState.CLAIMED, now.minusSeconds(300)),
    )
    val expected = listOf("C", "D", "F", "E", "G", "H", "A", "B")

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return config with sorted challenges ON getChallengeEvent WHEN no challenges`() {
    val challenges = emptyList<ChallengeSetup>()
    val expected = emptyList<String>()

    setupOrderTest(challenges)

    val result = runBlocking { underTest.getChallengeEvent(USER_ID, userLocale) }

    assertThat(result.getChallenges()).isEqualTo(expected)
  }

  @Test
  fun `SHOULD get list of challenge events`() {

    val dateFrom = Instant.parse("2024-11-21T00:00:00Z")
    val dateTo = Instant.parse("2024-11-23T00:00:00Z")
    val eventId = ChallengeEventId("eventId1")
    val challengeEvent = ChallengeEvent(
      id = eventId,
      dateFrom = dateFrom,
      dateTo = dateTo,
      //language=json
      cfg = """
            {
            "challengesUpdateText": "some challenge update text",
            "claimWidget": {
              "bannerColor": "red",
              "bannerEndColor": "green",
              "textColor": "white",
              "claimButtonText": "Ok",
              "headerText": "Some header",
              "progressBarSubtext": "Some sub progress",
              "aheadButtonText": "abt"
            },
            "tutorialSteps": [
               "welcome",
               "cash_bonus"
            ], 
            "tutorialWidget": {
              "backgroundColor": "#FFFFFF",
              "highlightStartColor": "#FFFFFA",
              "highlightEndColor": "#FFFFFB",
               "tcUrl": "https://example.com",
               "tutorialId": "tutorialId"
            },
            "bonusTracker": {
                "bonusId": "bonusId",
                "progressMax": 10,
                "completeText": "You won super bonus!"
              }
            }
          """.trimIndent(),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
      challenges = listOf(
      Challenge(
        id = ChallengeId("id1"),
        eventId = eventId,
        title = "title1",
        icon = "icon1",
        progressMax = 100,
        gameId = 200100,
        calculator = ChallengeProgressCalculatorType.LEVEL_ID,
        order = 1,
        goal = 100,
        applyEarningsCut = true,
        challengeType = ChallengeType.REGULAR,
      ),
        Challenge(
      id = ChallengeId("id2"),
      eventId = eventId,
      title = "title2",
      icon = "icon2",
      progressMax = 100,
      gameId = 200200,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )))
    challengeService.mock(
      { getChallengeEventList(dateFrom = dateFrom, dateTo = dateTo, enabled = true) }, listOf(challengeEvent)
    )

    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(200100)) }, listOf(androidGameOfferStub.copy(applicationId = "gameApplicationId1")))
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(200200)) }, listOf(androidGameOfferStub.copy(applicationId = "gameApplicationId2")))

    val result = runBlocking {
      underTest.getChallengeEventList(
        dateFrom = dateFrom,
        dateTo = dateTo,
        enabled = true
      )
    }

    assertThat(result).isEqualTo(
      ChallengeEventsAdminApiDto(
        events = listOf(
          ChallengeEventAdminApiDto(
            id = "eventId1",
            dateFrom = dateFrom,
            dateTo = dateTo,
            challengesUpdateText = "some challenge update text",
            claimWidget = ClaimWidgetDto(
              bannerColor = "red",
              bannerEndColor = "green",
              textColor = "white",
              claimButtonText = "Ok",
              headerText = "Some header",
              progressBarSubtext = "Some sub progress",
              aheadButtonText = "abt",
            ),
            tutorialSteps = listOf("welcome", "cash_bonus"),
            challenges = listOf(
              ChallengeAdminApiDto(
                id = "id1",
                title = "title1",
                icon = "icon1",
                progressMax = 100,
                gameApplicationId = "gameApplicationId1",
                calculator = ChallengeProgressCalculatorType.LEVEL_ID,
                order = 1,
                goal = 100,
                applyEarningsCut = true,
                challengeType = ChallengeType.REGULAR,
              ),
              ChallengeAdminApiDto(
                id = "id2",
                title = "title2",
                icon = "icon2",
                progressMax = 100,
                gameApplicationId = "gameApplicationId2",
                calculator = ChallengeProgressCalculatorType.LEVEL_ID,
                order = 1,
                goal = 100,
                applyEarningsCut = true,
                challengeType = ChallengeType.REGULAR,
              )
            ),
            enabled = true,
            eventType = ChallengeEventType.GLOBAL,
            tutorialWidget = TutorialWidgetDto(
              backgroundColor = "#FFFFFF",
              highlightStartColor = "#FFFFFA",
              highlightEndColor = "#FFFFFB",
              tcUrl = "https://example.com",
              tutorialId = "tutorialId",
            ),
            bonusTracker = BonusTrackerDto(
              bonusId = "bonusId",
              progressMax = 10,
              completeText = "You won super bonus!"
            ),
          )
        ),
      )
    )
  }

  @Test
  fun `SHOULD update challenge event`() {
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId1", AppPlatform.ANDROID) }, 200100)

    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId2", AppPlatform.ANDROID) }, 200200)

    runBlocking { underTest.updateChallengeEvent(challengeEventAdminApiDto) }

    verifyBlocking(challengeConfigValidator) { validateChallengeEvent(challengeEventAdminApiDto) }
    verifyBlocking(challengeService) { updateChallengeEvent(challengeEvent) }
  }

  @Test
  fun `SHOULD throw exception ON update challenge event WHEN there is no game `() = runTest {
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId1", AppPlatform.ANDROID) }, null)
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId2", AppPlatform.ANDROID) }, 200200)

    assertThrows<InvalidParameterValueException> {
      underTest.updateChallengeEvent(challengeEventAdminApiDto)
    }

    verifyBlocking(challengeConfigValidator) { validateChallengeEvent(challengeEventAdminApiDto) }
    verifyBlocking(challengeService, times(0)) { updateChallengeEvent(challengeEvent) }
  }

  @Test
  fun `SHOULD throw exception ON create challenge event WHEN there is no game `() = runTest {
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId1", AppPlatform.ANDROID) }, null)
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId2", AppPlatform.ANDROID) }, 200200)

    assertThrows<InvalidParameterValueException> {
      underTest.createChallengeEvent(challengeEventAdminApiDto)
    }

    verifyBlocking(challengeConfigValidator) { validateChallengeEvent(challengeEventAdminApiDto) }
    verifyBlocking(challengeService, times(0)) { updateChallengeEvent(challengeEvent) }
  }


  @Test
  fun `SHOULD create challenge event`() = runTest {
    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId1", AppPlatform.ANDROID) }, 200100)

    gamePersistenceService.mock({ getGameIdByApplicationId("gameApplicationId2", AppPlatform.ANDROID) }, 200200)

    runBlocking { underTest.createChallengeEvent(challengeEventAdminApiDto) }

    verifyBlocking(challengeConfigValidator) { validateChallengeEvent(challengeEventAdminApiDto) }
    verifyBlocking(challengeService) { createChallengeEvent(challengeEvent) }
  }


  private fun IChallengeEventConfigApiDto.getChallenges() = (this as ChallengeEventConfigApiDto)
    .challenges
    .map { it.challengeId.value }

  @OptIn(PackagePrivate::class)
  private fun setupOrderTest(challenges: List<ChallengeSetup>) {
    val completedAt = now.minusSeconds(10)
    val cfg = javaClass.getResource("/user/challenge/event-additional-config.json")!!.readText()
    val eventId = ChallengeEventId("15")
    val offerId = 20056
    val challenge =
    Challenge(
      id = ChallengeId(""),
      eventId = eventId,
      progressMax = 100,
      gameId = offerId,
      title = "Get %s correct questions in Trivia Madness",
      icon = "images/merge_blast_icon.jpg",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val userChallenge = UserChallenge(
      userId = USER_ID,
      challenge = challenge,
      progress = 5,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      completedAt = null,
      updatedAt = now,
    )
    val offer = androidGameOfferStub.copy(id = offerId)
    val challengeEvent = ChallengeEvent(
      id = eventId,
      dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
      dateTo = Instant.parse("2024-11-22T23:59:00.000Z"),
      cfg = cfg,
      enabled = true,
      challenges = emptyList(),
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    val dbChallenges = challenges.map { challengeSetup ->
      userChallenge.copy(
        challenge = challenge.copy(id = ChallengeId(challengeSetup.id), order = challengeSetup.order),
        state = challengeSetup.state,
        completedAt = if (challengeSetup.state in listOf(ChallengeState.COMPLETED, ChallengeState.CLAIMED)) completedAt else null,
        updatedAt = challengeSetup.updatedAt,
      )
    }

    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    timeService.mock({ now() }, now)
    challengeService.mock({ getCurrentChallengeEvent(any()) }, challengeEvent)
    challengeService.mock({ getUserChallenges(USER_ID) }, dbChallenges)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(offerId)) }, listOf(offer))
    androidInstallationLinkProvider.mock(
      { provide(offer.applicationId, USER_ID) },
      "https://play.google.com/store/apps/details?id=com.gimica.mergeblast&listing=justplay"
    )

  }

  data class ChallengeSetup(
    val id: String,
    val order: Int,
    val state: ChallengeState,
    val updatedAt: Instant? = null,
  )
}
