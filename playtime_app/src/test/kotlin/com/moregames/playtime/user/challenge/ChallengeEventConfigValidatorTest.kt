package com.moregames.playtime.user.challenge

import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.Game
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.exception.InvalidParameterValueException
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Locale

class ChallengeEventConfigValidatorTest {

  private val translationService: TranslationService = mock()
  private val timeService: TimeService = mock()
  private val gamesService: GamesService = mock()
  private val underTest = ChallengeConfigValidator(
    translationService = translationService,
    timeService = timeService,
    gamesService = gamesService,
  )

  companion object {
    val now: Instant = Instant.parse("2025-12-03T10:15:30.00Z")
    const val APPLICATION_ID1 = "gameApplicationId1"
    const val APPLICATION_ID2 = "gameApplicationId2"
    val challenge1 = ChallengeAdminApiDto(
      id = "id1",
      title = "\$_title1",
      icon = "icon1",
      progressMax = 100,
      gameApplicationId = APPLICATION_ID1,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )
    val challenge2 = ChallengeAdminApiDto(
      id = "id2",
      title = "title2",
      icon = "icon2",
      progressMax = 100,
      gameApplicationId = APPLICATION_ID2,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )
    val challengeEventAdminApiDto = ChallengeEventAdminApiDto(
      id = "eventId1",
      dateFrom = Instant.parse("2025-12-01T10:15:30.00Z"),
      dateTo = Instant.parse("2025-12-05T10:15:30.00Z"),
      challengesUpdateText = "some challenge update text",
      claimWidget = ClaimWidgetDto(
        bannerColor = "red",
        bannerEndColor = "green",
        textColor = "white",
        claimButtonText = "Ok",
        headerText = "Some header",
        progressBarSubtext = "Some sub progress",
        aheadButtonText = null,
      ),
      tutorialSteps = listOf("welcome", "cash_bonus"),
      challenges = listOf(challenge1, challenge2),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      tutorialWidget = TutorialWidgetDto(
        backgroundColor = "#FFFFFF",
        highlightStartColor = "#FFFFFA",
        highlightEndColor = "#FFFFFB",
        tcUrl = "https://example.com",
        tutorialId = "tutorialId",
      ),
      bonusTracker = BonusTrackerDto(
        bonusId = "bonusId",
        progressMax = 10,
        completeText = "You won super bonus!"
      ),
    )
  }

  @BeforeEach
  fun init() {
    timeService.mock ({ now() }, now)
    translationService.mock ({ tryTranslate("\$_title1", Locale.ENGLISH) }, "Title")
    val game = mock<Game>()
    gamesService.mock({ getGameByApplicationId(any(), eq(AppPlatform.ANDROID))}, game)
  }


  @Test
  fun `SHOULD throw exception WHEN there is no game with application id`() = runTest {
    gamesService.mock({ getGameByApplicationId(any(), eq(AppPlatform.ANDROID))}, null)
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(challengeEventAdminApiDto)
    }
  }

  @Test
  fun `SHOULD throw exception WHEN we have 1 game several times in challenge event`() = runTest {
    val dto = challengeEventAdminApiDto.copy(
      challenges = listOf(challenge1, challenge2, challenge1.copy(id = "id3")),
    )
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(dto)
    }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation`() = runTest {
    translationService.mock ({ tryTranslate("\$_title2", Locale.ENGLISH) }, "\$_title2")
    val dto = challengeEventAdminApiDto.copy(
      challenges = listOf(challenge1.copy(title = "\$_title2")),
    )
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(dto)
    }
  }


  @ParameterizedTest
  @ValueSource(ints = [0, -5])
  fun `SHOULD throw exception WHEN progressMax is less or equal zero`(progressMax: Int) = runTest {
    val dto = challengeEventAdminApiDto.copy(
      challenges = listOf(challenge1.copy(progressMax = progressMax)),
    )
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(dto)
    }
  }

  @Test
  fun `SHOULD throw exception WHEN dateTo is before now`() = runTest {
    val dto = challengeEventAdminApiDto.copy(
      dateTo = now.minus(1, ChronoUnit.DAYS),
    )
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(dto)
    }
  }

  @Test
  fun `SHOULD throw exception WHEN dateTo is before dateFrom`() = runTest {
    val dto = challengeEventAdminApiDto.copy(
      dateFrom = challengeEventAdminApiDto.dateTo,
      dateTo = challengeEventAdminApiDto.dateFrom,
    )
    assertThrows<InvalidParameterValueException> {
      underTest.validateChallengeEvent(dto)
    }
  }

  @Test
  fun `SHOULD not throw exception WHEN dto is valid`() = runTest {
    assertDoesNotThrow { underTest.validateChallengeEvent(challengeEventAdminApiDto) }
  }
}