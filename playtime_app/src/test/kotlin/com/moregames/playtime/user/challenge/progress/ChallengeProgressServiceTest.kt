package com.moregames.playtime.user.challenge.progress

import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.ChallengeCompletedEffectHandler
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeEventUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant

class ChallengeProgressServiceTest {

  private val gamesService: GamesService = mock()
  private val progressCalculators: Map<String, ChallengeGameProgressCalculator> = mock()
  private val progressCalculator: ChallengeGameProgressCalculator = mock()
  private val messageBus: MessageBus = mock()
  private val challengeService: ChallengeService = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val timeService: TimeService = mock()
  private val specialChallengePotService: SpecialChallengePotService = mock()

  private val underTest = ChallengeProgressService(
    challengeService = challengeService,
    gamesService = gamesService,
    progressCalculators = progressCalculators,
    messageBus = messageBus,
    bigQueryEventPublisher = bigQueryEventPublisher,
    timeService = timeService,
    specialChallengePotService = specialChallengePotService,
  )

  private companion object {
    const val USER_ID = "userId"
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.TREASURE_MASTER_APP_ID,
      score = 15,
    )
    val now: Instant = Instant.parse("2025-01-22T09:58:00.000Z")
    val EVENT_ID = ChallengeEventId("TM_20_CE")
    val CHALLENGE_ID = ChallengeId("TM_20_C")
    const val GAME_ID = 3000006
    val CALCULATOR_TYPE = ChallengeProgressCalculatorType.LEVEL_ID

    val challenge = Challenge(
      id = CHALLENGE_ID,
      eventId = EVENT_ID,
      progressMax = 100,
      gameId = GAME_ID,
      title = "title",
      icon = "icon",
      calculator = CALCULATOR_TYPE,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )

    val challengeEventDto = ChallengeEvent(
      id = EVENT_ID,
      dateFrom = now.minus(Duration.ofDays(1)),
      dateTo = now.plus(Duration.ofDays(1)),
      cfg = "{}",
      enabled = true,
      challenges = listOf(challenge),
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )

    val userChallengeStub = UserChallenge(
      userId = USER_ID,
      challenge = challenge,
      progress = 0,
      state = ChallengeState.NOT_STARTED,
      coins = 0,
      completedAt = null,
      updatedAt = now,
    )

    val userChallengeEvent = UserChallengeEvent(
      userId = USER_ID,
      eventId = EVENT_ID,
      state = ChallengeEventState.IN_PROGRESS,
      applovinNonBannerRevenue = BigDecimal.TWO,
      earnings = BigDecimal.TEN,
      startedAt = now.minus(Duration.ofDays(1)),
    )
  }

  @BeforeEach
  fun setUp() {
    gamesService.mock({ getGameId(ApplicationId.TREASURE_MASTER_APP_ID, AppPlatform.ANDROID) }, GAME_ID)
    reset(
      challengeService,
      progressCalculator,
      progressCalculators,
    )
    timeService.mock( { now()}, now)
  }

  @Test
  fun `SHOULD complete challenge event WHEN all challenges are completed`() {
    challengeService.mock( { getCurrentChallengeEvent(USER_ID)}, challengeEventDto)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(eq(progressDto), any()) }, ChallengeProgress(100, "20"))
    challengeService.mock({ updateChallengeProgress(any(), any(), any()) }, true)
    challengeService.mock( { getUserChallenges(USER_ID, EVENT_ID)}, listOf(
      userChallengeStub.copy(state = ChallengeState.COMPLETED),
      userChallengeStub.copy(challenge = challenge.copy(id = ChallengeId("2")), state = ChallengeState.COMPLETED),
    ))
    challengeService.mock( { completeEvent(EVENT_ID, USER_ID)}, true)
    challengeService.mock( { getUserChallenge(CHALLENGE_ID, USER_ID)}, userChallengeStub.copy(state = ChallengeState.IN_PROGRESS))
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)

    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService, times(1)) {
      updateChallengeProgress(
        userChallengeStub.copy(state = ChallengeState.IN_PROGRESS),
        ChallengeProgress(100, "20"),
        ChallengeState.COMPLETED
      )
    }
    verifyBlocking(challengeService, times(1)) { completeEvent(EVENT_ID, USER_ID) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserChallengeEventUpdatedBqDto(
      userId = USER_ID,
      challengeEventId = EVENT_ID,
      state = ChallengeEventState.COMPLETED,
      earnings = userChallengeEvent.earnings,
      applovinNonBannerRevenue = userChallengeEvent.applovinNonBannerRevenue!!,
      eventStart = challengeEventDto.dateFrom,
      eventEnd = challengeEventDto.dateTo,
      createdAt = timeService.now(),
    ))}
    verifyBlocking( specialChallengePotService) { updateChallengeSpecialPotProgress(userChallengeStub.copy(state = ChallengeState.COMPLETED, progress = 100)) }
  }

  @Test
  fun `SHOULD start challenge and update progress`() {
    val challenge1 = userChallengeStub.copy(challenge = challenge, state = ChallengeState.NOT_STARTED)
    challengeService.mock( { getCurrentChallengeEvent(USER_ID)}, challengeEventDto)
    challengeService.mock( { startUserEvent(EVENT_ID, USER_ID)}, true)
    challengeService.mock( { startUserChallenge(CHALLENGE_ID, USER_ID)}, true)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(eq(progressDto), any()) }, ChallengeProgress(97, "20"))
    challengeService.mock({ updateChallengeProgress(any(), any(), any()) }, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService, times(1)) { startUserChallenge(CHALLENGE_ID, USER_ID) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(UserChallengeUpdatedBqDto(
        userId = USER_ID,
        challengeId = CHALLENGE_ID,
        state = ChallengeState.IN_PROGRESS,
        gameId = GAME_ID,
        completedAt = null,
        createdAt = timeService.now(),
        challengeEventId = EVENT_ID,
      ))
    }
    verifyBlocking(challengeService, times(1)) {
      updateChallengeProgress(
        challenge1.copy(state = ChallengeState.IN_PROGRESS),
        ChallengeProgress(97, "20"),
        ChallengeState.IN_PROGRESS
      )
    }

    verifyBlocking(challengeService, times(0)) { completeEvent(challengeEventDto.id, USER_ID) }
  }

  @Test
  fun `SHOULD not start challenge and not update progress WHEN there is no progress calculator`() {
    challengeService.mock( { getCurrentChallengeEvent(USER_ID)}, challengeEventDto)
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock( { startUserChallenge(CHALLENGE_ID, USER_ID)}, true)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, null)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService, times(0)) { startUserChallenge(CHALLENGE_ID, USER_ID) }
    verifyBlocking(challengeService, times(0)) { updateChallengeProgress(any(), any(), any()) }
    verifyBlocking(challengeService, times(0)) { completeEvent(challengeEventDto.id, USER_ID) }
  }

  @Test
  fun `SHOULD not start challenge event WHEN there is user event`() {
    challengeService.mock( { getCurrentChallengeEvent(USER_ID)}, challengeEventDto)
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock( { startUserChallenge(CHALLENGE_ID, USER_ID)}, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService, times(0)) { startUserEvent(EVENT_ID, USER_ID) }
  }

  @Test
  fun `SHOULD start challenge event WHEN there is no user event`() {
    challengeService.mock( { getCurrentChallengeEvent(USER_ID)}, challengeEventDto)
    challengeService.mock( { startUserEvent(EVENT_ID, USER_ID)}, true)
    challengeService.mock( { startUserChallenge(CHALLENGE_ID, USER_ID)}, true)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(eq(progressDto), any()) }, ChallengeProgress(50, "20"))
    challengeService.mock( { updateChallengeProgress(any(), any(), any()) }, true)

    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService) { startUserEvent(EVENT_ID, USER_ID) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserChallengeEventUpdatedBqDto(
      userId = USER_ID,
      challengeEventId = EVENT_ID,
      state = ChallengeEventState.IN_PROGRESS,
      earnings = BigDecimal.ZERO,
      applovinNonBannerRevenue = BigDecimal.ZERO,
      eventStart = challengeEventDto.dateFrom,
      eventEnd = challengeEventDto.dateTo,
      createdAt = timeService.now(),
    ))}
  }

  @Test
  fun `SHOULD cap progress WHEN calculator gives too high value`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    val userChallenge = userChallengeStub.copy(state = ChallengeState.IN_PROGRESS, progress = 40)
    challengeService.mock( { getUserChallenge(CHALLENGE_ID, USER_ID)}, userChallenge)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(progressDto, userChallenge) }, ChallengeProgress(150, "20"))
    challengeService.mock( { updateChallengeProgress(any(), any(), any()) }, true)
    challengeService.mock( { getUserChallenges(USER_ID, EVENT_ID)}, listOf(userChallenge.copy(state = ChallengeState.COMPLETED, progress = 100)))
    challengeService.mock( { completeEvent(EVENT_ID, USER_ID) }, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService, times(1)) {
      updateChallengeProgress(any(), eq(ChallengeProgress(100, "20")), any()) }
  }

  @Test
  fun `SHOULD not update progress WHEN calculated progress is less then current progress`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    val userChallenge = userChallengeStub.copy(state = ChallengeState.IN_PROGRESS, progress = 40)
    challengeService.mock( { getUserChallenge(CHALLENGE_ID, USER_ID)}, userChallenge)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(progressDto, userChallenge) }, ChallengeProgress(35, "20"))
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    verifyBlocking(challengeService, times(0)) { updateChallengeProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD not update progress WHEN challenge is in final state`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( { getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock( { getUserChallenge(CHALLENGE_ID, USER_ID)}, userChallengeStub.copy(state = ChallengeState.COMPLETED))
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    verifyBlocking(challengeService, times(0)) { updateChallengeProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD not update progress WHEN event is in final state`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( {getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent.copy(state = ChallengeEventState.COMPLETED))
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    verifyBlocking(challengeService, times(0)) { updateChallengeProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD do nothing WHEN there is no challenge with app id`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    gamesService.mock( { getGameId("another.app", AppPlatform.ANDROID) }, 1257)
    runBlocking { underTest.handleUserChallengeProgress(progressDto.copy(applicationId = "another.app")) }
    verifyBlocking(challengeService) {getCurrentChallengeEvent(USER_ID)}
    verifyNoMoreInteractions(challengeService)
    verifyNoInteractions(progressCalculators)
  }

  @Test
  fun `SHOULD do nothing WHEN there is no game with app id`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    runBlocking { underTest.handleUserChallengeProgress(progressDto.copy(applicationId = "another.app")) }
    verifyBlocking(challengeService) {getCurrentChallengeEvent(USER_ID)}
    verifyNoMoreInteractions(challengeService)
    verifyNoInteractions(progressCalculators)
  }

  @Test
  fun `SHOULD do nothing WHEN there is no challenge event`() {
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, null)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyBlocking(challengeService) {getCurrentChallengeEvent(USER_ID)}
    verifyNoInteractions(gamesService)
    verifyNoMoreInteractions(challengeService)
    verifyNoInteractions(progressCalculators)
  }

  @Test
  fun `SHOULD NOT publish notifications ON handleUserChallengeProgress WHEN NOT progress updated to completed`() {
    val userChallenge = userChallengeStub.copy(state = ChallengeState.IN_PROGRESS, progress = 95)
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( {getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock({ getUserChallenge(challenge.id, USER_ID) }, userChallenge)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(progressDto, userChallenge) }, ChallengeProgress(97, "20"))
    challengeService.mock({ updateChallengeProgress(any(), any(), any()) }, false)
    challengeService.mock({ completeEvent(EVENT_ID, USER_ID)}, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT publish notifications ON handleUserChallengeProgress WHEN duplicated progress update`() {
    val userChallenge = userChallengeStub.copy(state = ChallengeState.IN_PROGRESS, progress = 95)
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( {getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock({ getUserChallenge(challenge.id, USER_ID) }, userChallenge)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(progressDto, userChallenge) }, ChallengeProgress(100, "20"))
    challengeService.mock({ updateChallengeProgress(any(), any(), any()) }, false)
    challengeService.mock({ getUserChallenges(USER_ID) }, listOf(userChallenge.copy(state = ChallengeState.COMPLETED, progress = 100)))
    challengeService.mock({ completeEvent(EVENT_ID, USER_ID)}, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD publish notifications ON handleUserChallengeProgress WHEN progress updated to completed`() {
    //given
    val userChallenge = userChallengeStub.copy(state = ChallengeState.IN_PROGRESS, progress = 95)
    challengeService.mock({ getCurrentChallengeEvent(USER_ID) }, challengeEventDto)
    challengeService.mock( {getUserChallengeEvent(USER_ID, EVENT_ID)}, userChallengeEvent)
    challengeService.mock({ getUserChallenge(challenge.id, USER_ID) }, userChallenge)
    progressCalculators.mock({ get(CALCULATOR_TYPE.name) }, progressCalculator)
    progressCalculator.mock({ calculateProgress(progressDto, userChallenge) }, ChallengeProgress(100, "20"))
    challengeService.mock({ updateChallengeProgress(any(), any(), any()) }, true)
    challengeService.mock({ getUserChallenges(USER_ID, EVENT_ID) }, listOf(userChallenge.copy(state = ChallengeState.COMPLETED, progress = 100)))
    challengeService.mock({ completeEvent(EVENT_ID, USER_ID)}, true)
    //when
    runBlocking { underTest.handleUserChallengeProgress(progressDto) }
    //then
    verify(messageBus, times(1)).publishAsync(
      ChallengeCompletedEffectHandler.ChallengeCompletedEffect(USER_ID, challenge.id, EVENT_ID, GAME_ID, ChallengeType.REGULAR)
    )
  }
}