package com.moregames.playtime.user.offer

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.utils.androidGameOfferListStub
import com.moregames.playtime.utils.androidGameOfferStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import kotlin.time.Duration.Companion.days
import kotlin.time.toJavaDuration

@ExtendWith(MockExtension::class)
class AndroidGameReplacementServiceTest(
  private val abTestingService: AbTestingService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val timeService: TimeService,
) {
  private val underTest = AndroidGameReplacementService(
    abTestingService = abTestingService,
    gamePersistenceService = gamePersistenceService,
    gamesService = gamesService,
    timeService = timeService,
  )

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
    abTestingService.mock({ isUserExperimentParticipant(userDto.id, ClientExperiment.ANDROID_NEW_SOLITAIRE) }, true)
    abTestingService.mock({ isUserExperimentParticipant(userDto.id, ClientExperiment.BUBBLE_CHEF_VS_BUBBLE_POP) }, true)
  }

  @Test
  fun `SHOULD replace solitaire verse with forevergreen WHEN user is ON exp`() {
    val newSolitaireId = 200070
    gamesService.mock({ getGameId(ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, AppPlatform.ANDROID) }, newSolitaireId)
    val forevergreenSolitaire = androidGameOfferStub.copy(id = newSolitaireId, applicationId = ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, orderKey = 100000)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(newSolitaireId)) }, listOf(forevergreenSolitaire))

    val result = runBlocking {
      underTest.replaceGames(userDto, androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub.filter { it.applicationId != ApplicationId.SOLITAIRE_VERSE_APP_ID } + forevergreenSolitaire.copy(orderKey = 20))
  }

  @Test
  fun `SHOULD replace solitaire verse with forevergreen WHEN user is OFF exp and FTUE passed`() {
    val newSolitaireId = 200070
    gamesService.mock({ getGameId(ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, AppPlatform.ANDROID) }, newSolitaireId)
    abTestingService.mock({ isUserExperimentParticipant(userDto.id, ClientExperiment.ANDROID_NEW_SOLITAIRE) }, false)
    val forevergreenSolitaire = androidGameOfferStub.copy(id = newSolitaireId, applicationId = ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, orderKey = 100000)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(newSolitaireId)) }, listOf(forevergreenSolitaire))

    val result = runBlocking {
      underTest.replaceGames(userDto.copy(createdAt = now - 8.days.toJavaDuration()), androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub.filter { it.applicationId != ApplicationId.SOLITAIRE_VERSE_APP_ID } + forevergreenSolitaire.copy(orderKey = 20))
  }

  @Test
  fun `SHOULD return original list WHEN solitaire verse is not present AND user is ON exp`() {
    val offersWithoutSolitaireVerse = androidGameOfferListStub.filter { it.applicationId != ApplicationId.SOLITAIRE_VERSE_APP_ID }
    val result = runBlocking {
      underTest.replaceGames(userDto, offersWithoutSolitaireVerse)
    }
    assertThat(result).isEqualTo(offersWithoutSolitaireVerse)
    verifyNoInteractions(gamePersistenceService)
  }

  @Test
  fun `SHOULD replace bubble pop with bubble chef WHEN user is ON exp`() {
    val newBubbleChefId = 200077
    gamesService.mock({ getGameId(ApplicationId.BUBBLE_CHIEF_APP_ID, AppPlatform.ANDROID) }, newBubbleChefId)
    val bubbleChefOffer = androidGameOfferStub.copy(id = newBubbleChefId, applicationId = ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, orderKey = 100000)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(newBubbleChefId)) }, listOf(bubbleChefOffer))

    val result = runBlocking {
      underTest.replaceGames(userDto, androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub.filter { it.applicationId != ApplicationId.BUBBLE_POP_APP_ID } + bubbleChefOffer.copy(orderKey = 80))
  }

  @Test
  fun `SHOULD replace bubble pop with bubble chef WHEN user is OFF exp and FTUE passed`() {
    val newBubbleChefId = 200077
    gamesService.mock({ getGameId(ApplicationId.BUBBLE_CHIEF_APP_ID, AppPlatform.ANDROID) }, newBubbleChefId)
    abTestingService.mock({ isUserExperimentParticipant(userDto.id, ClientExperiment.BUBBLE_CHEF_VS_BUBBLE_POP) }, false)
    val bubbleChefOffer = androidGameOfferStub.copy(id = newBubbleChefId, applicationId = ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID, orderKey = 100000)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(newBubbleChefId)) }, listOf(bubbleChefOffer))

    val result = runBlocking {
      underTest.replaceGames(userDto.copy(createdAt = now - 8.days.toJavaDuration()), androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub.filter { it.applicationId != ApplicationId.BUBBLE_POP_APP_ID } + bubbleChefOffer.copy(orderKey = 80))
  }

  @Test
  fun `SHOULD return original list WHEN bubble chef is not present AND user is ON exp`() {
    val offersWithoutBubblePop = androidGameOfferListStub.filter { it.applicationId != ApplicationId.BUBBLE_POP_APP_ID }
    val result = runBlocking {
      underTest.replaceGames(userDto, offersWithoutBubblePop)
    }
    assertThat(result).isEqualTo(offersWithoutBubblePop)
    verifyNoInteractions(gamePersistenceService)
  }

  private companion object {
    private val now = Instant.now()
    private val userDto = userDtoStub.copy(
      createdAt = now,
    )
  }
}