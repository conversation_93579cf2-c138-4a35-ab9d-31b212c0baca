package com.moregames.playtime.user.challenge.common

import assertk.assertThat
import assertk.assertions.*
import com.justplayapps.service.rewarding.earnings.table.UserChallengeEventRewardedTable
import com.moregames.base.gameStub
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.GamesTable
import com.moregames.base.table.UserSuspicionsTable.clientDefault
import com.moregames.base.util.PackagePrivate
import com.moregames.base.util.TimeService
import com.moregames.base.util.prepareGame
import com.moregames.base.util.prepareUser
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.config.BonusTrackerDto
import com.moregames.playtime.user.challenge.dto.config.ChallengeEventJsonConfigDto
import com.moregames.playtime.user.challenge.dto.config.ClaimWidgetDto
import com.moregames.playtime.user.challenge.dto.config.TutorialWidgetDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import com.moregames.playtime.user.challenge.table.ChallengeEventTable
import com.moregames.playtime.user.challenge.table.ChallengeTable
import com.moregames.playtime.user.challenge.table.UserChallengeEventTable
import com.moregames.playtime.user.challenge.table.UserChallengeTable
import com.moregames.playtime.user.promotion.event.manager.ftue.FtueChallengeEventId
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.util.minus
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.decodeFromString
import net.javacrumbs.jsonunit.JsonAssert
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlin.time.Duration.Companion.hours

@ExtendWith(DatabaseExtension::class)
@OptIn(PackagePrivate::class)
class ChallengeEventPersistenceServiceTest(
  private val database: Database,
) {
  private val timeService: TimeService = mock()

  companion object {
    const val APPLICATION_ID = "com.gimica.supergame"
  }

  private lateinit var service: ChallengeEventPersistenceService
  private var testGameId: Int = 0
  private lateinit var user1Id: String
  private lateinit var user2Id: String
  private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

  @BeforeEach
  fun before() {
    service = ChallengeEventPersistenceService(database, timeService)
    testGameId = database.prepareGame(gameStub.copy(applicationId = APPLICATION_ID), isDisabled = true)
    user1Id = database.prepareUser()
    user2Id = database.prepareUser()
    UserChallengeTable.updatedAt.clientDefault { now }
    whenever(timeService.now()).thenReturn(now)
  }

  @AfterEach
  fun after() {
    transaction(database) {
      UserChallengeEventRewardedTable.deleteAll()
      UserChallengeEventTable.deleteAll()
      UserChallengeTable.deleteAll()
      ChallengeTable.deleteWhere {
        ChallengeTable.id like "TEST_%"
      }
      ChallengeEventTable.deleteWhere {
        ChallengeEventTable.id like "TEST_%"
      }
      GamesTable.deleteWhere {
        GamesTable.applicationId eq APPLICATION_ID
      }
    }
  }

  @Test
  fun `SHOULD count claimed bonusEvent ON countClaimedBonusEvent`() = runTest {
    insertChallengeEvent(ChallengeEventId("TEST_1"), bonusId = "bonusId1")
    insertChallengeEvent(ChallengeEventId("TEST_2"), bonusId = "bonusId1")
    insertChallengeEvent(ChallengeEventId("TEST_3"), bonusId = "bonusId1")
    insertChallengeEvent(ChallengeEventId("TEST_4"), bonusId = "bonusId2")
    insertChallengeEvent(ChallengeEventId("TEST_5"), bonusId = "bonusId2")

    insertUserChallengeEvent(user1Id, ChallengeEventId("TEST_1"), state = ChallengeEventState.CLAIMED)
    insertUserChallengeEvent(user2Id, ChallengeEventId("TEST_1"), state = ChallengeEventState.CLAIMED)
    insertUserChallengeEvent(user1Id, ChallengeEventId("TEST_2"), state = ChallengeEventState.CLAIMED)
    insertUserChallengeEvent(user1Id, ChallengeEventId("TEST_3"), state = ChallengeEventState.IN_PROGRESS)
    insertUserChallengeEvent(user1Id, ChallengeEventId("TEST_4"), state = ChallengeEventState.CLAIMED)
    insertUserChallengeEvent(user1Id, ChallengeEventId("TEST_5"), state = ChallengeEventState.CLAIMED)

    val result = service.countClaimedBonusEvent(user1Id, "bonusId1")
    assertThat(result).isEqualTo(2)
  }

  @ParameterizedTest
  @EnumSource(FtueChallengeEventId::class)
  fun `SHOULD find challenges for FTUE`(eventId: FtueChallengeEventId) = runTest {
    val result = service.loadChallengeEventById(ChallengeEventId(eventId.name))
    assertThat(result).isNotNull()
    result!!
    assertThat(result.challenges).isNotEmpty()
  }

  @Test
  fun `SHOULD not update challenge progress WHEN there is no appropriate record`() {
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    insertUserChallengeEvent(user1Id, eventId, state = ChallengeEventState.COMPLETED, earnings = BigDecimal.ZERO)
    insertUserChallenge(user1Id, challenge1Id)
    val challenge = Challenge(
      id = challenge1Id,
      eventId = eventId,
      title = "TEST_21",
      progressMax = 100,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      icon = "icon",
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val dto = UserChallenge(
      userId = user1Id,
      challenge = challenge,
      progress = 10,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      achievement = "15",
      completedAt = null,
      updatedAt = now,
    )
    val result = runBlocking { service.updateChallengeProgress(dto, ChallengeProgress(100, "20"), ChallengeState.COMPLETED) }
    assertThat(result).isFalse()
    assertUserChallenge(challenge1Id, user1Id, ChallengeState.IN_PROGRESS, 15, 0, "15")
  }

  @Test
  fun `SHOULD update challenge progress WHEN there is appropriate record`() {
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challengeId = ChallengeId("TEST_12")
    insertChallenge(challengeId, eventId, "t1")
    insertUserChallengeEvent(user1Id, eventId, state = ChallengeEventState.COMPLETED, earnings = BigDecimal.ZERO)
    insertUserChallenge(user1Id, challengeId)
    val challenge = Challenge(
      id = challengeId,
      eventId = eventId,
      progressMax = 100,
      gameId = 42,
      title = "title",
      icon = "icon",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val dto = UserChallenge(
      userId = user1Id,
      challenge = challenge,
      progress = 15,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      achievement = "15",
      completedAt = now,
      updatedAt = now,
    )
    val result = runBlocking { service.updateChallengeProgress(dto, ChallengeProgress(100, "20"), ChallengeState.COMPLETED) }
    assertThat(result).isTrue()
    assertUserChallenge(challengeId, user1Id, ChallengeState.COMPLETED, 100, 0, "20", now)
  }


  @Test
  fun `SHOULD do nothing ON complete WHEN event state equals COMPLETED`() {
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    insertUserChallengeEvent(user1Id, eventId, state = ChallengeEventState.COMPLETED, earnings = BigDecimal.ZERO)
    val result = runBlocking { service.completeEvent(eventId, user1Id) }
    assertThat(result).isFalse()
    assertUserChallengeEvent(eventId, user1Id, ChallengeEventState.COMPLETED, BigDecimal.ZERO.setScale(2))
  }

  @Test
  fun `SHOULD complete event WHEN event state equals IN_PROGRESS`() {
    val eventId = ChallengeEventId("TEST_2")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    insertUserChallengeEvent(user1Id, eventId, state = ChallengeEventState.IN_PROGRESS, earnings = BigDecimal.ZERO)
    val result = runBlocking { service.completeEvent(eventId, user1Id) }
    assertThat(result).isTrue()
    assertUserChallengeEvent(eventId, user1Id, ChallengeEventState.COMPLETED, BigDecimal.ZERO.setScale(2))
  }

  @Test
  fun `SHOULD start a user's challenge`() {
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    val result = runBlocking { service.startUserChallenge(challenge1Id, user1Id) }
    assertThat(result).isTrue()
    val result2 = assertDoesNotThrow { runBlocking { service.startUserChallenge(challenge1Id, user1Id) } }
    assertThat(result2).isFalse()
    assertUserChallenge(challenge1Id, user1Id, ChallengeState.IN_PROGRESS, 0, 0, null)
    val challenge = Challenge(
      id = challenge1Id,
      eventId = eventId,
      progressMax = 100,
      gameId = testGameId,
      title = "title",
      icon = "icon",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val dto = UserChallenge(
      userId = user1Id,
      challenge = challenge,
      progress = 0,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      achievement = "15",
      completedAt = null,
      updatedAt = now,
    )
    runBlocking { service.updateChallengeProgress(dto, ChallengeProgress(50, "10"), ChallengeState.IN_PROGRESS) }
    assertUserChallenge(challenge1Id, user1Id, ChallengeState.IN_PROGRESS, 50, 0, "10")
    assertDoesNotThrow { runBlocking { service.startUserChallenge(challenge1Id, user1Id) } }
    assertUserChallenge(challenge1Id, user1Id, ChallengeState.IN_PROGRESS, 50, 0, "10")
  }

  @Test
  fun `SHOULD start a user's challenge event`() {
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    runBlocking { service.startUserEvent(eventId, user1Id) }
    assertDoesNotThrow { runBlocking { service.startUserEvent(eventId, user1Id) } }
    assertUserChallengeEvent(eventId, user1Id, ChallengeEventState.IN_PROGRESS, BigDecimal.ZERO.setScale(2))
    runBlocking { service.completeEvent(eventId, user1Id) }
    assertDoesNotThrow { runBlocking { service.startUserEvent(eventId, user1Id) } }
    assertUserChallengeEvent(eventId, user1Id, ChallengeEventState.COMPLETED, BigDecimal.ZERO.setScale(2))
  }

  @Test
  fun `SHOULD complete challenge ON forceCompleteUserChallenge WHEN challenge is in PROGRESS `() {
    //given
    val eventId = ChallengeEventId("TEST_1_")
    insertChallengeEvent(eventId)
    val challengeId = ChallengeId("TEST_11_")
    insertChallenge(challengeId, eventId, challengeId.value)
    insertUserChallenge(user1Id, challengeId, state = ChallengeState.IN_PROGRESS, progress = 15)
    //when
    val result = runBlocking { service.forceCompleteUserChallenge(challengeId = challengeId.value, userId = user1Id) }
    //then
    assertThat(result).isTrue()
    assertUserChallenge(challengeId, user1Id, ChallengeState.COMPLETED, 100, 0, "15")
  }

  private fun assertUserChallenge(
    challengeId: ChallengeId,
    userId: String,
    state: ChallengeState,
    progress: Int,
    coins: Int,
    achievement: String?,
    completedAt: Instant? = null,
  ) {
    val cnt = transaction(database) {
      UserChallengeTable
        .select {
          (UserChallengeTable.challengeId eq challengeId.value) and
              (UserChallengeTable.userId eq userId)
        }
        .map {
          assertThat(it[UserChallengeTable.state]).isEqualTo(state)
          assertThat(it[UserChallengeTable.progress]).isEqualTo(progress)
          assertThat(it[UserChallengeTable.coins]).isEqualTo(coins)
          assertThat(it[UserChallengeTable.achievement]).isEqualTo(achievement)
          assertThat(it[UserChallengeTable.completedAt]).isEqualTo(completedAt)
          it
        }
        .count()
    }
    assertThat(cnt).isEqualTo(1)
  }

  private fun assertUserChallengeEvent(eventId: ChallengeEventId, userId: String, state: ChallengeEventState, earnings: BigDecimal) {
    val cnt = transaction(database) {
      UserChallengeEventTable
        .select {
          (UserChallengeEventTable.eventId eq eventId.value) and
              (UserChallengeEventTable.userId eq userId)
        }
        .map {
          assertThat(it[UserChallengeEventTable.state]).isEqualTo(state)
          assertThat(it[UserChallengeEventTable.earnings].setScale(2)).isEqualTo(earnings)
          it
        }
        .count()
    }
    assertThat(cnt).isEqualTo(1)
  }

  @Test
  fun `SHOULD return UserChallengeDto on getUserChallenge`() {
    val completedAt = now.minus(Duration.ofMinutes(10))
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challenge1Id = ChallengeId("TEST_11")
    insertChallenge(challenge1Id, eventId, "t1")
    val challenge2Id = ChallengeId("TEST_12")
    insertChallenge(ChallengeId("TEST_12"), eventId, "t1", goal = 100)
    insertUserChallenge(user1Id, challenge1Id, state = ChallengeState.CLAIMED, coins = 135, completedAt = completedAt)
    insertUserChallenge(user1Id, challenge2Id, state = ChallengeState.CLAIMED, coins = 140, progress = 100, completedAt = completedAt)
    val result = runBlocking { service.getUserChallenge(challenge2Id, user1Id) }
    assertThat(result).isEqualTo(
      UserChallenge(
        userId = user1Id,
        challenge = Challenge(
          id = challenge2Id,
          eventId = eventId,
          progressMax = 100,
          gameId = testGameId,
          icon = "icon",
          title = "t1",
          calculator = ChallengeProgressCalculatorType.LEVEL_ID,
          applyEarningsCut = true,
          order = 0,
          goal = 100,
          challengeType = ChallengeType.REGULAR,
        ),
        state = ChallengeState.CLAIMED,
        coins = 140,
        progress = 100,
        completedAt = completedAt,
        updatedAt = now,
        achievement = "15"
      )
    )
  }

  @Test
  fun `SHOULD return UserChallengeEventDto ON getUserChallengeEvent`() {
    val event1Id = ChallengeEventId("TEST_1")
    val someDate = Instant.parse("2025-01-31T12:13:14Z")

    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(user1Id, event1Id, state = ChallengeEventState.IN_PROGRESS, earnings = BigDecimal("0.7676544"), createdAt = someDate)
    insertUserChallengeEvent(user2Id, event1Id, state = ChallengeEventState.CLAIMED)
    val result = runBlocking { service.getUserChallengeEvent(user1Id, event1Id) }
    assertThat(result).isEqualTo(
      UserChallengeEvent(
        userId = user1Id,
        eventId = event1Id,
        state = ChallengeEventState.IN_PROGRESS,
        earnings = BigDecimal("0.767654"),
        startedAt = someDate,
        applovinNonBannerRevenue = null,
      )
    )
  }

  @Test
  fun `SHOULD claim event ON claimEvent when event is COMPLETED`() {
    //given
    val event1Id = ChallengeEventId("TEST_1")
    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(user1Id, event1Id, state = ChallengeEventState.COMPLETED)
    //when
    val result1 = runBlocking { service.claimEvent(eventId = event1Id, userId = user1Id, earnings = BigDecimal("0.745665")) }
    //then
    assertThat(result1).isEqualTo(true)
    transaction(database) {
      UserChallengeEventTable.select {
        (UserChallengeEventTable.eventId eq event1Id.value) and (UserChallengeEventTable.userId eq user1Id)
      }.forEach {
        assertThat(it[UserChallengeEventTable.state]).isEqualTo(ChallengeEventState.CLAIMED)
        assertThat(it[UserChallengeEventTable.earnings]).isEqualTo(BigDecimal("0.745665"))
      }
    }
  }

  @Test
  fun `SHOULD not claim event ON claimEvent when event is IN_PROGRESS`() {
    //given
    val event1Id = ChallengeEventId("TEST_1_")
    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(user1Id, event1Id, state = ChallengeEventState.IN_PROGRESS)
    //when
    val result1 = runBlocking { service.claimEvent(eventId = event1Id, userId = user1Id, earnings = BigDecimal("0.75")) }
    //then
    assertThat(result1).isEqualTo(false)
    val state = transaction(database) {
      UserChallengeEventTable.select {
        (UserChallengeEventTable.eventId eq event1Id.value) and (UserChallengeEventTable.userId eq user1Id)
      }.map {
        it[UserChallengeEventTable.state]
      }.first()
    }
    assertThat(state).isEqualTo(ChallengeEventState.IN_PROGRESS)
  }

  @Test
  fun `SHOULD not claim challenge ON claimChallenge WHEN challenge IN_PROGRESS`() {
    //given
    val eventId = ChallengeEventId("TEST_1")
    insertChallengeEvent(eventId)
    val challengeId = ChallengeId("TEST_11")
    insertChallenge(challengeId, eventId, "TEST_11")
    insertUserChallenge(user1Id, challengeId, state = ChallengeState.IN_PROGRESS)
    //when
    val result = runBlocking { service.claimChallenge(challengeId = challengeId, userId = user1Id, coins = 32) }
    //then
    assertThat(result).isFalse()
    transaction(database) {
      UserChallengeTable.select {
        (UserChallengeTable.challengeId eq challengeId.value) and (UserChallengeTable.userId eq user1Id)
      }.map {
        it[UserChallengeTable.state]
      }.first()
    }
  }

  @Test
  fun `SHOULD claim challenge ON claimChallenge WHEN challenge is COMPLETED`() {
    //given
    val eventId = ChallengeEventId("TEST_1_")
    insertChallengeEvent(eventId)
    val challengeId = ChallengeId("TEST_11_")
    insertChallenge(challengeId, eventId, challengeId.value)
    insertUserChallenge(user1Id, challengeId, state = ChallengeState.COMPLETED, progress = 100)
    //when
    val result = runBlocking { service.claimChallenge(challengeId = challengeId, userId = user1Id, 45) }
    //then
    assertThat(result).isTrue()
    assertUserChallenge(challengeId, user1Id, ChallengeState.CLAIMED, 100, 45, "15")
  }

  @Test
  fun `SHOULD return user challenges ON getUserChallenges`() {
    //given
    val event1Id = ChallengeEventId("TEST_1")
    insertChallengeEvent(event1Id)
    val event2Id = ChallengeEventId("TEST_2")
    insertChallengeEvent(event2Id)
    val challengeId11 = ChallengeId("TEST_11")
    insertChallenge(challengeId11, event1Id, event1Id.value)
    val challengeId12 = ChallengeId("TEST_12")
    insertChallenge(challengeId12, event1Id, challengeId12.value)
    val challengeId21 = ChallengeId("TEST_21")
    insertChallenge(challengeId21, event2Id, challengeId21.value)
    val challengeId22 = ChallengeId("TEST_22")
    insertChallenge(challengeId22, event2Id, challengeId22.value, order = 2)
    val challengeId23 = ChallengeId("TEST_23")
    insertChallenge(challengeId23, event2Id, challengeId23.value, order = 1, goal = 100)
    insertUserChallengeEvent(user1Id, event1Id)
    insertUserChallengeEvent(user1Id, event2Id)
    insertUserChallengeEvent(user2Id, event1Id)
    insertUserChallengeEvent(user2Id, event2Id)
    insertUserChallenge(user1Id, challengeId11)
    insertUserChallenge(user1Id, challengeId12)
    insertUserChallenge(user2Id, challengeId11)
    insertUserChallenge(user2Id, challengeId21)
    insertUserChallenge(user2Id, challengeId22, 45)

    val userChallenges = runBlocking { service.getUserChallenges(event2Id, user2Id) }
    val expectedChallenge = Challenge(
      id = challengeId21,
      eventId = event2Id,
      title = "TEST_21",
      progressMax = 100,
      gameId = testGameId,
      icon = "icon",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = null,
      challengeType = ChallengeType.REGULAR,
    )
    val expectedUserChallenge = UserChallenge(
      userId = user2Id,
      challenge = expectedChallenge,
      progress = 15,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      achievement = "15",
      completedAt = null,
      updatedAt = now,
    )
    assertThat(userChallenges).hasSize(3)
    assertThat(userChallenges)
      .containsExactly(
        expectedUserChallenge,
        expectedUserChallenge.copy(challenge = expectedChallenge.copy(id = challengeId22, title = "TEST_22", order = 2), progress = 45),
        expectedUserChallenge.copy(
          challenge = expectedChallenge.copy(id = challengeId23, eventId = event2Id, title = "TEST_23", order = 1, goal = 100),
          progress = 0,
          state = ChallengeState.NOT_STARTED,
          achievement = null,
          updatedAt = null,
        ),
      )
  }

  @Test
  fun `SHOULD update event ON updateChallengeEvent`() {
    //region given
    val eventId = ChallengeEventId("TEST_EVENT_ID")
    val challenge1 = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_1"),
      eventId = eventId,
      title = "title1",
      icon = "icon1",
      progressMax = 100,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )
    val challenge2 = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_2"),
      eventId = eventId,
      title = "title2",
      icon = "icon2",
      progressMax = 100,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 2,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )

    val dateFrom = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo = Instant.parse("2025-12-05T10:15:30.00Z")

    //language=json
    val cfg = """
      {
        "claimWidget": {
          "bannerColor": "red",
          "bannerEndColor": "green",
          "textColor": "white",
          "claimButtonText": "Ok",
          "headerText": "Some header",
          "progressBarSubtext": "Some sub progress"
        },
        "tutorialSteps": [
          "welcome",
          "cash_bonus"
        ],
        "challengesUpdateText": "some challenge update text"
      }
      """.trimIndent()

    val dto = ChallengeEvent(
      id = eventId,
      dateFrom = dateFrom,
      dateTo = dateTo,
      cfg = cfg,
      challenges = listOf(
        challenge1, challenge2
      ),
      enabled = true,
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    runBlocking { service.createChallengeEvent(dto) }

    val challenge1Upd = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_1"),
      eventId = eventId,
      title = "title1_upd",
      icon = "icon1_upd",
      progressMax = 150,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.SCORE_PROGRESS,
      order = 2,
      goal = 150,
      applyEarningsCut = false,
      challengeType = ChallengeType.REGULAR,
    )
    val challenge2Upd = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_2"),
      eventId = eventId,
      title = "title_upd",
      icon = "icon_upd",
      progressMax = 50,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.SCORE_PROGRESS,
      order = 1,
      goal = 50,
      applyEarningsCut = false,
      challengeType = ChallengeType.REGULAR,
    )

    val dateFromUpd = Instant.parse("2025-12-03T10:20:30.00Z")
    val dateToUpd = Instant.parse("2025-12-05T10:20:30.00Z")

    //language=json
    val cfgUpd = """
      {
        "claimWidget": {
          "bannerColor": "red",
          "bannerEndColor": "green",
          "textColor": "white",
          "claimButtonText": "Ok",
          "headerText": "Some header",
          "progressBarSubtext": "Some sub progress"
        },
        "tutorialSteps": [
          "welcome",
          "cash_bonus"
        ],
        "challengesUpdateText": "some challenge update text_upd"
      }
      """.trimIndent()

    //endregion given

    //when
    runBlocking {
      service.updateChallengeEvent(
        ChallengeEvent(
          id = eventId,
          dateFrom = dateFromUpd,
          dateTo = dateToUpd,
          cfg = cfgUpd,
          challenges = listOf(
            challenge1Upd, challenge2Upd
          ),
          enabled = false,
          eventType = ChallengeEventType.GLOBAL,
          bonusId = "bonusId",
          )
      )
    }

    //then
    assertEvent(eventId.value, dateFromUpd, dateToUpd, cfgUpd, false)
    assertChallenge(challenge1Upd)
    assertChallenge(challenge2Upd)
  }

  @Test
  fun `SHOULD create event ON createChallengeEvent`() {
    val eventId = ChallengeEventId("TEST_EVENT_ID")
    val challenge1 = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_1"),
      eventId = eventId,
      title = "title1",
      icon = "icon1",
      progressMax = 100,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 1,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )
    val challenge2 = Challenge(
      id = ChallengeId("TEST_CHALLENGE_ID_2"),
      eventId = eventId,
      title = "title2",
      icon = "icon2",
      progressMax = 100,
      gameId = testGameId,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      order = 2,
      goal = 100,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    )

    val dateFrom = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo = Instant.parse("2025-12-05T10:15:30.00Z")

    //language=json
    val cfg = """
      {
        "claimWidget": {
          "bannerColor": "red",
          "bannerEndColor": "green",
          "textColor": "white",
          "claimButtonText": "Ok",
          "headerText": "Some header",
          "progressBarSubtext": "Some sub progress"
        },
        "tutorialSteps": [
          "welcome",
          "cash_bonus"
        ],
        "challengesUpdateText": "some challenge update text"
      }
      """.trimIndent()

    runBlocking {
      service.createChallengeEvent(
        ChallengeEvent(
          id = eventId,
          dateFrom = dateFrom,
          dateTo = dateTo,
          cfg = cfg,
          challenges = listOf(
            challenge1, challenge2
          ),
          enabled = true,
          eventType = ChallengeEventType.GLOBAL,
          bonusId = "bonusId",
          )
      )
    }

    assertEvent(eventId.value, dateFrom, dateTo, cfg, true)
    assertChallenge(challenge1)
    assertChallenge(challenge2)
  }

  @Test
  fun `SHOULD return list of events ON getChallengeEventsList`() {
    prepareEvent(eventId = "TEST_1", challengeIds = setOf("TEST_1_1", "TEST_1_2"))
    prepareEvent(eventId = "TEST_2", dateTo = Instant.parse("2024-05-06T00:00:00Z"), challengeIds = setOf("TEST_2_1", "TEST_2_2"))
    prepareEvent(eventId = "TEST_3", dateTo = Instant.parse("2024-08-06T00:00:00Z"), challengeIds = setOf("TEST_3_1", "TEST_3_2"))
    prepareEvent(eventId = "TEST_4", dateTo = Instant.parse("2024-09-06T00:00:00Z"), challengeIds = setOf("TEST_4_1", "TEST_4_2"))
    prepareEvent(
      eventId = "TEST_5",
      dateTo = Instant.parse("2024-09-06T00:00:00Z"),
      dateFrom = Instant.parse("2024-09-02T00:00:00Z"),
      challengeIds = setOf("TEST_5_1", "TEST_5_2")
    )
    prepareEvent(eventId = "TEST_6", dateTo = Instant.parse("2024-09-06T00:00:00Z"), enabled = false, challengeIds = setOf("TEST_6_1", "TEST_6_2"))
    prepareEvent(eventId = "TEST_7", dateTo = Instant.parse("2024-09-06T00:00:00Z"), challengeIds = setOf("TEST_7_1", "TEST_7_2"))
    prepareEvent(eventId = "TEST_8", dateTo = Instant.parse("2024-09-06T00:00:00Z"), challengeIds = setOf("TEST_8_1", "TEST_8_2"))
    //when
    var result = runBlocking {
      service.getChallengeEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = true
      )
    }
    //then
    var filteredResult = result.filter { it.id.value.startsWith("TEST") }
    assertThat(filteredResult).hasSize(4)
    var ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly(ChallengeEventId("TEST_3"), ChallengeEventId("TEST_4"), ChallengeEventId("TEST_7"), ChallengeEventId("TEST_8"))
    var test3 = filteredResult.find { it.id.value == "TEST_3" }!!
    assertThat(test3.dateTo).isEqualTo(Instant.parse("2024-08-06T00:00:00Z"))
    assertThat(test3.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test3.challenges.size).isEqualTo(2)
    assertChallenge(challenge = test3.challenges[0], order = 1, challengeId = ChallengeId("TEST_3_1"), eventId = ChallengeEventId("TEST_3"))
    assertChallenge(challenge = test3.challenges[1], order = 2, challengeId = ChallengeId("TEST_3_2"), eventId = ChallengeEventId("TEST_3"))

    //when
    result = runBlocking {
      service.getChallengeEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = null
      )
    }
    //then
    filteredResult = result.filter { it.id.value.startsWith("TEST") }
    assertThat(filteredResult).hasSize(5)
    ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly(
      ChallengeEventId("TEST_3"),
      ChallengeEventId("TEST_4"),
      ChallengeEventId("TEST_6"),
      ChallengeEventId("TEST_7"),
      ChallengeEventId("TEST_8")
    )
    test3 = filteredResult.find { it.id.value == "TEST_3" }!!
    assertThat(test3.dateTo).isEqualTo(Instant.parse("2024-08-06T00:00:00Z"))
    assertThat(test3.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test3.challenges.size).isEqualTo(2)
    assertChallenge(challenge = test3.challenges[0], order = 1, challengeId = ChallengeId("TEST_3_1"), eventId = ChallengeEventId("TEST_3"))
    assertChallenge(challenge = test3.challenges[1], order = 2, challengeId = ChallengeId("TEST_3_2"), eventId = ChallengeEventId("TEST_3"))

    var test6 = filteredResult.find { it.id.value == "TEST_6" }!!
    assertThat(test6.challenges.size).isEqualTo(2)
    assertChallenge(challenge = test6.challenges[0], order = 1, challengeId = ChallengeId("TEST_6_1"), eventId = ChallengeEventId("TEST_6"))
    assertChallenge(challenge = test6.challenges[1], order = 2, challengeId = ChallengeId("TEST_6_2"), eventId = ChallengeEventId("TEST_6"))

    //when
    result = runBlocking {
      service.getChallengeEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = false
      )
    }
    //then
    filteredResult = result.filter { it.id.value.startsWith("TEST") }
    assertThat(filteredResult).hasSize(1)
    ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly(ChallengeEventId("TEST_6"))
    test6 = filteredResult.find { it.id.value == "TEST_6" }!!
    assertThat(test6.dateTo).isEqualTo(Instant.parse("2024-09-06T00:00:00Z"))
    assertThat(test6.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test6.challenges.size).isEqualTo(2)
    assertChallenge(challenge = test6.challenges[0], order = 1, challengeId = ChallengeId("TEST_6_1"), eventId = ChallengeEventId("TEST_6"))
    assertChallenge(challenge = test6.challenges[1], order = 2, challengeId = ChallengeId("TEST_6_2"), eventId = ChallengeEventId("TEST_6"))
  }

  private fun prepareEvent(
    eventId: String,
    dateFrom: Instant = Instant.parse("2024-01-01T00:00:00Z"),
    dateTo: Instant = Instant.parse("2024-01-03T00:00:00Z"),
    enabled: Boolean = true,
    challengeIds: Set<String>
  ) {
    transaction(database) {
      ChallengeEventTable.insert {
        it[id] = eventId
        it[ChallengeEventTable.dateFrom] = dateFrom
        it[ChallengeEventTable.dateTo] = dateTo
        it[cfg] = "{}"
        it[ChallengeEventTable.enabled] = enabled
        it[eventType] = ChallengeEventType.GLOBAL
      }
    }

    var order = 0
    challengeIds.forEach { challengeId ->
      order++
      transaction(database) {
        ChallengeTable.insert {
          it[id] = challengeId
          it[ChallengeTable.eventId] = eventId
          it[title] = "title"
          it[icon] = "icon"
          it[progressMax] = 100
          it[gameId] = testGameId
          it[calculator] = ChallengeProgressCalculatorType.LEVEL_ID
          it[ChallengeTable.order] = order
          it[applyEarningsCut] = true
          it[challengeType] = ChallengeType.REGULAR
        }
      }
    }
  }

  private fun assertEvent(
    id: String,
    dateFrom: Instant,
    dateTo: Instant,
    cfg: String,
    enabled: Boolean
  ) {
    val configList = transaction(database) {
      ChallengeEventTable
        .select { ChallengeEventTable.id eq id }
        .map {
          ChallengeEvent(
            id = ChallengeEventId(it[ChallengeEventTable.id]),
            dateFrom = it[ChallengeEventTable.dateFrom],
            dateTo = it[ChallengeEventTable.dateTo],
            cfg = it[ChallengeEventTable.cfg],
            enabled = it[ChallengeEventTable.enabled],
            challenges = emptyList(),
            eventType = ChallengeEventType.GLOBAL,
            bonusId = "bonusId",
            )
        }
    }
    assertThat(configList).hasSize(1)
    assertThat(configList[0].id.value).isEqualTo(id)
    assertThat(configList[0].dateFrom).isEqualTo(dateFrom)
    assertThat(configList[0].dateTo).isEqualTo(dateTo)
    assertThat(configList[0].enabled).isEqualTo(enabled)
    JsonAssert.assertJsonEquals(
      cfg,
      configList[0].cfg
    )
  }

  private fun assertChallenge(challenge: Challenge, order: Int, challengeId: ChallengeId, eventId: ChallengeEventId) {
    assertThat(challenge.id).isEqualTo(challengeId)
    assertThat(challenge.eventId).isEqualTo(eventId)
    assertThat(challenge.goal).isNull()
    assertThat(challenge.title).isEqualTo("title")
    assertThat(challenge.icon).isEqualTo("icon")
    assertThat(challenge.progressMax).isEqualTo(100)
    assertThat(challenge.gameId).isEqualTo(testGameId)
    assertThat(challenge.calculator).isEqualTo(ChallengeProgressCalculatorType.LEVEL_ID)
    assertThat(challenge.order).isEqualTo(order)
    assertThat(challenge.applyEarningsCut).isTrue()
  }

  private fun assertChallenge(expectedChallenge: Challenge) {
    val challengeList = transaction(database) {
      ChallengeTable
        .select { ChallengeTable.id eq expectedChallenge.id.value }
        .map {
          Challenge(
            id = ChallengeId(it[ChallengeTable.id]),
            eventId = ChallengeEventId(it[ChallengeTable.eventId]),
            title = it[ChallengeTable.title],
            icon = it[ChallengeTable.icon],
            progressMax = it[ChallengeTable.progressMax],
            gameId = it[ChallengeTable.gameId],
            calculator = it[ChallengeTable.calculator],
            order = it[ChallengeTable.order],
            goal = it[ChallengeTable.goal],
            applyEarningsCut = it[ChallengeTable.applyEarningsCut],
            challengeType = it[ChallengeTable.challengeType],
          )
        }
    }
    assertThat(challengeList).hasSize(1)
    assertThat(challengeList[0]).isEqualTo(expectedChallenge)
  }

  private fun insertUserChallenge(
    userId: String,
    challengeId: ChallengeId,
    progress: Int = 15,
    state: ChallengeState = ChallengeState.IN_PROGRESS,
    coins: Int = 0,
    achievement: String? = "15",
    completedAt: Instant? = null,
  ) {
    transaction(database) {
      UserChallengeTable.insert {
        it[UserChallengeTable.userId] = userId
        it[UserChallengeTable.challengeId] = challengeId.value
        it[UserChallengeTable.state] = state
        it[UserChallengeTable.progress] = progress
        it[UserChallengeTable.coins] = coins
        it[UserChallengeTable.achievement] = achievement
        it[UserChallengeTable.completedAt] = completedAt
      }
    }
  }

  private fun insertUserChallengeEvent(
    userId: String,
    eventId: ChallengeEventId,
    state: ChallengeEventState = ChallengeEventState.IN_PROGRESS,
    earnings: BigDecimal = BigDecimal.ZERO,
    createdAt: Instant = Instant.now(),
    applovinNonBannerRev: BigDecimal? = null,
  ) {
    transaction(database) {
      UserChallengeEventTable.insert {
        it[UserChallengeEventTable.eventId] = eventId.value
        it[UserChallengeEventTable.userId] = userId
        it[UserChallengeEventTable.state] = state
        it[UserChallengeEventTable.earnings] = earnings
        it[UserChallengeEventTable.createdAt] = createdAt
        it[UserChallengeEventTable.applovinNonBannerRev] = applovinNonBannerRev
      }
    }
  }

  private fun insertChallenge(
    id: ChallengeId,
    eventId: ChallengeEventId,
    title: String,
    order: Int = 0,
    goal: Int? = null,
  ) =
    transaction(database) {
      ChallengeTable.insert {
        it[ChallengeTable.id] = id.value
        it[ChallengeTable.eventId] = eventId.value
        it[ChallengeTable.title] = title
        it[icon] = "icon"
        it[gameId] = testGameId
        it[progressMax] = 100
        it[calculator] = ChallengeProgressCalculatorType.LEVEL_ID
        it[ChallengeTable.order] = order
        it[ChallengeTable.goal] = goal
        it[applyEarningsCut] = true
        it[challengeType] = ChallengeType.REGULAR
      }
    }

  @Test
  fun `SHOULD return ChallengeEventDto ON loadChallengeEvent`() {
    //given
    val cfg = javaClass.getResource("/user/challenge/event-additional-config.json")!!.readText()
    insertChallengeEvent(ChallengeEventId("TEST_1"))
    insertChallengeEvent(ChallengeEventId("TEST_2"), dateTo = Instant.parse("2024-05-06T00:00:00Z"))
    insertChallengeEvent(ChallengeEventId("TEST_3"), dateTo = Instant.parse("2024-08-06T00:00:00Z"), cfg = cfg)
    insertChallengeEvent(ChallengeEventId("TEST_4"), dateTo = Instant.parse("2024-09-06T00:00:00Z"))
    insertChallengeEvent(ChallengeEventId("TEST_5"), dateTo = Instant.parse("2024-09-06T00:00:00Z"), dateFrom = Instant.parse("2024-09-02T00:00:00Z"))
    insertChallengeEvent(ChallengeEventId("TEST_6"), dateTo = Instant.parse("2024-09-06T00:00:00Z"), enabled = false)
    insertChallenge(ChallengeId("TEST_33"), ChallengeEventId("TEST_3"), title = "superChallenge")
    //when
    val result = runBlocking { service.loadChallengeEvent(Instant.parse("2024-08-01T00:00:00Z")) }
    //then
    assertThat(result).isNotNull()
    assertThat(result?.id).isEqualTo(ChallengeEventId("TEST_3"))
    assertThat(result!!.dateTo).isEqualTo(Instant.parse("2024-08-06T00:00:00Z"))
    assertThat(result.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    val exampleDto = defaultJsonConverter.decodeFromString<ChallengeEventJsonConfigDto>(result.cfg)
    assertThat(exampleDto).isEqualTo(
      ChallengeEventJsonConfigDto(
        challengesUpdateText = "New challenges on New Year!",
        tutorialSteps = listOf("welcome", "cashBonus"),
        claimWidget = ClaimWidgetDto(
          bannerColor = "#FF0000",
          bannerEndColor = "#00FF00",
          textColor = "#FFFFFF",
          claimButtonText = "Claim up to 25$ CASH Reward!",
          headerText = "Golden Tickets Collected: ",
          progressBarSubtext = "Collect All Golden Tickets to Claim your Cash Bonus",
          aheadButtonText = null,
        ),
        tutorialWidget = TutorialWidgetDto(
          backgroundColor = "#FFFFFF",
          highlightStartColor = "#FFFFFA",
          highlightEndColor = "#FFFFFB",
          tcUrl = "https://example.com",
          tutorialId = "tutorialId",
        ),
        bonusTracker = BonusTrackerDto(
          bonusId = "bonusId",
          progressMax = 10,
          completeText = "You won super bonus!"
        ),
      )
    )
    assertThat(result.challenges.size).isEqualTo(1)
    assertThat(result.challenges[0]).isEqualTo(Challenge(
      id = ChallengeId("TEST_33"),
      eventId = ChallengeEventId("TEST_3"),
      title = "superChallenge",
      icon = "icon",
      gameId = testGameId,
      order = 0,
      progressMax = 100,
      goal = null,
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      challengeType = ChallengeType.REGULAR,
    ))
  }

  @Test
  fun `SHOULD update initial null applovin revenue balance ON updateIncompleteChallengeEventRevenue`() {
    val event1Id = ChallengeEventId("TEST_1")
    val someDate = Instant.parse("2025-01-31T12:13:14Z")

    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(user1Id, event1Id, state = ChallengeEventState.IN_PROGRESS, earnings = BigDecimal("0.76"), createdAt = someDate)

    runBlocking { service.updateIncompleteChallengeEventRevenue(user1Id, event1Id, BigDecimal("3.14")) }

    runBlocking { service.getUserChallengeEvent(user1Id, event1Id) }.let { actual ->
      assertThat(actual?.applovinNonBannerRevenue).isNotNull().isEqualByComparingTo(BigDecimal("3.14"))
    }
  }

  @Test
  fun `SHOULD update applovin revenue balance ON updateIncompleteChallengeEventRevenue`() {
    val event1Id = ChallengeEventId("TEST_1")
    val someDate = Instant.parse("2025-01-31T12:13:14Z")

    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(
      user1Id, event1Id,
      state = ChallengeEventState.IN_PROGRESS,
      earnings = BigDecimal("0.76"),
      createdAt = someDate,
      applovinNonBannerRev = BigDecimal("2.71"),
    )

    runBlocking { service.updateIncompleteChallengeEventRevenue(user1Id, event1Id, BigDecimal("3.14")) }

    runBlocking { service.getUserChallengeEvent(user1Id, event1Id) }.let { actual ->
      assertThat(actual?.applovinNonBannerRevenue).isNotNull().isEqualByComparingTo(BigDecimal("5.85"))
    }
  }

  @Test
  fun `SHOULD NOT update applovin revenue balance ON updateIncompleteChallengeEventRevenue WHEN status is complete`() {
    val event1Id = ChallengeEventId("TEST_1")
    val someDate = Instant.parse("2025-01-31T12:13:14Z")

    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(
      user1Id, event1Id,
      state = ChallengeEventState.COMPLETED,
      earnings = BigDecimal("0.76"),
      createdAt = someDate,
      applovinNonBannerRev = BigDecimal("2.71"),
    )

    runBlocking { service.updateIncompleteChallengeEventRevenue(user1Id, event1Id, BigDecimal("3.14")) }

    runBlocking { service.getUserChallengeEvent(user1Id, event1Id) }.let { actual ->
      assertThat(actual?.applovinNonBannerRevenue).isNotNull().isEqualByComparingTo(BigDecimal("2.71"))
    }
  }

  @Test
  fun `SHOULD NOT update applovin revenue balance ON updateIncompleteChallengeEventRevenue WHEN status is claimed`() {
    val event1Id = ChallengeEventId("TEST_1")
    val someDate = Instant.parse("2025-01-31T12:13:14Z")

    insertChallengeEvent(event1Id)
    insertUserChallengeEvent(
      user1Id, event1Id,
      state = ChallengeEventState.CLAIMED,
      earnings = BigDecimal("0.76"),
      createdAt = someDate,
      applovinNonBannerRev = BigDecimal("2.71"),
    )

    runBlocking { service.updateIncompleteChallengeEventRevenue(user1Id, event1Id, BigDecimal("3.14")) }

    runBlocking { service.getUserChallengeEvent(user1Id, event1Id) }.let { actual ->
      assertThat(actual?.applovinNonBannerRevenue).isNotNull().isEqualByComparingTo(BigDecimal("2.71"))
    }
  }

  @Test
  fun `SHOULD return false ON userHadChallengesInLastDay WHEN there was an event but user did not participate`() = runBlocking {
    val event1Id = ChallengeEventId("userHadChallengesInLastDay_1")

    insertChallengeEvent(event1Id, dateTo = now.minus(12.hours))

    val actual = service.userHadChallengesInLastDay(user1Id)

    assertFalse(actual)
  }

  @Test
  fun `SHOULD return false ON userHadChallengesInLastDay WHEN there was an event but it was 25 hours ago`() = runBlocking {
    val event1Id = ChallengeEventId("userHadChallengesInLastDay_3")

    insertChallengeEvent(event1Id, dateTo = now.minus(25.hours))
    insertUserChallengeEvent(user1Id, event1Id)

    val actual = service.userHadChallengesInLastDay(user1Id)

    assertFalse(actual)
  }

  @Test
  fun `SHOULD return true ON userHadChallengesInLastDay WHEN there was an event but it was 12 hours ago`() = runBlocking {
    val event1Id = ChallengeEventId("userHadChallengesInLastDay_2")

    insertChallengeEvent(event1Id, dateTo = now.minus(12.hours))
    insertUserChallengeEvent(user1Id, event1Id)

    val actual = service.userHadChallengesInLastDay(user1Id)

    assertTrue(actual)
  }

  private fun insertChallengeEvent(
    id: ChallengeEventId,
    dateFrom: Instant = Instant.parse("2024-01-01T00:00:00Z"),
    dateTo: Instant = Instant.parse("2024-01-03T00:00:00Z"),
    cfg: String = "{}",
    enabled: Boolean = true,
    bonusId: String? = null,
  ) = transaction(database) {
    ChallengeEventTable.insert {
      it[ChallengeEventTable.id] = id.value
      it[ChallengeEventTable.dateFrom] = dateFrom
      it[ChallengeEventTable.dateTo] = dateTo
      it[ChallengeEventTable.cfg] = cfg
      it[ChallengeEventTable.enabled] = enabled
      it[eventType] = ChallengeEventType.GLOBAL
      it[ChallengeEventTable.bonusId] = bonusId
    }
  }
}