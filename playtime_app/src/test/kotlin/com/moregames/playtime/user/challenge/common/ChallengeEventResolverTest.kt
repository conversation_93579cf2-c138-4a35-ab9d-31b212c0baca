package com.moregames.playtime.user.challenge.common

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.PackagePrivate
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.ChallengeEventConfigServiceTest
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.ChallengeEvent
import com.moregames.playtime.user.challenge.dto.ChallengeEventId
import com.moregames.playtime.user.promotion.event.manager.ftue.FtueExperimentService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyBlocking
import java.time.Instant
import java.time.temporal.ChronoUnit

class ChallengeEventResolverTest {

  @OptIn(PackagePrivate::class)
  private val challengeEventPersistenceService: ChallengeEventPersistenceService = mock()
  private val timeService: TimeService = mock()
  private val userService: UserService = mock()
  private val ftueExperimentService: FtueExperimentService = mock()
  private val abTestingService: AbTestingService = mock()


  @OptIn(PackagePrivate::class)
  val underTest = ChallengeEventResolver(
    timeService = timeService,
    challengeEventPersistenceService = challengeEventPersistenceService,
    userService = userService,
    ftueExperimentService = ftueExperimentService,
    abTestingService = abTestingService,
  )

  val now: Instant = Instant.parse("2024-11-22T12:00:01.000Z")


  @BeforeEach
  fun init() {
    timeService.mock({ now() }, now)
    userService.mock({getUser(ChallengeEventConfigServiceTest.Companion.USER_ID, true)}, ChallengeEventConfigServiceTest.Companion.userDto.copy(createdAt = now.minus(10, ChronoUnit.DAYS)))
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOOLD return null ON getChallengeEvent WHEN user is banned`() {
    val newUser = ChallengeEventConfigServiceTest.Companion.userDto.copy(isBanned = true)
    userService.mock({getUser(ChallengeEventConfigServiceTest.Companion.USER_ID, true)}, newUser)
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }

    assertThat(result).isEqualTo(null)
    verifyBlocking(challengeEventPersistenceService, times(0)) { loadChallengeEvent(any()) }
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOOLD return null ON getChallengeEvent WHEN user is deleted`() {
    val newUser = ChallengeEventConfigServiceTest.Companion.userDto.copy(isDeleted = true)
    userService.mock({getUser(ChallengeEventConfigServiceTest.Companion.USER_ID, true)}, newUser)
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }

    assertThat(result).isEqualTo(null)
    verifyBlocking(challengeEventPersistenceService, times(0)) { loadChallengeEvent(any()) }
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return null ON getChallengeEvent WHEN user is in FTUE_PERIOD and ftueExperimentService returns null`() {
    //given
    val newUser = ChallengeEventConfigServiceTest.Companion.userDto.copy(createdAt = now.minus(3, ChronoUnit.DAYS))
    userService.mock({getUser(ChallengeEventConfigServiceTest.Companion.USER_ID, true)}, newUser)
    ftueExperimentService.mock({ resolveFtueChallengeEventId(newUser)}, null)
    //when
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    //then
    assertThat(result).isEqualTo(null)
    verifyBlocking(challengeEventPersistenceService, times(0)) { loadChallengeEvent(any()) }
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return config ON getChallengeEvent WHEN user is in FTUE_PERIOD and ftueExperimentService returns id`() {
    //given
    val newUser = ChallengeEventConfigServiceTest.Companion.userDto.copy(createdAt = now.minus(3, ChronoUnit.DAYS))
    userService.mock({getUser(ChallengeEventConfigServiceTest.Companion.USER_ID, true)}, newUser)
    val challengeEventId = ChallengeEventId("FTUE")
    ftueExperimentService.mock({ resolveFtueChallengeEventId(newUser)}, challengeEventId.value)
    val ftueEvent = ChallengeEventConfigServiceTest.Companion.CHALLENGE_EVENT.copy(
      id = challengeEventId,
      dateFrom = Instant.parse("2000-01-01T00:00:00.000Z"),
      dateTo = Instant.parse("2038-01-01T00:00:00.000Z"),
    )
    challengeEventPersistenceService.mock( { loadChallengeEventById(challengeEventId)}, ftueEvent)
    //when
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    //then
    assertThat(result).isEqualTo(ftueEvent.copy(
      dateFrom = Instant.parse("2024-11-22T12:00:01.000Z"),
      dateTo =   Instant.parse("2024-11-23T12:00:01.000Z"),
    ))
    verifyBlocking(challengeEventPersistenceService, times(0)) { loadChallengeEvent(any()) }
  }


  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return null ON getChallengeEvent WHEN androidChallenges variation is default `() {
    abTestingService.mock({ assignedVariationValue(ChallengeEventConfigServiceTest.Companion.USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, DEFAULT)
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    assertThat(result).isNull()
    verifyBlocking(challengeEventPersistenceService, times(0)) { loadChallengeEvent(any()) }
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return null ON getChallengeEvent WHEN persistence service returns null`() {
    challengeEventPersistenceService.mock({ loadChallengeEvent(any()) }, null)
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    val result2 = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    assertThat(result).isNull()
    assertThat(result2).isNull()
    verifyBlocking(challengeEventPersistenceService, times(1)) { loadChallengeEvent(any()) }
  }

  @OptIn(PackagePrivate::class)
  @Test
  fun `SHOULD return ChallengeEventDto ON getChallengeEvent WHEN persistence service returns DTO`() {
    abTestingService.mock(
      { assignedVariationValue(ChallengeEventConfigServiceTest.Companion.USER_ID, ClientExperiment.ANDROID_CHALLENGES) },
      Variations.SHOW_CHALLENGES
    )
    val cfg = javaClass.getResource("/user/challenge/event-additional-config.json")!!.readText()
    val eventId = ChallengeEventId("16")
    val challengeEvent = ChallengeEvent(
      id = eventId,
      dateFrom = Instant.parse("2024-11-22T00:00:00.000Z"),
      dateTo = Instant.parse("2024-11-22T23:59:00.000Z"),
      cfg = cfg,
      enabled = true,
      challenges = emptyList(),
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    challengeEventPersistenceService.mock({ loadChallengeEvent(any()) }, challengeEvent)
    val result = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    assertThat(result).isEqualTo(challengeEvent)
    // to be sure that it is cached
    challengeEventPersistenceService.mock({ loadChallengeEvent(any()) }, null)
    val resultCached = runBlocking { underTest.getChallengeEvent(ChallengeEventConfigServiceTest.Companion.USER_ID) }
    assertThat(result).isEqualTo(resultCached)
    verifyBlocking(challengeEventPersistenceService, times(1)) { loadChallengeEvent(now.truncatedTo(ChronoUnit.MINUTES)) }
  }

}