package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.SpecialChallengePot
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import com.moregames.playtime.user.challenge.dto.UserSpecialChallengePot
import com.moregames.playtime.user.challenge.dto.claim.pot.ClaimPotResponseApiDto
import com.moregames.playtime.user.challenge.dto.config.SpecialChallengeWidgetsDto
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*

class ClaimSpecialChallengePotServiceTest {
  companion object {
    const val USER_ID = "userId"
    const val USER_POT_ID = "df3a64e0-3466-11f0-81a6-f32e6004b04b"
    val startedAt: Instant = Instant.parse("2025-01-31T12:13:14Z")
    val currencyAud: Currency = Currency.getInstance("AUD")
    val zeroExchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal.ZERO,
      userCurrency = currencyAud,
      amount = BigDecimal.ZERO,
      amountNoRounding = BigDecimal.ZERO,
    )
    val potConfig = SpecialChallengePot(
      id = 1,
      key = "POT_1",
      progressMax = 50,
      uiConfig = "{}"
    )
    val userSpecialPotSub = UserSpecialChallengePot(
      id = USER_POT_ID,
      userId = USER_ID,
      counter = 1,
      potConfig = potConfig,
      earnings = BigDecimal.ONE,
      applovinNonBannerRevenue = BigDecimal.TWO,
      state = SpecialChallengePotState.COMPLETED,
      progress = 10,
    )
    val now: Instant = Instant.parse("2025-02-15T12:13:14Z")
    val specialPotWidgetsStub = SpecialChallengeWidgetsDto(
      mainScreen = listOf(
        SpecialChallengeWidgetsDto.MenuItemDto(
          challengeType = ChallengeType.REGULAR,
          imageUrl = "https://example.com/regular-image.png",
          title = "[fr translated] Regular challenges",
        ),
        SpecialChallengeWidgetsDto.MenuItemDto(
          challengeType = ChallengeType.SPECIAL,
          imageUrl = "https://example.com/special-image.png",
          title = "[fr translated] Special challenges",
        )
      ),
      specialChallengeScreen = SpecialChallengeWidgetsDto.SpecialChallengeScreenDto(
        treasureImageUrl = "https://example.com/treasure-url.png"
      ),
      claimWidget = SpecialChallengeWidgetsDto.ClaimWidgetDto(
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png"
      )
    )
  }

  private val challengeRewardingService: SpecialChallengeRewardingService = mock()
  private val marketService: MarketService = mock()
  private val currencyExchangeService: CurrencyExchangeService = mock()
  private val challengeService: SpecialChallengePotService = mock()
  private val timeService: TimeService = mock()
  private val challengeEventConfigService: ChallengeEventConfigService = mock()
  private val userService: UserService = mock()

  private val underTest = ClaimSpecialChallengePotService(
    specialChallengeRewardingService = challengeRewardingService,
    marketService = marketService,
    currencyExchangeService = currencyExchangeService,
    specialChallengePotService = challengeService,
    challengeEventConfigService = challengeEventConfigService,
    userService = userService,
  )

  @BeforeEach
  fun before() {
    timeService.mock({ now() }, now)
    marketService.mock({ getUserCurrency(USER_ID) }, currencyAud)
    currencyExchangeService.mock({ convert(BigDecimal.ZERO, currencyAud) }, zeroExchange)
    userService.mock({ getUser(USER_ID, false) }, userDtoStub.copy(id = USER_ID, locale = Locale.FRENCH))
    challengeEventConfigService.mock({ createSpecialChallengeWidgets(any(), eq(Locale.FRENCH)) }, specialPotWidgetsStub)
  }

  @Test
  fun `SHOULD return not claimed response WHEN pot is in invalid state`() {

    challengeService.mock(
      { findUserSpecialPot(USER_ID) },
      userSpecialPotSub.copy(state = SpecialChallengePotState.IN_PROGRESS)
    )

    val result = runBlocking { underTest.claimPot(USER_ID) }

    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto("A$ 0.00", DEFAULT_EXTERNAL_MESSAGE)
    )

    verifyNoInteractions(challengeRewardingService)
  }

  @Test
  fun `SHOULD return not claimed response WHEN pot is in invalid state after claiming`() {
    val earnings = BigDecimal("1.25")
    val noEarningsPot = userSpecialPotSub.copy(earnings = earnings)
    val withEarningsEvent = userSpecialPotSub
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )

    challengeService.mock({ findUserSpecialPot(USER_ID) }, noEarningsPot, arrayOf(withEarningsEvent))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(noEarningsPot) }, BigDecimal("0.05"))
    challengeService.mock({ claimSpecialPot(USER_POT_ID, BigDecimal("0.05")) }, false)
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    challengeRewardingService.mock({ giveChallengeEventReward(noEarningsPot, reward) }, true)

    val result = runBlocking { underTest.claimPot(USER_ID) }

    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto("A$ 0.00", DEFAULT_EXTERNAL_MESSAGE)
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(noEarningsPot, reward) }
  }

  @Test
  fun `SHOULD return claimed response WHEN pot has been claimed by other request at the same time`() {
    val earnings = BigDecimal("1.25")
    val completedPot = userSpecialPotSub.copy(state = SpecialChallengePotState.COMPLETED, earnings = BigDecimal.ZERO)
    val claimedPot = userSpecialPotSub.copy(state = SpecialChallengePotState.CLAIMED, earnings = earnings)
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )
    val claimedReward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock({ findUserSpecialPot(USER_ID) }, completedPot, arrayOf(claimedPot))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(completedPot) }, BigDecimal("0.05"))
    challengeService.mock({ claimSpecialPot(USER_POT_ID, BigDecimal("0.05")) }, false)
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, claimedReward)

    challengeRewardingService.mock({ giveChallengeEventReward(completedPot, reward) }, true)

    val result = runBlocking { underTest.claimPot(USER_ID) }

    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto(
        amountString = "A$ 2.34",
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png",
      )
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(completedPot, reward) }
  }

  @Test
  fun `SHOULD return claimed response WHEN earnings reward already added`() {
    val earnings = BigDecimal("1.25")
    val completedEvent = userSpecialPotSub.copy(state = SpecialChallengePotState.COMPLETED)
    val claimedEvent = userSpecialPotSub.copy(state = SpecialChallengePotState.CLAIMED, earnings = earnings)
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )
    val claimedReward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock({ findUserSpecialPot(USER_ID) }, completedEvent, arrayOf(claimedEvent))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(completedEvent) }, BigDecimal("0.05"))
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, claimedReward)
    challengeRewardingService.mock({ giveChallengeEventReward(completedEvent, reward) }, false)

    val result = runBlocking { underTest.claimPot(USER_ID) }

    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto(
        amountString = "A$ 2.34",
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png",
        )
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(completedEvent, reward) }
    verifyBlocking(challengeRewardingService) { calculateChallengeEventRewardUsd(completedEvent) }
    verifyNoMoreInteractions(challengeRewardingService)
    verifyBlocking(challengeService, never()) { claimSpecialPot(USER_POT_ID, earnings) }
  }

  @Test
  fun `SHOULD return claimed response WHEN pot is completed`() {
    val earnings = BigDecimal("1.25")
    val userEvent = userSpecialPotSub.copy(state = SpecialChallengePotState.COMPLETED, earnings = BigDecimal.TEN)
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("1.67"),
      amountNoRounding = BigDecimal("1.6789"),
    )

    challengeService.mock({ findUserSpecialPot(USER_ID) }, userEvent)
    challengeService.mock({ claimSpecialPot(USER_POT_ID, earnings) }, true)
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(userEvent) }, earnings)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, reward)
    challengeRewardingService.mock({ giveChallengeEventReward(userEvent, reward) }, true)

    val result = runBlocking { underTest.claimPot(USER_ID) }

    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto(
        amountString = "A$ 1.67",
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png",
      )
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(userEvent, reward) }
  }

  @Test
  fun `SHOULD return claimed response WHEN pot has been already claimed`() {
    val earnings = BigDecimal("1.25")
    val exchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock(
      { findUserSpecialPot(USER_ID) },
      userSpecialPotSub.copy(
        earnings = earnings,
        state = SpecialChallengePotState.CLAIMED,
      )
    )
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, exchange)

    val result = runBlocking { underTest.claimPot(USER_ID) }
    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto(
        amountString = "A$ 2.34",
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png",
      )
    )
    verifyNoInteractions(challengeRewardingService)
  }

  @Test
  fun `SHOULD correctly handle many decimal digits ON claimEvent WHEN pot has been already claimed`() {
    val earnings = BigDecimal("1.23456789")
    val exchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.23456789"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock(
      { findUserSpecialPot(USER_ID) },
      userSpecialPotSub.copy(
        userId = USER_ID,
        earnings = earnings,
        state = SpecialChallengePotState.CLAIMED,
        applovinNonBannerRevenue = BigDecimal.ZERO
      )
    )
    currencyExchangeService.mock({ convert(BigDecimal("1.23456789"), currencyAud) }, exchange)

    val result = runBlocking { underTest.claimPot(USER_ID) }
    assertThat(result).isEqualTo(
      ClaimPotResponseApiDto(
        amountString = "A$ 2.34",
        title = "[fr translated] You hit the JackPot!",
        description = "[fr translated] Keep going! Complete more Treasure Quests for you new epicReward!",
        image = "https://example.com/claim-image.png",
      )
    )
    verifyNoInteractions(challengeRewardingService)
  }
}
