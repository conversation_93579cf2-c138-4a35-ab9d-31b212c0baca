package com.moregames.playtime.buseffects

import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.Game
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.ChallengeCompletedEffectHandler.ChallengeCompletedEffect
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.ChallengeCompletedNotification
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.KeepDoingChallengesNotification
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.SpecialChallengeCompletedNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import com.moregames.playtime.utils.FR_LOCALE
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.time.Instant
import java.util.Locale

class ChallengeCompletedEffectHandlerTest {
  private val timeService: TimeService = mock()
  private val userService: UserService = mock()
  private val userTranslationService: UserTranslationService = mock {
    onBlocking { translateOrDefault(any(), eq(FR_LOCALE), any()) } doAnswer { "FR TRANSLATED: " + (it.arguments[0] as TranslationResource).defaultValue }
  }
  private val messageBus: MessageBus = mock()
  private val gamesService: GamesService = mock()
  private val challengeService: ChallengeService = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()

  private val underTest = ChallengeCompletedEffectHandler(
    challengeService = challengeService,
    userService = userService,
    userTranslationService = userTranslationService,
    messageBus = messageBus,
    gamesService = gamesService,
    bqPublisher = bigQueryEventPublisher,
    timeService = timeService,
  )

  private val userMock: UserDto = mock()
  private val gameMock: Game = mock()

  private val userId = "user-id"
  private val eventId = ChallengeEventId("omnesque")
  private val challengeId = ChallengeId("cosmic")
  private val gameId = 8250
  private val applicationId = "app.lica.tion"
  private val effect = ChallengeCompletedEffect(
    userId = userId,
    challengeEventId = eventId,
    gameId = gameId,
    challengeId = challengeId,
    challengeType = ChallengeType.REGULAR,
  )
  private val now = Instant.now()
  private val challengeEvent = ChallengeEvent(
    id = eventId,
    dateFrom = Instant.parse("2020-01-01T00:00:00Z"),
    dateTo = Instant.parse("2020-01-01T00:00:00Z"),
    cfg = "{}",
    enabled = true,
    eventType = ChallengeEventType.GLOBAL,
    bonusId = "bonusId",
    challenges = listOf(
      Challenge(
        id = challengeId,
        eventId = eventId,
        title = "Get 100 correct questions in Trivia Madness",
        icon = "images/merge_blast_icon.jpg",
        progressMax = 100,
        gameId = 20056,
        calculator = ChallengeProgressCalculatorType.LEVEL_ID,
        order = 0,
        goal = 0,
        applyEarningsCut = true,
        challengeType = ChallengeType.REGULAR,
      )
    )
  )
  private val challenge = challengeEvent.challenges.first()
  private val userChallenge = UserChallenge(
    userId = userId,
    challenge = challenge,
    progress = 5,
    state = ChallengeState.IN_PROGRESS,
    coins = 0,
    completedAt = null,
    updatedAt = now,
  )
  private var userChallenges: List<UserChallenge> = emptyList()

  private val idToGame = mapOf(
    gameId to gameMock
  )

  @BeforeEach
  fun before() {
    userChallenges = listOf(
      userChallenge,
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("17"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("19"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("23"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("29"))),
      userChallenge.copy(challenge = challenge.copy(id = ChallengeId("31"))),
    )
    challengeEvent.copy(
      challenges = listOf(
        challenge,
        challenge.copy(id = ChallengeId("17")),
        challenge.copy(id = ChallengeId("19")),
        challenge.copy(id = ChallengeId("23")),
        challenge.copy(id = ChallengeId("29")),
        challenge.copy(id = ChallengeId("31")),
      )
    )
    timeService.mock({ now() }, now)
    challengeService.mock({ getUserChallenges(userId) }, userChallenges)
    userService.mock({ getUser(userId) }, userMock)
    whenever(userMock.locale).thenReturn(FR_LOCALE)
    whenever(userMock.id).thenReturn(userId)
    whenever(userMock.appPlatform).thenReturn(AppPlatform.ANDROID)
    gamesService.mock({ getGames() }, idToGame)
    whenever(gameMock.applicationId).thenReturn(applicationId)
  }

  @Test
  fun `SHOULD send challenge completed ON handle to BQ`() {
    runBlocking { underTest.handle(effect) }

    verifyBlocking(bigQueryEventPublisher) {
      publish(
        UserChallengeUpdatedBqDto(
          userId = userId,
          challengeId = challengeId,
          gameId = gameId,
          state = ChallengeState.COMPLETED,
          completedAt = now,
          createdAt = now,
          challengeEventId = <EMAIL>
        )
      )
    }
  }

  @Test
  fun `SHOULD send challenge completed ON handle`() {
    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus, times(1)) {
      publishAsync(
        PushNotificationEffect(
          ChallengeCompletedNotification(userId, FR_LOCALE)
        )
      )
    }
  }

  @Test
  fun `SHOULD send special challenge completed ON handle`() {
    runBlocking { underTest.handle(effect.copy(challengeType = ChallengeType.SPECIAL)) }

    verifyBlocking(messageBus, times(1)) {
      publishAsync(
        PushNotificationEffect(
          SpecialChallengeCompletedNotification(userId, FR_LOCALE)
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN completed status for 1 challenge`() {
    val completedChallenges = 1

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.COMPLETED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: First Challenge Done!",
            body = "FR TRANSLATED: Off to a strong start! ✅ Keep going to grab your $$$ cash bonus! \uD83D\uDCB8",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN completed status for 3 challenge`() {
    val completedChallenges = 3

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.COMPLETED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: Over Halfway! Nice!",
            body = "FR TRANSLATED: Challenge #3 is DONE! \uD83D\uDCAA Just two more to lock in your $$$ cash bonus! \uD83D\uDE80",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN completed status for 4 challenge`() {
    val completedChallenges = 4

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.COMPLETED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: One More to get your $$$$!",
            body = "FR TRANSLATED: You’ve crushed 4 challenges—just 1 left for your $$$$ cash bonus! \uD83D\uDCB0",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN claimed status for 1 challenge`() {
    val completedChallenges = 1

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.CLAIMED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: First Challenge Done!",
            body = "FR TRANSLATED: Off to a strong start! ✅ Keep going to grab your $$$ cash bonus! \uD83D\uDCB8",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN claimed status for 3 challenge`() {
    val completedChallenges = 3

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.CLAIMED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: Over Halfway! Nice!",
            body = "FR TRANSLATED: Challenge #3 is DONE! \uD83D\uDCAA Just two more to lock in your $$$ cash bonus! \uD83D\uDE80",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD send keep going ON handle WHEN claimed status for 4 challenge`() {
    val completedChallenges = 4

    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.CLAIMED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          KeepDoingChallengesNotification(
            userId,
            title = "FR TRANSLATED: One More to get your $$$$!",
            body = "FR TRANSLATED: You’ve crushed 4 challenges—just 1 left for your $$$$ cash bonus! \uD83D\uDCB0",
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD NOT send keep going ON handle WHEN NOT ongoing event`() {
    challengeService.mock({ getUserChallenges(userId) }, emptyList())

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(ChallengeCompletedNotification(userId, Locale.FRENCH))) }
    verifyBlocking(messageBus, times(0)) {
      publishAsync(argThat { arg -> arg::class == KeepDoingChallengesNotification::class })
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 2, 5, 6])
  fun `SHOULD NOT send keep going ON handle WHEN notifications should be skipped`(completedChallenges: Int) {
    val userChallenges = userChallenges.mapIndexed { i, v ->
      if (i < completedChallenges) v.copy(state = ChallengeState.COMPLETED)
      else v
    }

    challengeService.mock({ getUserChallenges(userId) }, userChallenges)

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(ChallengeCompletedNotification(userId, Locale.FRENCH))) }
    verifyBlocking(messageBus, times(0)) {
      publishAsync(argThat { arg -> arg::class == KeepDoingChallengesNotification::class })
    }
  }

  @Test
  fun `SHOULD send amplitude event ON handle`() {
    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus) {
      publish(
        UserChallengeCompletedEffect(
          userId = userId,
          appPlatform = AppPlatform.ANDROID,
          challengeEventId = <EMAIL>,
          applicationId = "app.lica.tion",
        )
      )
    }
  }

  @Test
  fun `SHOULD NOT send amplitude event ON handle WHEN unknown game id`() {
    gamesService.mock({ getGames() }, emptyMap())

    runBlocking { underTest.handle(effect) }

    verifyBlocking(messageBus, times(0)) {
      publishAsync(argThat { arg -> arg::class == UserChallengeCompletedEffect::class })
    }
  }
}