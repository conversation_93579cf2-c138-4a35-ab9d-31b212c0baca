package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidCashout2xOfferVariation
import com.moregames.base.abtesting.variations.CooldownCoinNotificationVariation
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.junit.TypedVariationSource
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.playtime.app.PlaytimeFeatureFlags.PUSH_NOTIFICATIONS_TRACKING_ENABLED
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.*
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.PlayFirstGameReminder
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.notifications.UserNotificationConfig
import com.moregames.playtime.notifications.android.AndroidPushNotificationService
import com.moregames.playtime.notifications.ios.IosPushNotificationService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout2xoffer.Cashout2xOfferService
import com.moregames.playtime.utils.ES_LOCALE
import com.moregames.playtime.utils.cashoutTransactionStub
import com.moregames.playtime.utils.user
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import redis.clients.jedis.params.SetParams
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit.SECONDS
import java.util.*

class PushNotificationEffectHandlerTest {
  private val testUserId: String = UUID.randomUUID().toString()
  private val now = Instant.now().truncatedTo(SECONDS)

  private val userNotificationStatusService: UserNotificationStatusService = mock {
    onBlocking { areNotificationsEnabled(testUserId) } doReturn true
  }
  private val androidPushNotificationService: AndroidPushNotificationService = mock()
  private val iosPushNotificationService: IosPushNotificationService = mock()
  private val userService: UserService = mock {
    onBlocking { getUser(eq(testUserId), any()) } doReturn userDtoStub.copy(id = testUserId, locale = ES_LOCALE)
  }
  private val featureFlagsFacade: FeatureFlagsFacade = mock {
    on { boolValue(PUSH_NOTIFICATIONS_TRACKING_ENABLED) } doReturn true
  }
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val applicationConfig: ApplicationConfig = mock {
    on { justplayMarket } doReturn "unit-test-market"
  }
  private val timeService: TimeService = mock {
    on { now() } doReturn now
  }
  private val abTestingService: AbTestingService = mock {
    onBlocking { assignedVariationValue(testUserId, ClientExperiment.COIN_NOTIFICATION_COOLDOWN) } doReturn DEFAULT
  }
  private val safeJedisClient: SafeJedisClient = mock()
  private val cashout2xOfferService: Cashout2xOfferService = mock {
    onBlocking { isOfferActive(testUserId) } doReturn false
  }

  private val underTest = PushNotificationEffectHandler(
    userNotificationStatusService = userNotificationStatusService,
    androidPushNotificationService = androidPushNotificationService,
    iosPushNotificationService = iosPushNotificationService,
    userService = userService,
    featureFlagsFacade = featureFlagsFacade,
    bigQueryEventPublisher = bigQueryEventPublisher,
    applicationConfig = applicationConfig,
    timeService = timeService,
    abTestingService = abTestingService,
    safeJedisClient = safeJedisClient,
    cashout2xOfferService = cashout2xOfferService,
  )

  @Test
  fun `SHOULD not send notification ON handlePushNotificationEffect WHEN user disabled notifications`() {
    userNotificationStatusService.mock({ areNotificationsEnabled(testUserId) }, false)
    val notification = FirstCashoutOfDayNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyNoInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD not send notification ON handlePushNotificationEffect WHEN user is deleted`() {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, isDeleted = true))
    val notification = FirstCashoutOfDayNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyNoInteractions(userNotificationStatusService)
    verifyNoInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD restrict notification sending ON handlePushNotificationEffect WHEN an iOS notification for Android user`() {
    val notification = PlayFirstGameReminder(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyNoInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD restrict notification sending ON handlePushNotificationEffect WHEN an Android notification for iOS user`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = FirstCashoutOfDayNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyNoInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD process RewardSpecialChallengesEarningsAddedNotification correctly ON handlePushNotificationEffect`() {
    val notification = RewardSpecialChallengesEarningsAddedNotification(
      testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      locale = ES_LOCALE
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendSpecialChallengeEarningsAdded(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "special-challenge-earnings-added",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process SpecialChallengeCompletedNotification correctly ON handlePushNotificationEffect`() {
    val notification = SpecialChallengeCompletedNotification(
      testUserId,
      locale = ES_LOCALE
      )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendSpecialChallengeCompleted(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "special-challenge-completed",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process SpecialChallengeClaimedNotification correctly ON handlePushNotificationEffect`() {
    val notification = SpecialChallengeClaimedNotification(
      testUserId,
      locale = ES_LOCALE
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendSpecialChallengeClaimed(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "special-challenge-claimed",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process FirstCoinsForGamePushNotification correctly ON handlePushNotificationEffect`() {
    val notification = FirstCoinsForGamePushNotification(testUserId, gameName = "Balls versus blocks")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendFirstCoinsForGameNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "after_first_coins_for_game",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD not publish BQ event ON handlePushNotificationEffect WHEN FF disabled`() {
    featureFlagsFacade.mock({ boolValue(PUSH_NOTIFICATIONS_TRACKING_ENABLED) }, false)
    val notification = FirstCoinsForGamePushNotification(testUserId, gameName = "Balls versus blocks")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendFirstCoinsForGameNotification(notification, ES_LOCALE) }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD process FirstCashoutOfDayNotification correctly ON handlePushNotificationEffect`() {
    val notification = FirstCashoutOfDayNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendOnFirstCashoutOfDayNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "after_first_cashout_of_day",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process XMinutesToCashoutPeriodEndNotification correctly ON handlePushNotificationEffect`() {
    val notification = XMinutesToCashoutPeriodEndNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendXMinutesToCashoutPeriodEndNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "30min_to_cashout",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process ReachCoinGoalNotification correctly ON handlePushNotificationEffect`() {
    val notification = ReachCoinGoalNotification(testUserId, coinGoalReached = true)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendReachCoinGoalNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "reach_coin_goal",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process MissedEarningsNotification correctly ON handlePushNotificationEffect`() {
    val notification = MissedEarningsNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendMissedEarningsNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "missed_earnings",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process InactivityReminder correctly ON handlePushNotificationEffect`() {
    val notification = InactivityReminder(
      userId = testUserId,
      userFirstName = "Bart",
      notificationConfig = UserNotificationConfig(
        id = 3,
        title = "title 3",
        message = "message 3",
        personalizedMessage = "\$_translatable_message",
        personalizedTitle = "\$_translatable_title"
      )
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendInactivityReminder(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "inactivity_reminder_3",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process CashoutFailedNotification correctly ON handlePushNotificationEffect`() {
    val notification = CashoutFailedNotification(testUserId, cashoutTransaction = cashoutTransactionStub)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendCashoutFailedNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "cashout_failed",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process WelcomeCoinsOfferAvailableNotification correctly ON handlePushNotificationEffect`() {
    val notification = WelcomeCoinsOfferAvailableNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendWelcomeCoinsOfferAvailableNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "welcome_coins_offer_available",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process GooglePlayOpenedNotification correctly ON handlePushNotificationEffect`() {
    val notification = GooglePlayOpenedNotification(testUserId, gameName = "Solitaire verse")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendGooglePlayOpenedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "google_play_opened",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process SurveyNotification correctly ON handlePushNotificationEffect`() {
    val notification = SurveyNotification(testUserId, surveyId = "oncashout")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendSurveyNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "android_survey",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process GameUnlockedNotification correctly ON handlePushNotificationEffect`() {
    val notification = GameUnlockedNotification(testUserId, gameName = "Merge Blast", widgetId = "42")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendGameUnlockedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "game_unlocked",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process RemindToPlayNotification correctly ON handlePushNotificationEffect`() {
    val notification = RemindToPlayNotification(testUserId, title = "Play", description = "PLAY!")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendRemindToPlayNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "remind_to_play",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process ContinueIncompleteCashoutNotification correctly ON handlePushNotificationEffect`() {
    val notification = ContinueIncompleteCashoutNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendContinueIncompleteCashoutNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "continue_incomplete_cashout",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process InstallGameReminder correctly ON handlePushNotificationEffect`() {
    val notification = InstallGameReminder(
      userId = testUserId,
      title = "title",
      text = "text",
      icon = "icon.ico",
      image = "background.jpg",
      onClickAction = OnClickActionApiDto.openLinkInBrowser("link"),
      buttonAction = ButtonAction.openLinkInBrowser("link"),
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendInstallGameReminder(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "install_game_reminder",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process BalanceUpdatedNotification correctly ON handlePushNotificationEffect WHEN Android`() {
    val notification = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendBalanceUpdatedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "balance_updated",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process CashoutProcessedNotification correctly ON handlePushNotificationEffect WHEN Android`() {
    val notification = CashoutProcessedNotification(testUserId, cashoutTransaction = cashoutTransactionStub)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendCashoutProcessedNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "cashout_processed",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process EarningsAddedNotification correctly ON handlePushNotificationEffect WHEN Android`() {
    val notification = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendEarningsAddedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "earnings_added",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process RatingPromptCommand correctly ON handlePushNotificationEffect WHEN Android`() {
    val notification = RatingPromptCommand(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendRatingPromptCommand(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "rating_prompt",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process GameCoinGoalReachedNotification correctly ON handlePushNotificationEffect WHEN Android`() {
    val notification = GameCoinGoalReachedNotification(testUserId, text = "Reached", title = "Title")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendGameCoinGoalReachedNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "game_coin_goal_reached",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process PlayFirstGameReminder correctly ON handlePushNotificationEffect`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = PlayFirstGameReminder(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendPlayFirstGameReminder(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "play_first_game",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process ShareYourExperienceNotification correctly ON handlePushNotificationEffect`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = ShareYourExperienceNotification(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendShareYourExperienceNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "share_your_experience",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process BalanceUpdatedNotification correctly ON handlePushNotificationEffect WHEN iOS`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendBalanceUpdatedNotification(notification, ES_LOCALE, userDtoStub.appVersion) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "balance_updated",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process CashoutProcessedNotification correctly ON handlePushNotificationEffect WHEN iOS`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = CashoutProcessedNotification(testUserId, cashoutTransaction = cashoutTransactionStub)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendCashoutProcessedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "cashout_processed",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process EarningsAddedNotification correctly ON handlePushNotificationEffect WHEN iOS`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform, locale = ES_LOCALE))
    val notification = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendEarningsAddedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "earnings_added",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process RatingPromptCommand correctly ON handlePushNotificationEffect WHEN iOS`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform))
    val notification = RatingPromptCommand(testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendRatingPromptCommand(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "rating_prompt",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD process GameCoinGoalReachedNotification correctly ON handlePushNotificationEffect WHEN iOS`(platform: AppPlatform) {
    userService.mock({ getUser(testUserId, includingDeleted = true) }, userDtoStub.copy(id = testUserId, appPlatform = platform))
    val notification = GameCoinGoalReachedNotification(testUserId, text = "Reached", title = "Title")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendGameCoinGoalReachedNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = platform,
          label = "game_coin_goal_reached",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @Test
  fun `SHOULD process UnclaimedEarningNotification on handlePushNotificationEffect WHEN Android`() {
    val notification = UnclaimedEarningNotification(testUserId, title = "title", text = "text")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendUnclaimedEarningNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "earnings_added",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process UnclaimedEarningNotification on handlePushNotificationEffect WHEN IOS`() {
    userService.mock({ getUser(testUserId, true) }, userDtoStub.copy(id = testUserId, appPlatform = IOS))
    val notification = UnclaimedEarningNotification(testUserId, title = "title", text = "text")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(iosPushNotificationService) { sendUnclaimedEarningNotification(notification) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = IOS,
          label = "earnings_added",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(iosPushNotificationService)
    verifyNoInteractions(androidPushNotificationService)
  }

  @ParameterizedTest
  @TypedVariationSource(variationBase = CooldownCoinNotificationVariation::class, keys = ["cooldown5Sec", "cooldown15Sec"])
  fun `SHOOLD skip BalanceUpdatedNotification WHEN cooldown key exists`(coolDownVariation: CooldownCoinNotificationVariation) {
    //given
    val redisKey = "COIN_NOTIFICATION_COOLDOWN_${testUserId}"
    val params = SetParams().ex(coolDownVariation.period.inWholeSeconds).nx()
    safeJedisClient.mock({
      set(
        key = eq(redisKey),
        value = eq("COOLDOWN"),
        params = argThat { it ->
          it.toString() == params.toString()
        }
      )
    }, false)
    abTestingService.mock({ assignedVariationValue(testUserId, ClientExperiment.COIN_NOTIFICATION_COOLDOWN) }, coolDownVariation)
    val notification = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyNoInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @ParameterizedTest
  @TypedVariationSource(variationBase = CooldownCoinNotificationVariation::class, keys = ["cooldown5Sec", "cooldown15Sec"])
  fun `SHOOLD send BalanceUpdatedNotification WHEN cooldown key doesn't exist`(coolDownVariation: CooldownCoinNotificationVariation) {
    //given
    val redisKey = "COIN_NOTIFICATION_COOLDOWN_${user.userId}"
    val params = SetParams().ex(coolDownVariation.period.inWholeSeconds).nx()
    safeJedisClient.mock({
      set(
        key = eq(redisKey),
        value = eq("COOLDOWN"),
        params = argThat { it ->
          it.toString() == params.toString()
        }
      )
    }, true)
    abTestingService.mock({ assignedVariationValue(user.userId, ClientExperiment.COIN_NOTIFICATION_COOLDOWN) }, coolDownVariation)
    val notification = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendBalanceUpdatedNotification(notification, ES_LOCALE) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "balance_updated",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD propagate cashout 2x status ON handlePushNotificationEffect WHEN android`() {
    val notification = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)
    cashout2xOfferService.mock({ isOfferActive(testUserId) }, true)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendBalanceUpdatedNotification(notification, ES_LOCALE, cashout2xOfferActive = true) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PushNotificationSentBqEvent(
          userId = testUserId,
          platform = ANDROID,
          label = "balance_updated",
          market = "unit-test-market",
          createdAt = now,
        )
      )
    }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process RewardEarningsAddedNotification ON handlePushNotificationEffect WHEN Android`() {
    val notification = RewardEarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      locale = ES_LOCALE,
    )

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendRewardEarningsAdded(notification) }
    verifyNoMoreInteractions(androidPushNotificationService)
    verifyNoInteractions(iosPushNotificationService)
  }

  @Test
  fun `SHOULD process Cashout2xOfferActivatedNotification ON handleAndroidPushNotification`() {
    val notification =
      Cashout2xOfferActivatedNotification(userId = testUserId, offerDuration = 42, variation = AndroidCashout2xOfferVariation.AndroidCashout2xOfferOn)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendCashout2xOfferActivatedNotification(notification) }
  }

  @Test
  fun `SHOULD process CashoutOfferBalanceUpdate ON handleAndroidPushNotification`() {
    val notification = CashoutOfferBalanceUpdate(userId = testUserId, coinsBalance = 123456, hideCoins = false)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendCashoutOfferBalanceUpdatedNotification(notification, ES_LOCALE) }
  }

  @Test
  fun `SHOULD process ChallengeCompletedNotification ON handleAndroidPushNotification`() {
    val notification = ChallengeCompletedNotification(userId = testUserId, locale = ES_LOCALE)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendChallengeCompleted(notification) }
  }


  @Test
  fun `SHOULD process KeepDoingChallengesNotification ON handleAndroidPushNotification`() {
    val notification = KeepDoingChallengesNotification(userId = testUserId, title = "keep doing title", body = "keep doing body")

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendKeepDoingChallenges(notification) }
  }

  @Test
  fun `SHOULD process DayStreakRewardReadyNotification ON handleAndroidPushNotification`() {
    val notification = DayStreakRewardReadyNotification(userId = testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { sendDayStreakRewardReadyNotification(notification) }
  }

  @Test
  fun `SHOULD process OnOfferwallNowAllowedNotification ON handleAndroidPushNotification`() {
    val notification = OnOfferwallNowAllowedNotification(userId = testUserId)

    runBlocking { underTest.handlePushNotificationEffect(PushNotificationEffect(notification)) }

    verifyBlocking(androidPushNotificationService) { (sendOfferwallNowAllowedNotification(notification)) }
  }

}