package com.moregames.playtime.games

import assertk.assertThat
import assertk.assertions.containsExactly
import assertk.assertions.isEmpty
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.administration.blacklist.BlacklistService
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.gamerank.GameRankOfferModifiers
import com.moregames.playtime.user.gamerank.UserGameRankService
import com.moregames.playtime.user.offer.AndroidGameOrderExperimentService
import com.moregames.playtime.user.offer.AndroidGameReplacementService
import com.moregames.playtime.utils.EN_LOCALE
import com.moregames.playtime.utils.androidGameOfferStub
import com.moregames.playtime.utils.userWithPlatform
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.*

@ExtendWith(MockExtension::class)
class AndroidGameServiceTest(
  private val gamePersistenceService: GamePersistenceService,
  private val abTestingService: AbTestingService,
  private val blacklistService: BlacklistService,
  private val translationService: UserTranslationService,
  private val androidGameOrderExperimentService: AndroidGameOrderExperimentService,
  private val androidGameReplacementService: AndroidGameReplacementService,
  private val userGameRankService: UserGameRankService,
) {

  private val underTest = AndroidGameService(
    gamePersistenceService = gamePersistenceService,
    abTestingService = abTestingService,
    blacklistService = blacklistService,
    translationService = translationService,
    androidGameOrderExperimentService = androidGameOrderExperimentService,
    androidGameReplacementService = androidGameReplacementService,
    userGameRankService = userGameRankService,
  )


  private companion object {
    val gameOffer1 = androidGameOfferStub.copy(id = 1, orderKey = 1)
    val gameOffer2 = androidGameOfferStub.copy(id = 2, orderKey = 2)
  }

  @BeforeEach
  fun init() {
    abTestingService.mock({ isEm2Participant(userWithPlatform.id) }, false)
    blacklistService.mock({ isGoogleAdIdBlacklisted(userWithPlatform.googleAdId!!) }, false)
    translationService.answer({ tryTranslate(any(), any(), any()) }, { it.getArgument(0) })
    androidGameOrderExperimentService.answer({ addGameToTheTop(any(), any()) }, { it.getArgument(1) })
    androidGameReplacementService.answer({ replaceGames(any(), any()) }, { it.getArgument(1) })
  }

  @Test
  fun `SHOULD load sort offers by orderKey ON loadGamesOrdered`() {
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(gameOffer1, gameOffer2))

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    assertThat(actual).containsExactly(gameOffer1, gameOffer2)
  }

  @Test
  fun `SHOULD call loadVisibleGimicaGames when user is blacklisted instead of loadVisibleGame`() {
    val hexmatch = androidGameOfferStub.copy(id = 6, orderKey = 2, applicationId = "com.gimica.hexmatch")
    val emojiclickers = androidGameOfferStub.copy(id = 7, orderKey = 3, applicationId = "com.gimica.emojiclickers")
    val sugarmatch = androidGameOfferStub.copy(id = 8, orderKey = 4, applicationId = SUGAR_RUSH_APP_ID)
    val bobblepop = androidGameOfferStub.copy(id = 9, orderKey = 8, applicationId = "com.gimica.bobblepop")
    gamePersistenceService.mock({ loadVisibleAndroidGimicaGamesOffer(EN_LOCALE.language) }, listOf(hexmatch, emojiclickers, sugarmatch, bobblepop))
    blacklistService.mock({ isGoogleAdIdBlacklisted(userWithPlatform.googleAdId!!) }, true)

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    assertThat(actual).containsExactly(hexmatch, emojiclickers, sugarmatch, bobblepop)
    verifyBlocking(gamePersistenceService, never()) { loadVisibleGames(EN_LOCALE.language, ANDROID) }
    verifyBlocking(gamePersistenceService) { loadVisibleAndroidGimicaGamesOffer(EN_LOCALE.language) }
  }

  @Test
  fun `SHOULD use additional filter for shown games WHEN user is em2 participant`() {
    val hexapuzzle = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "com.relaxingbraintraining.hexapuzzle")
    val hexmatch = androidGameOfferStub.copy(id = 6, orderKey = 2, applicationId = "com.gimica.hexmatch")
    val emojiclickers = androidGameOfferStub.copy(id = 7, orderKey = 3, applicationId = "com.gimica.emojiclickers")
    val sugarmatch = androidGameOfferStub.copy(id = 8, orderKey = 4, applicationId = SUGAR_RUSH_APP_ID)
    val bobblepop = androidGameOfferStub.copy(id = 9, orderKey = 8, applicationId = "com.gimica.bobblepop")
    val gamesListGimicaOk = listOf(hexmatch, sugarmatch, bobblepop)
    val notOkGames = listOf(hexapuzzle, emojiclickers)
    gamePersistenceService.mock({ loadVisibleGames(EN_LOCALE.language, ANDROID) }, gamesListGimicaOk + notOkGames)
    abTestingService.mock({ isEm2Participant(userWithPlatform.id) }, true)
//    notOkGames.forEach {
//      emExperimentBaseService.mock({ isApplicationIdAllowedForEm2Participants(it.applicationId) }, false)
//    }
//    gamesListGimicaOk.forEach {
//      emExperimentBaseService.mock({ isApplicationIdAllowedForEm2Participants(it.applicationId) }, true)
//    }


    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    assertThat(actual).containsExactly(hexmatch, sugarmatch, bobblepop)
    verifyBlocking(gamePersistenceService) { loadVisibleGames(EN_LOCALE.language, ANDROID) }
  }

  @Test
  fun `SHOULD load games with translated fields ON loadGamesOrdered WHEN we have translations`() {
    listOf("infoTextInstallTop", "infoTextInstallBottom")
      .map { it to "en_$it" }
      .forEach { (textParameter, translation) ->
        translationService.mock({ tryTranslate(textParameter, EN_LOCALE, "userId") }, translation)
      }
    translationService.mock({ tryTranslate("description", EN_LOCALE, "userId") }, "description") // does not have such a parameter
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(gameOffer1, gameOffer2))
    val game1en = gameOffer1.copy(
      description = "description",
      infoTextInstallTop = "en_infoTextInstallTop",
      infoTextInstallBottom = "en_infoTextInstallBottom"
    )
    val game2en = game1en.copy(id = 2, orderKey = 2)
    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    assertThat(actual).containsExactly(game1en, game2en)
  }

  @Test
  fun `SHOULD load only lat games ON loadGamesOrdered WHEN lat flag true`() {
    val game1 = androidGameOfferStub.copy(id = 1, orderKey = 2, applicationId = "game1")
    val game2 = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "game2", showForLat = false)
    val game3 = androidGameOfferStub.copy(id = 3, orderKey = 3, applicationId = "game3", showForLat = true)
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(game1, game2, game3))

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = true)
    }

    assertThat(actual).isEqualTo(listOf(game3))
  }

  @Test
  fun `SHOULD return empty list ON loadGamesOrdered WHEN lat flag true AND no appropriate games`() {
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(gameOffer1, gameOffer2))

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = true)
    }

    assertThat(actual).isEmpty()
  }

  @Test
  fun `SHOULD call androidGameOrderService to add pinMaster ON loadGameOffers`() {
    val listGames = listOf(gameOffer1, gameOffer2)
    val listGamesWithPinMaster = listGames + gameOffer1.copy(id = 3, applicationId = ApplicationId.PIN_MASTER_APP_ID)
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listGames)
    whenever(runBlocking { androidGameOrderExperimentService.addGameToTheTop(any(), eq(listGames)) }).then { listGamesWithPinMaster }

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }
    assertThat(actual).isEqualTo(listGamesWithPinMaster.sortedBy { it.orderKey })
    verifyBlocking(androidGameOrderExperimentService) { addGameToTheTop(userWithPlatform.id, listGames) }
    verifyBlocking(androidGameReplacementService) { replaceGames(userWithPlatform, listGamesWithPinMaster) }
  }

  @Test
  fun `SHOULD call androidGameOrderService to add bubbleChef ON loadGameOffers`() {
    val listGames = listOf(gameOffer1, gameOffer2)
    val listGamesWithBubbleChef = listGames + gameOffer1.copy(id = 4, applicationId = ApplicationId.BUBBLE_CHIEF_APP_ID)
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listGames)
    whenever(runBlocking { androidGameOrderExperimentService.addGameToTheTop(any(), eq(listGames)) }).then { listGamesWithBubbleChef }

    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }
    assertThat(actual).isEqualTo(listGamesWithBubbleChef.sortedBy { it.orderKey })
    verifyBlocking(androidGameOrderExperimentService) { addGameToTheTop(userWithPlatform.id, listGames) }
    verifyBlocking(androidGameReplacementService) { replaceGames(userWithPlatform, listGamesWithBubbleChef) }
  }

  @Test
  fun `SHOULD apply game rank modifiers ON loadGameOffers WHEN user is eligible for game ranking`() {
    // Given
    val game1 = androidGameOfferStub.copy(id = 1, orderKey = 1, applicationId = "com.game1")
    val game2 = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "com.game2")
    val gameWithModifiers = game1.copy(
      imageFilename = "game1_rank_2.jpg",
      infoTextInstallBottom = "rank_rules_text"
    )
    val modifiers = GameRankOfferModifiers(
      imageFilename = "game1_rank_2.jpg",
      infoTextInstallBottom = "rank_rules_text"
    )

    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(game1, game2))
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 1) }, modifiers)
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 2) }, null)

    // When
    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    // Then
    assertThat(actual).containsExactly(gameWithModifiers, game2)
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 1) }
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 2) }
  }

  @Test
  fun `SHOULD not modify games ON loadGameOffers WHEN user is not eligible for game ranking`() {
    // Given
    val game1 = androidGameOfferStub.copy(id = 1, orderKey = 1, applicationId = "com.game1")
    val game2 = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "com.game2")

    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(game1, game2))
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 1) }, null)
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 2) }, null)

    // When
    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    // Then
    assertThat(actual).containsExactly(game1, game2)
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 1) }
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 2) }
  }

  @Test
  fun `SHOULD apply game rank modifiers to specific games only ON loadGameOffers`() {
    // Given
    val game1 = androidGameOfferStub.copy(id = 1, orderKey = 1, applicationId = "com.game1")
    val game2 = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "com.game2")
    val game3 = androidGameOfferStub.copy(id = 3, orderKey = 3, applicationId = "com.game3")
    val gameWithModifiers = game2.copy(
      imageFilename = "game2_rank_3.jpg",
      infoTextInstallBottom = "rank_rules_text_game2"
    )
    val modifiers = GameRankOfferModifiers(
      imageFilename = "game2_rank_3.jpg",
      infoTextInstallBottom = "rank_rules_text_game2"
    )

    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(game1, game2, game3))
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 1) }, null)
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 2) }, modifiers)
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 3) }, null)

    // When
    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    // Then
    assertThat(actual).containsExactly(game1, gameWithModifiers, game3)
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 1) }
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 2) }
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 3) }
  }

  @Test
  fun `SHOULD apply game rank modifiers after other transformations ON loadGameOffers`() {
    // Given
    val game1 = androidGameOfferStub.copy(id = 1, orderKey = 1, applicationId = "com.game1")
    val game2 = androidGameOfferStub.copy(id = 2, orderKey = 2, applicationId = "com.game2")
    val modifiers = GameRankOfferModifiers(
      imageFilename = "game1_rank_2.jpg",
      infoTextInstallBottom = "rank_rules_text"
    )

    // Setup translations first
    val translatedGame1 = game1.copy(
      description = "description",
      infoTextInstallTop = "en_infoTextInstallTop",
      infoTextInstallBottom = "en_infoTextInstallBottom"
    )
    // Then apply rank modifiers
    val finalGame1 = translatedGame1.copy(
      imageFilename = "game1_rank_2.jpg",
      infoTextInstallBottom = "rank_rules_text"
    )

    listOf("infoTextInstallTop", "infoTextInstallBottom")
      .map { it to "en_$it" }
      .forEach { (textParameter, translation) ->
        translationService.mock({ tryTranslate(textParameter, EN_LOCALE, "userId") }, translation)
      }
    translationService.mock({ tryTranslate("description", EN_LOCALE, "userId") }, "description")

    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(game1, game2))
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 1) }, modifiers)
    userGameRankService.mock({ getOfferModifiers(userWithPlatform.id, 2) }, null)

    // When
    val actual = runBlocking {
      underTest.loadGameOffers(userWithPlatform, EN_LOCALE, loadLatGamesOnly = false)
    }

    // Then
    assertThat(actual[0]).isEqualTo(finalGame1)
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 1) }
    verifyBlocking(userGameRankService) { getOfferModifiers(userWithPlatform.id, 2) }
  }
}
