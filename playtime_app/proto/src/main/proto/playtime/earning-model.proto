syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "base/common.proto";

package com.justplayapps.service.rewarding.earnings.proto;

service EmApi {
  rpc GetInflatingCoinsMultiplier(InflatingCoinsMultiplierRequest) returns (InflatingCoinsMultiplierResponse);
  rpc GetRandomCoinsUsdAmountForEm2(GetRandomCoinsUsdAmountForEm2Request) returns (GetRandomCoinsUsdAmountForEm2Response);
  rpc GetWelcomeBonusAmount(GetWelcomeBonusAmountRequest) returns (GetWelcomeBonusAmountResponse);
  rpc SeizeSomeCoinsFromStash(SeizeSomeCoinsFromStashRequest) returns (SeizeSomeCoinsFromStashResponse);
  rpc UserEverHadEarnings(UserEverHadEarningsRequest) returns (UserEverHadEarningsResponse);
  rpc GetTotalUsdEarningsForUser(GetTotalUsdEarningsForUserRequest) returns (GetTotalUsdEarningsForUserResponse);
  rpc GetRevenueTotals(GetRevenueTotalsRequest) returns (GetRevenueTotalsResponse);
  rpc LoadUserEarningsForMetaId(LoadUserEarningsForMetaIdRequest) returns (LoadUserEarningsForMetaIdResponse);
  rpc LoadUnpaidUserCurrencyEarnings(LoadUnpaidUserCurrencyEarningsRequest) returns (LoadUnpaidUserCurrencyEarningsResponse);
  rpc NoEarningsAfter(NoEarningsAfterRequest) returns (NoEarningsAfterResponse);
  rpc GetNonCashedUserEarningsWithOfferwallAmount(GetNonCashedUserEarningsWithOfferwallAmountRequest) returns (GetNonCashedUserEarningsWithOfferwallAmountResponse);
  rpc GetLatestRevenueTime(GetLatestRevenueTimeRequest) returns (GetLatestRevenueTimeResponse);
  rpc GetUnpaidUserEarningsUSD(GetUnpaidUserEarningsUSDRequest) returns (GetUnpaidUserEarningsUSDResponse);
  rpc GetApplovinInterRevenue(GetApplovinInterRevenueRequest) returns (GetApplovinInterRevenueResponse);
  rpc GetRevenueSumForUserForTimeInterval(GetRevenueSumForUserForTimeIntervalRequest) returns (GetRevenueSumForUserForTimeIntervalResponse);
  rpc GetApplovinRevenue5minPeriodsCount(GetApplovinRevenue5minPeriodsCountRequest) returns (GetApplovinRevenue5minPeriodsCountResponse);
  rpc GetLastLowEarningsCashoutPeriodEnd(GetLastLowEarningsCashoutPeriodEndRequest) returns (GetLastLowEarningsCashoutPeriodEndResponse);
  rpc GetUserNonBannerApplovinRevenueTransactionsCountByHours(GetUserNonBannerApplovinRevenueTransactionsCountByHoursRequest) returns (GetUserNonBannerApplovinRevenueTransactionsCountByHoursResponse);
  rpc Get5MinIntervalsWithRevenueByGames(Get5MinIntervalsWithRevenueByGamesRequest) returns (Get5MinIntervalsWithRevenueByGamesResponse);
  rpc GetUserPerGameRevenue(GetUserPerGameRevenueRequest) returns (GetUserPerGameRevenueResponse);

  rpc GetUserCurrentCoinsBalance(GetUserCurrentCoinsBalanceRequest) returns (GetUserCurrentCoinsBalanceResponse);
  rpc GetUninflatedGoalCoins(GetUninflatedGoalCoinsRequest) returns (GetUninflatedGoalCoinsResponse);

  rpc GetUserRevenueLast2Days(GetUserRevenueLast2DaysRequest) returns (GetUserRevenueLast2DaysResponse);
}

message InflatingCoinsMultiplierRequest {
  string user_id = 1;
}
message InflatingCoinsMultiplierResponse {
  int32 multiplier = 1;
}
message GetRandomCoinsUsdAmountForEm2Request {
  string user_id = 1;
}
message GetRandomCoinsUsdAmountForEm2Response {
  com.justplayapps.base.DecimalValue amount = 1;
}
message GetWelcomeBonusAmountRequest {
  string user_id = 1;
  com.justplayapps.base.AppPlatformProto platform = 2;
}
message GetWelcomeBonusAmountResponse {
  int32 amount = 1;
}
message SeizeSomeCoinsFromStashRequest {
  string user_id = 1;
}
message SeizeSomeCoinsFromStashResponse {
  com.justplayapps.base.DecimalValue amount = 1;
}
message GetNonCashedOutUserCurrencyEarningsNoMoreThanThresholdRequest {
  string user_id = 1;
}
message GetNonCashedOutUserCurrencyEarningsNoMoreThanThresholdResponse {
  com.justplayapps.base.DecimalValue amount_usd = 1;
  string user_currency_code = 2;
  com.justplayapps.base.DecimalValue user_currency_amount = 3;
}
message UserEverHadEarningsRequest {
  string user_id = 1;
}
message UserEverHadEarningsResponse {
  bool has_earnings = 1;
}
message GetTotalUsdEarningsForUserRequest {
  string user_id = 1;
}
message GetTotalUsdEarningsForUserResponse {
  com.justplayapps.base.DecimalValue amount = 1;
}
message GetRevenueTotalsRequest {
  string user_id = 1;
}
message GetRevenueTotalsResponse {
  com.justplayapps.base.DecimalValue revenue = 1;
  com.justplayapps.base.DecimalValue offerwal_revenue = 2;
  com.justplayapps.base.DecimalValue day2_revenue = 3;
  com.justplayapps.base.DecimalValue day0_revenue = 4;
}
message LoadUserEarningsForMetaIdRequest {
  int32 meta_id = 1;
}
message LoadUserEarningsForMetaIdResponse {
  string user_id = 1;
  google.protobuf.Int32Value meta_user_earnings_id = 2;
  com.justplayapps.base.DecimalValue calculated_user_earnings_usd = 3;
  google.protobuf.StringValue cashout_transaction_id = 4;
}
message LoadUnpaidUserCurrencyEarningsRequest {
  string user_id = 1;
}
message LoadUnpaidUserCurrencyEarningsResponse {
  com.justplayapps.base.DecimalValue amount_usd = 1;
  string user_currency_code = 2;
  com.justplayapps.base.DecimalValue user_currency_amount = 3;
  com.justplayapps.base.DecimalValue non_boosted_amount_usd = 4;
  com.justplayapps.base.DecimalValue non_boosted_user_currency_amount = 5;
}
message NoEarningsAfterRequest {
  string user_id = 1;
  google.protobuf.Timestamp from_date = 2;
}
message NoEarningsAfterResponse {
  bool no_earnings = 1;
}
message GetNonCashedUserEarningsWithOfferwallAmountRequest {
  string user_id = 1;
}
message GetNonCashedUserEarningsWithOfferwallAmountResponse {
  com.justplayapps.base.DecimalValue total_revenue = 1;
  com.justplayapps.base.DecimalValue offerwall_revenue = 2;
}
message GetLatestRevenueTimeRequest {
  string user_id = 1;
}
message GetLatestRevenueTimeResponse {
  google.protobuf.Timestamp revenue_time = 1;
}
message GetUnpaidUserEarningsUSDRequest {
  string user_id = 1;
}
message GetUnpaidUserEarningsUSDResponse {
  com.justplayapps.base.DecimalValue earnings_usd = 1;
}
message GetApplovinInterRevenueRequest {
  string user_id = 1;
  google.protobuf.Timestamp after = 2;
}
message GetApplovinInterRevenueResponse {
  com.justplayapps.base.DecimalValue amount = 1;
}
message GetRevenueSumForUserForTimeIntervalRequest {
  string user_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
}
message GetRevenueSumForUserForTimeIntervalResponse {
  com.justplayapps.base.DecimalValue revenue = 1;
}
message GetApplovinRevenue5minPeriodsCountRequest {
  string user_id = 1;
  google.protobuf.Timestamp since = 2;
}
message GetApplovinRevenue5minPeriodsCountResponse {
  map<int32, int64> periods = 1;
}
message GetLastLowEarningsCashoutPeriodEndRequest {
  string user_id = 1;
}
message GetLastLowEarningsCashoutPeriodEndResponse {
  google.protobuf.Timestamp period_end = 1;
}
message GetUserNonBannerApplovinRevenueTransactionsCountByHoursRequest {
  string user_id = 1;
  google.protobuf.Timestamp since = 2;
}
message GetUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
  repeated RevenueByPeriod revenue_by_periods = 1;
}
message RevenueByPeriod {
  google.protobuf.Timestamp period_start = 1;
  int32 transactions_count = 2;
}
message Get5MinIntervalsWithRevenueByGamesRequest {
  string user_id = 1;
  google.protobuf.Timestamp since = 2;
}
message Get5MinIntervalsWithRevenueByGamesResponse {
  map<int32, Intervals> periods = 1;
}
message Intervals {
  repeated google.protobuf.Timestamp intervals = 1;
}
message GetUserPerGameRevenueRequest {
  string user_id = 1;
}
message UserPerGameRevenue {
  int32 gameId = 1;
  com.justplayapps.base.DecimalValue revenue = 2;
}
message GetUserPerGameRevenueResponse {
  repeated UserPerGameRevenue gamesRevenue = 1;
}
message GetUserCurrentCoinsBalanceRequest {
  string user_id = 1;
  com.justplayapps.base.AppPlatformProto platform = 2;
}
message GetUserCurrentCoinsBalanceResponse {
  int64 coins = 1;
  int32 game_coins = 2;
  int32 goal_coins = 3;
}
message GetUninflatedGoalCoinsRequest {
  string user_id = 1;
}
message GetUninflatedGoalCoinsResponse {
  int32 goal_coins = 1;
}

message GetUserRevenueLast2DaysRequest {
  string user_id = 1;
}

message GetUserRevenueLast2DaysResponse {
  com.justplayapps.base.DecimalValue revenue = 1;
}
